namespace :sidekiq do
  desc 'Test direct Sidekiq scheduling'
  task test_direct_schedule: :environment do
    jid = DirectTestScheduledJob.schedule_test
    puts "Scheduled direct test job with JID: #{jid}"
  end

  desc 'Test unique job scheduler'
  task test_unique_scheduler: :environment do
    scheduled_time = 1.minute.from_now
    result = UniqueJobScheduler.schedule(DirectTestScheduledJob, ['unique scheduler test'], scheduled_time)
    puts "Result of unique scheduler: #{result ? 'Job scheduled' : 'Job not scheduled'}"
  end

  desc 'Test TodaysMatchesUpdateJob direct scheduling'
  task test_todays_matches_direct: :environment do
    jid = TodaysMatchesUpdateJob.schedule_direct_test
    puts "Scheduled TodaysMatchesUpdateJob with JID: #{jid}"
  end

  desc 'Test TodaysMatchesUpdateJob via UniqueJobScheduler'
  task test_todays_matches_unique: :environment do
    scheduled_time = 2.minutes.from_now
    result = UniqueJobScheduler.schedule(TodaysMatchesUpdateJob, ['PL'], scheduled_time)
    puts "Result of scheduling TodaysMatchesUpdateJob via UniqueJobScheduler: #{result ? 'Job scheduled' : 'Job not scheduled'}"
  end

  desc 'Run manual daily match update for testing'
  task test_daily_update: :environment do
    puts 'Starting manual DailyMatchUpdateJob'
    DailyMatchUpdateJob.new.perform('ALL')
    puts 'Manual DailyMatchUpdateJob completed'
  end

  desc 'Test force scheduling via UniqueJobScheduler'
  task test_force_schedule: :environment do
    scheduled_time = 1.minute.from_now
    result = UniqueJobScheduler.force_schedule(TodaysMatchesUpdateJob, ['PL'], scheduled_time)
    puts "Result of force scheduling: #{result ? 'Job scheduled' : 'Job scheduling failed'}"
  end

  desc 'Clear job scheduling history in Redis'
  task clear_scheduling_history: :environment do
    pattern = 'scheduled_job:*'
    keys = RedisConnection.redis.keys(pattern)
    if keys.any?
      RedisConnection.redis.del(*keys)
      puts "Cleared #{keys.count} job scheduling records"
    else
      puts 'No job scheduling records found'
    end
  end
end
