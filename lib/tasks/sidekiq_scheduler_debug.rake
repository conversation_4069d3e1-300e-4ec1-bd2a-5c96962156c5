namespace :sidekiq do
  desc 'Debug Sidekiq scheduler status'
  task scheduler_debug: :environment do
    puts "Sidekiq version: #{Sidekiq::VERSION}"
    puts "SidekiqScheduler version: #{SidekiqScheduler::VERSION}"

    puts "\nSchedule file contents:"
    schedule_file = Rails.root.join('config', 'sidekiq_scheduler.yml')
    if File.exist?(schedule_file)
      schedule = YAML.load_file(schedule_file)
      puts schedule.inspect
    else
      puts 'Schedule file not found!'
    end

    puts "\nLoaded into Sidekiq.schedule:"
    if Sidekiq.schedule && !Sidekiq.schedule.empty?
      Sidekiq.schedule.each do |name, config|
        puts "- #{name}: #{config.inspect}"
      end
    else
      puts 'No jobs in Sidekiq.schedule!'
    end

    # Check Redis directly
    puts "\nScheduler data in Redis:"
    require 'redis'
    redis = Redis.new(url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/0'))
    keys = redis.keys('sidekiq-scheduler:*')
    if keys.any?
      keys.each do |key|
        value = redis.get(key)
        puts "#{key}: #{value}"
      end
    else
      puts 'No sidekiq-scheduler keys in Redis'
    end
  end
end
