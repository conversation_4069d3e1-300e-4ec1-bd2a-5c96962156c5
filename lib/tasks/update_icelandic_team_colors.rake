namespace :teams do # rubocop:disable Metrics/BlockLength
  desc 'Update team colors from Icelandic team colors file'
  task update_icelandic_colors: :environment do # rubocop:disable Metrics/BlockLength
    puts 'Starting to update team colors from Icelandic team colors file...'

    # Path to the Icelandic team colors file
    file_path = Rails.root.join('docs', 'icelandic_team_colors.md')

    unless File.exist?(file_path)
      abort "Error: File not found at #{file_path}"
    end

    # Read the file content
    content = File.read(file_path)

    # Parse the file to extract team data
    teams_data = []
    current_team = nil
    current_kit = nil

    content.each_line do |line|
      line = line.strip
      next if line.empty?

      if line.start_with?('Félag:')
        team_name = line.gsub('Félag:', '').strip
        current_team = { name: team_name, home: {}, away: {} }
        teams_data << current_team
      elsif line == 'Aðalbúningur'
        current_kit = :home
      elsif line == 'Varabúningur:'
        current_kit = :away
      elsif current_team && current_kit
        if line.start_with?('Peysa:')
          color = line.gsub('Peysa:', '').strip
          current_team[current_kit][:primary] = color
        elsif line.start_with?('Buxur:')
          color = line.gsub('Buxur:', '').strip
          current_team[current_kit][:secondary] = color
        elsif line.start_with?('Sokkar:')
          color = line.gsub('Sokkar:', '').strip
          current_team[current_kit][:tertiary] = color
        end
      end
    end

    # Map of Icelandic team names to database team names
    team_name_map = {
      'Breðablik' => 'Breiðablik',
      'Víkingur R.' => 'Víkingur R.'
    }

    # Map of Icelandic color names to English
    color_map = {
      'Svart' => 'Black',
      'Hvítt' => 'White',
      'Rautt' => 'Red',
      'Blátt' => 'Blue',
      'Blár' => 'Blue',
      'Gult' => 'Yellow',
      'Gulur' => 'Yellow',
      'Grænt' => 'Green'
    }

    # Process each team
    not_found_teams = []

    teams_data.each do |team_data|
      team_name = team_data[:name]
      mapped_name = team_name_map[team_name] || team_name

      team = Team.find_by(name: mapped_name)

      if team.nil?
        not_found_teams << team_name
        next
      end

      puts "Updating colors for team: #{team.name}"

      # Initialize club_colors structure if needed
      team.club_colors ||= {}
      club_colors = team.club_colors.is_a?(Hash) ? team.club_colors.with_indifferent_access : {}

      # Update home colors
      if team_data[:home].present?
        club_colors[:home] ||= {}

        # Set primary color (Peysa)
        if team_data[:home][:primary].present?
          primary_color = convert_color(team_data[:home][:primary], color_map, team)
          club_colors[:home][:primary] = primary_color
          puts "  Home primary: #{team_data[:home][:primary]} -> #{primary_color}"
        end

        # Set secondary color (Buxur)
        if team_data[:home][:secondary].present?
          secondary_color = convert_color(team_data[:home][:secondary], color_map, team)
          club_colors[:home][:secondary] = secondary_color
          puts "  Home secondary: #{team_data[:home][:secondary]} -> #{secondary_color}"
        end

        # Set tertiary color (Sokkar) - only if it doesn't match primary or secondary
        if team_data[:home][:tertiary].present?
          tertiary_color = convert_color(team_data[:home][:tertiary], color_map, team)

          if tertiary_color != club_colors[:home][:primary] && tertiary_color != club_colors[:home][:secondary]
            club_colors[:home][:tertiary] = tertiary_color
            puts "  Home tertiary: #{team_data[:home][:tertiary]} -> #{tertiary_color}"
          else
            puts '  Skipping home tertiary (matches primary or secondary)'
          end
        end
      end

      # Update away colors
      if team_data[:away].present?
        club_colors[:away] ||= {}

        # Set primary color (Peysa)
        if team_data[:away][:primary].present?
          primary_color = convert_color(team_data[:away][:primary], color_map, team)
          club_colors[:away][:primary] = primary_color
          puts "  Away primary: #{team_data[:away][:primary]} -> #{primary_color}"
        end

        # Set secondary color (Buxur)
        if team_data[:away][:secondary].present?
          secondary_color = convert_color(team_data[:away][:secondary], color_map, team)
          club_colors[:away][:secondary] = secondary_color
          puts "  Away secondary: #{team_data[:away][:secondary]} -> #{secondary_color}"
        end

        # Set tertiary color (Sokkar) - only if it doesn't match primary or secondary
        if team_data[:away][:tertiary].present?
          tertiary_color = convert_color(team_data[:away][:tertiary], color_map, team)

          if tertiary_color != club_colors[:away][:primary] && tertiary_color != club_colors[:away][:secondary]
            club_colors[:away][:tertiary] = tertiary_color
            puts "  Away tertiary: #{team_data[:away][:tertiary]} -> #{tertiary_color}"
          else
            puts '  Skipping away tertiary (matches primary or secondary)'
          end
        end
      end

      # Update the team with the new colors
      team.update(club_colors:)
      puts "  Updated team colors for #{team.name}"
    end

    # Report teams that were not found
    if not_found_teams.any?
      puts "\nThe following teams were not found in the database:"
      not_found_teams.each do |team_name|
        puts "  - #{team_name}"
      end
      puts "\nPlease check the spelling of these teams in the icelandic_team_colors.md file."
    end

    puts 'Finished updating team colors from Icelandic team colors file'
  end

  private

  def convert_color(color_name, color_map, team)
    # For complex patterns like "Svart/Rautt röndótt", extract the first color
    if color_name.include?('/')
      main_color = color_name.split('/').first.strip
      color_name = main_color
    end

    # For patterns like "Svart m rauðum röndum", extract the first part
    if color_name.include?(' m ')
      main_color = color_name.split(' m ').first.strip
      color_name = main_color
    end

    # Map Icelandic color name to English
    english_color = color_map[color_name] || color_name

    # Try to convert to hex using the team's color_name_to_hex method
    hex_value = team.color_name_to_hex(english_color)

    # If the hex value is the same as the input, it means the color wasn't recognized
    if hex_value == english_color
      # Use default mappings for common Icelandic colors
      default_colors = {
        'Svart' => '#000000',
        'Hvítt' => '#FFFFFF',
        'Rautt' => '#FF0000',
        'Blátt' => '#0000FF',
        'Blár' => '#0000FF',
        'Gult' => '#FFFF00',
        'Gulur' => '#FFFF00',
        'Grænt' => '#008000'
      }

      hex_value = default_colors[color_name] || hex_value
    end

    hex_value
  end
end
