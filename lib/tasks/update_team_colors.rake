namespace :teams do
  desc 'Update club colors for teams from FootballDataService'
  task update_club_colors: :environment do
    puts 'Starting to update club colors for teams from FootballDataService...'

    # Get all competitions that use football_data as source
    competitions = Competition.where(source: 'football_data')

    if competitions.empty?
      puts "No competitions found with source 'football_data'"
      exit
    end

    service = FootballDataService.new

    competitions.each do |competition|
      puts "Updating teams for competition: #{competition.name} (#{competition.code})"

      begin
        # Fetch teams data from the API
        response = HTTParty.get(
          "#{service.instance_variable_get(:@base_url)}/competitions/#{competition.code}/teams",
          headers: { 'X-Auth-Token' => service.instance_variable_get(:@api_key) }
        )

        data = JSON.parse(response.body)

        if data && data['teams']
          puts "Found #{data['teams'].count} teams for competition #{competition.name}"

          data['teams'].each do |team_data|
            team = Team.find_by(external_service_id: team_data['id'], source: 'football_data')

            if team
              puts "Updating club colors for team: #{team.name} - #{team_data['clubColors']}"

              # Parse the club colors string and convert to the new format
              if team_data['clubColors'].present?
                colors = team_data['clubColors'].split(' / ')
                club_colors_hash = {
                  home: {
                    primary: team.color_name_to_hex(colors[0]),
                    secondary: colors[1].present? ? team.color_name_to_hex(colors[1]) : nil,
                    tertiary: colors[2].present? ? team.color_name_to_hex(colors[2]) : nil
                  }.compact
                }

                # Log the color conversion for debugging
                puts "  Original colors: #{colors.join(' / ')}"
                puts "  Converted to: #{club_colors_hash[:home].inspect}"

                # Preserve away colors if they exist
                club_colors_hash[:away] = if team.club_colors.is_a?(Hash) && team.club_colors['away'].present?
                                            team.club_colors['away']
                                          else
                                            {}
                                          end

                team.update(club_colors: club_colors_hash)
              end
            else
              puts "Team not found with external_service_id: #{team_data['id']}"
            end
          end
        else
          puts "No teams data found for competition #{competition.name}"
        end
      rescue StandardError => e
        puts "Error updating teams for competition #{competition.name}: #{e.message}"
      end
    end

    puts 'Finished updating club colors for teams'
  end
end
