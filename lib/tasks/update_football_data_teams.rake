namespace :football_data do
  desc 'Update teams from FootballDataService API for a specific competition'
  task :update_teams, [:competition_code] => :environment do |_t, args|
    competition_code = args[:competition_code]
    
    if competition_code.blank?
      puts "Please provide a competition code. Example: rake football_data:update_teams[PL]"
      exit
    end
    
    puts "Updating teams for competition code: #{competition_code}"
    
    competition = Competition.find_by(code: competition_code)
    
    if competition.nil?
      puts "Competition not found with code: #{competition_code}"
      exit
    end
    
    begin
      service = FootballDataService.new
      service.import_teams_for_competition(competition, competition_code)
      puts "Teams updated successfully for competition: #{competition.name}"
    rescue StandardError => e
      puts "Error updating teams: #{e.message}"
    end
  end
end
