namespace :sidekiq do
  desc 'Repair Sidekiq scheduler Redis entries'
  task repair_scheduler: :environment do
    require 'sidekiq-scheduler'
    require 'rufus-scheduler'

    puts 'Checking Sidekiq scheduler status...'

    # Load schedule from YAML file
    schedule_file = Rails.root.join('config', 'sidekiq_scheduler.yml')
    if File.exist?(schedule_file)
      schedule = YAML.load_file(schedule_file)
      puts "Schedule file found with #{schedule.keys.size} jobs: #{schedule.keys.join(', ')}"
    else
      puts "ERROR: Schedule file not found at #{schedule_file}"
      exit 1
    end

    # Connect to Redis directly
    redis_url = ENV.fetch('REDIS_URL', 'redis://localhost:6379/0')
    puts "Connecting to Redis at #{redis_url}"
    redis = Redis.new(url: redis_url)

    begin
      redis_info = redis.info
      puts "Connected to Redis version #{redis_info['redis_version']}"
      puts "Redis persistence: #{redis_info['persistence']}"
    rescue StandardError => e
      puts "ERROR connecting to Redis: #{e.message}"
      exit 1
    end

    # Check if Redis is persisting data
    puts "\nTesting Redis persistence..."
    test_key = "sidekiq:persistence:test:#{Time.now.to_i}"
    redis.set(test_key, 'test')
    if redis.get(test_key) == 'test'
      puts '✓ Redis is accepting and retrieving data'
      redis.del(test_key)
    else
      puts 'ERROR: Redis failed basic persistence test'
    end

    # Manually register jobs in Redis
    puts "\nManually registering jobs in Redis..."
    schedule.each do |job_name, job_config|
      cron = job_config['cron']
      next_time = Rufus::Scheduler.parse(cron).next_time.to_f

      # Store next execution time
      key = "sidekiq-scheduler:#{job_name}:next_time"
      redis.set(key, next_time)
      puts "Set #{key} = #{next_time} (#{Time.at(next_time)})"

      # Add to next_times sorted set
      redis.zadd('sidekiq-scheduler:next_times', next_time, job_name)
      puts 'Added to sidekiq-scheduler:next_times'

      # Store the schedule
      Sidekiq.set_schedule(job_name, job_config)
      puts "Set schedule for #{job_name}"

      # Enqueue the job if it's supposed to run now
      next unless Time.now >= Time.at(next_time)

      klass = job_config['class'].constantize
      args = job_config['args'] || []

      puts "Job #{job_name} should run now, enqueueing..."
      if defined?(Sidekiq::Client) && klass.respond_to?(:perform_async)
        jid = klass.perform_async(*args)
        puts "✓ Enqueued #{job_name} (#{jid})"
      else
        puts 'WARNING: Could not enqueue job - class may not include Sidekiq::Worker'
      end
    end

    puts "\nUpdating last_run timestamp..."
    redis.set('sidekiq-scheduler:last_run', Time.now.to_f)

    puts "\nVerification:"
    scheduler_keys = redis.keys('sidekiq-scheduler:*').sort
    puts "Found #{scheduler_keys.size} sidekiq-scheduler keys in Redis:"
    scheduler_keys.each do |key|
      value = redis.get(key)
      puts "- #{key}: #{value}"
    end

    puts "\nNext times:"
    next_times = redis.zrange('sidekiq-scheduler:next_times', 0, -1, with_scores: true)
    if next_times.any?
      next_times.each do |job_name, timestamp|
        puts "- #{job_name}: #{Time.at(timestamp)}"
      end
    else
      puts 'No jobs in next_times sorted set'
    end
  end
end
