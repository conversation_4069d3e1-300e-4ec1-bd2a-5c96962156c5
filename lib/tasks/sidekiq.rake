namespace :sidekiq do
  desc 'Clear Sidekiq scheduler data in Redis'
  task clear_scheduler_data: :environment do
    Sidekiq.redis do |conn|
      # Find and delete keys related to sidekiq-scheduler
      keys = conn.keys('sidekiq-scheduler:*')

      if keys.any?
        puts "Found #{keys.size} sidekiq-scheduler keys"
        keys.each do |key|
          puts "Deleting Redis key: #{key}"
          conn.del(key)
        end
      else
        puts 'No sidekiq-scheduler keys found'
      end

      # Check for any other potential problematic keys
      job_key = 'competition_matchday_checker_job'
      if conn.exists(job_key)
        puts "Found problematic key: #{job_key}"
        conn.del(job_key)
      end

      puts 'Sidekiq scheduler data cleanup completed'
    end
  end
end
