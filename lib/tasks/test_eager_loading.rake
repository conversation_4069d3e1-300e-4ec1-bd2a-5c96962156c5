namespace :test do
  desc 'Test eager loading of all controllers to catch inheritance issues'
  task eager_loading: :environment do
    puts 'Testing eager loading of all controllers...'

    begin
      # Set eager loading to true temporarily
      original_eager_load = Rails.application.config.eager_load
      Rails.application.config.eager_load = true

      # Force eager loading of all files
      Rails.application.eager_load!

      # Get all controller classes
      controllers = ApplicationController.descendants
      puts "Successfully loaded #{controllers.count} controllers"

      # Check each controller's inheritance chain
      controllers.each do |controller_class|
        # Skip abstract controllers
        next if controller_class.respond_to?(:abstract?) && controller_class.abstract?

        puts "Verifying inheritance chain for #{controller_class.name}"

        # Walk up the inheritance chain to make sure all parent classes exist
        current_class = controller_class
        while current_class != ApplicationController
          parent_class = current_class.superclass
          raise "#{current_class.name} has a nil superclass" if parent_class.nil?

          current_class = parent_class
        end

        # Try to initialize the controller
        begin
          controller_instance = controller_class.new
          puts "  ✓ Successfully initialized #{controller_class.name}"
        rescue StandardError => e
          puts "  ✗ Failed to initialize #{controller_class.name}: #{e.message}"
          raise e
        end
      end

      puts 'All controllers loaded and verified successfully!'
    rescue StandardError => e
      puts "Error during eager loading test: #{e.class.name} - #{e.message}"
      puts e.backtrace.join("\n")
      exit 1
    ensure
      # Restore original eager loading setting
      Rails.application.config.eager_load = original_eager_load
    end
  end
end
