{"editor.defaultFormatter": "esbenp.prettier-vscode", "rufo.exe": "/home/<USER>/.rbenv/shims/rufo", "[ruby]": {"editor.defaultFormatter": "Shopify.ruby-lsp"}, "github.copilot.chat.codesearch.enabled": true, "github.copilot.chat.newWorkspaceCreation.enabled": true, "workbench.colorCustomizations": {"activityBar.activeBackground": "#e11a01", "activityBar.background": "#e11a01", "activityBar.foreground": "#e7e7e7", "activityBar.inactiveForeground": "#e7e7e799", "activityBarBadge.background": "#003f07", "activityBarBadge.foreground": "#e7e7e7", "commandCenter.border": "#e7e7e799", "sash.hoverBorder": "#e11a01", "statusBar.background": "#ae1401", "statusBar.foreground": "#e7e7e7", "statusBarItem.hoverBackground": "#e11a01", "statusBarItem.remoteBackground": "#ae1401", "statusBarItem.remoteForeground": "#e7e7e7", "titleBar.activeBackground": "#ae1401", "titleBar.activeForeground": "#e7e7e7", "titleBar.inactiveBackground": "#ae140199", "titleBar.inactiveForeground": "#e7e7e799"}, "peacock.remoteColor": "#AE1401"}