user = User.find_by(email: '<EMAIL>')

# Ensure the user is confirmed
user.update(confirmed_at: Time.current) unless user.confirmed_at

# Add mock YouTube credentials (for local testing)
user.update(
  youtube_credentials: {
    access_token: 'fake_access_token',
    refresh_token: 'fake_refresh_token',
    expires_at: 1.hour.from_now.to_i,
    channel_id: 'UC_test_channel_id',
    channel_title: 'Test Channel'
  }
)

# Enable content creator mode
user.update(is_content_creator: true)

# Verify the setup
puts "YouTube connected: #{user.youtube_connected?}"
puts "Content creator: #{user.is_content_creator}"

user2 = User.find_by(email: '<EMAIL>')
user2.update(confirmed_at: Time.current) unless user.confirmed_at

# Add mock YouTube credentials to simulate a connected account
user2.update(
  youtube_credentials: {
    access_token: 'fake_subscriber_token',
    refresh_token: 'fake_subscriber_refresh_token',
    expires_at: 1.hour.from_now.to_i,
    channel_id: 'UC_test_subscriber_channel'
  },
  youtube_channel_id: 'UC_test_subscriber_channel',
  youtube_channel_name: 'Test Subscriber Channel',
  youtube_verified_at: Time.current,
  youtube_subscriber_count: 50, # Less than minimum requirement (100)
  youtube_avatar_url: 'https://example.com/avatar.jpg',
  is_content_creator: false # Ensure this is false
)

# Verify the setup
puts "YouTube connected: #{user2.youtube_connected?}"
puts "Subscriber count: #{user2.youtube_subscriber_count}"
puts "Meets subscriber requirement: #{user2.youtube_subscriber_count >= 100}"
puts "Content creator mode: #{user2.is_content_creator}"
