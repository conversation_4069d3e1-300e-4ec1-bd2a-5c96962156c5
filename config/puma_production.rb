#!/usr/bin/env puma

# Basic configuration
directory '/var/www/bragrights-be'
environment 'production'
pidfile '/var/www/bragrights-be/tmp/pids/puma.pid'
state_path '/var/www/bragrights-be/tmp/pids/puma.state'
stdout_redirect '/var/www/bragrights-be/log/puma_access.log', '/var/www/bragrights-be/log/puma_error.log', true

# Threads configuration - Low Memory Setup
threads 1, 3

# Workers configuration - Only 1 worker for low memory usage
workers 1

# Socket for Nginx with umask for world-readable permissions
# This is key to fixing the permission issue
umask 0000
bind 'unix:///var/www/bragrights-be/tmp/sockets/puma.sock'

# Preload app for better performance
preload_app!

on_worker_boot do
  ActiveRecord::Base.establish_connection if defined?(ActiveRecord)
end

before_fork do
  ActiveRecord::Base.connection_pool.disconnect! if defined?(ActiveRecord)
end

plugin :tmp_restart
