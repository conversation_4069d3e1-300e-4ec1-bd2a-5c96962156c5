default: &default
  adapter: postgresql
  encoding: unicode
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>

development:
  <<: *default
  database: bragrights_development
  host: localhost
  port: 5434 # Use the appropriate port for your desired PostgreSQL version
  username: magnus
  password: <%= ENV['LOCAL_PASSWORD'] %>

test:
  <<: *default
  database: bragrights_test
  adapter: postgresql
  encoding: unicode
  pool: 5
  username: magnus
  password: <%= ENV['LOCAL_PASSWORD'] %>
  host: localhost
  port: 5434

production:
  <<: *default
  # Remove individual connection parameters when using DATABASE_URL
  # Only use url OR the individual components, not both
  url: <%= ENV['DATABASE_URL'] %>
  # The following lines should be commented out or removed
  # username: bragrights
  # password: <%= ENV['DB_PASSWORD'] %>
  # database: bragrights
