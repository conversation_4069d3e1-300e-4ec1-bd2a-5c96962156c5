require_relative 'boot'

require 'rails/all'

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module BragRights
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 7.0

    config.autoload_paths += %W[#{config.root}/app/services]

    # Add errors directory to autoload paths
    config.autoload_paths += %W[#{config.root}/app/errors]

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    config.time_zone = 'UTC'
    # config.eager_load_paths << Rails.root.join("extras")

    # Only loads a smaller set of middleware suitable for API only apps.
    # Middleware like session, flash, cookies can be added back manually.
    # Skip views, helpers and assets when generating a new resource.
    config.api_only = true

    config.middleware.use ActionDispatch::Cookies
    config.middleware.use ActionDispatch::Session::CookieStore

    # Hack for allowing SVG files. While this hack is here, we should **not**
    # allow arbitrary SVG uploads. https://github.com/rails/rails/issues/34665

    ActiveStorage::Engine.config
                         .active_storage
                         .content_types_to_serve_as_binary
                         .delete('image/svg+xml')

    config.active_job.queue_adapter = :sidekiq

    # Configure Active Record Encryption
    config.active_record.encryption.primary_key = ENV.fetch('ENCRYPTION_PRIMARY_KEY',
                                                            'brag-rights-primary-key-for-dev-only')
    config.active_record.encryption.deterministic_key = ENV.fetch('ENCRYPTION_DETERMINISTIC_KEY',
                                                                  'brag-rights-deterministic-key-for-dev-only')
    config.active_record.encryption.key_derivation_salt = ENV.fetch('ENCRYPTION_KEY_DERIVATION_SALT',
                                                                    'brag-rights-key-derivation-salt-for-dev-only')
  end
end
