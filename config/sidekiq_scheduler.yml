---
competition_matchday_checker_job:
  cron: "0 6 * * *"
  class: DailyMatchUpdateJob
  queue: default
  description: "Daily job to update all matches and schedule update for today's matches"
  args:
    - "ALL"

check_season_completion_job:
  cron: "0 7 * * *"
  class: CheckSeasonCompletionJob
  queue: default
  description: "Daily job to check if any seasons are completed and mark them as such"

subscription_verification_active_users:
  cron: "0 0 * * 0" # Weekly on Sunday at midnight
  class: SubscriptionVerificationJob
  queue: youtube
  args:
    - "active"
  description: "Weekly job to verify YouTube subscriptions for active users"

subscription_verification_semi_active_users:
  cron: "0 0 * * 3" # Weekly on Wednesday at midnight
  class: SubscriptionVerificationJob
  queue: youtube
  args:
    - "semi_active"
  description: "Weekly job to verify YouTube subscriptions for semi-active users"

subscription_verification_inactive_users:
  cron: "0 0 1 * *" # Monthly on the 1st at midnight
  class: SubscriptionVerificationJob
  queue: youtube
  args:
    - "inactive"
  description: "Monthly job to verify YouTube subscriptions for inactive users"
