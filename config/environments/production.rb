require 'active_support/core_ext/integer/time'

Rails.application.configure do
  config.cache_classes = true

  host = ENV['API_HOST'] || 'api.bragrights.football'

  config.eager_load = true

  config.consider_all_requests_local = false

  config.public_file_server.enabled = ENV['RAILS_SERVE_STATIC_FILES'].present?

  config.active_storage.service = :cloudinary
  config.log_level = :info

  config.log_tags = [:request_id]

  config.action_mailer.perform_caching = false
  config.action_mailer.default_url_options = { host: }
  config.action_mailer.default_url_options = { host: 'api.bragrights.football' }

  # Configure email delivery with fallback options
  config.action_mailer.delivery_method = :mailersend_with_fallback
  config.action_mailer.perform_deliveries = true
  config.action_mailer.raise_delivery_errors = true

  # SMTP settings for the fallback mechanism
  config.action_mailer.smtp_settings = {
    address: 'mail.privateemail.com',
    port: 587,
    domain: 'bragrights.football',
    user_name: '<EMAIL>',
    password: ENV['NAMECHEAP_PASSWORD'],
    authentication: 'plain',
    enable_starttls_auto: true,
    open_timeout: 5,
    read_timeout: 5
  }

  # Add last-resort delivery method settings for the edge case when both MailerSend and SMTP fail
  # This ensures emails don't block the application flow
  config.action_mailer.file_settings = {
    location: Rails.root.join('tmp/mails')
  }

  config.i18n.fallbacks = true

  config.active_support.report_deprecations = false

  config.log_formatter = ::Logger::Formatter.new

  # Add Redis cache store for production
  config.cache_store = :redis_cache_store, {
    url: ENV['REDIS_URL'],
    expires_in: 1.day,
    namespace: 'brag_rights_cache',
    reconnect_attempts: 1,
    error_handler: lambda { |_method:, _returning:, exception:|
      Rails.logger.error("Redis cache error: #{exception.message}")
    }
  }

  # Use Sidekiq for ActiveJob
  config.active_job.queue_adapter = :sidekiq

  # TODO: Remove this once we have a proper logging solution
  config.log_level = :info # More detailed logging

  Rails.application.routes.default_url_options[:host] = host

  if ENV['RAILS_LOG_TO_STDOUT'].present?
    logger           = ActiveSupport::Logger.new($stdout)
    logger.formatter = config.log_formatter
    config.logger    = ActiveSupport::TaggedLogging.new(logger)
  end

  config.active_record.dump_schema_after_migration = false
end
