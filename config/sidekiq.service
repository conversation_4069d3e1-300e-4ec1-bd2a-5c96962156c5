[Unit]
Description=sidekiq
After=syslog.target network.target redis-server.service

[Service]
Type=simple
WorkingDirectory=/var/www/bragrights-be
# Source rbenv and run Sidekiq
ExecStart=/bin/bash -lc 'eval "$(rbenv init -)"; cd /var/www/bragrights-be && bundle exec sidekiq -e production -C config/sidekiq.yml'
User=deploy
Group=deploy
UMask=0002
RestartSec=1
Restart=always
StandardOutput=append:/var/log/sidekiq.log
StandardError=append:/var/log/sidekiq.error.log

# Greatly reduce Ruby memory fragmentation and heap usage
Environment=MALLOC_ARENA_MAX=2
# Set proper environment for application
Environment=RAILS_ENV=production
Environment=PATH=/home/<USER>/.rbenv/shims:/home/<USER>/.rbenv/bin:/usr/local/bin:/usr/bin:/bin
Environment=RBENV_ROOT=/home/<USER>/.rbenv

[Install]
WantedBy=multi-user.target
