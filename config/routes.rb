# frozen_string_literal: true

require 'sidekiq/web'
require 'sidekiq-scheduler/web' # If you're using sidekiq-scheduler

Rails.application.routes.draw do # rubocop:disable Metrics/BlockLength
  mount ActiveStorage::Engine => '/rails/active_storage'
  devise_for :users, controllers: { tokens: 'tokens' }

  # Wrap the tokens/show route in a devise_scope block
  devise_scope :user do
    get 'tokens/show', to: 'tokens#show'
    get 'confirmation', to: 'tokens#confirm'
    post 'resend_confirmation', to: 'tokens#resend_confirmation'
    post 'forgot_password', to: 'tokens#forgot_password'
    put 'reset_password', to: 'tokens#reset_password'
  end

  # Configure Sidekiq Web UI
  Sidekiq::Web.use Rack::Auth::Basic do |username, password|
    ActiveSupport::SecurityUtils.secure_compare(::Digest::SHA256.hexdigest(username),
                                                ::Digest::SHA256.hexdigest(ENV.fetch('SIDEKIQ_USERNAME', 'admin'))) &
      ActiveSupport::SecurityUtils.secure_compare(::Digest::SHA256.hexdigest(password),
                                                  ::Digest::SHA256.hexdigest(ENV.fetch('SIDEKIQ_PASSWORD', 'password')))
  end

  mount Sidekiq::Web => '/sidekiq'
  match '*path', to: 'application#not_found', via: :propfind

  namespace :api do # rubocop:disable Metrics/BlockLength
    get 'health', to: 'health#check'

    namespace :v1 do # rubocop:disable Metrics/BlockLength
      get 'health', to: 'health#check'

      resources :competitions, only: %i[index show] do
        post 'get_logos', on: :collection
        get 'favorite_team', on: :member
        get 'teams', on: :member
        get 'standings', on: :member
      end

      resources :teams, only: %i[index show create] do
        member do
          get 'standings'
        end
      end
      resources :matches, only: %i[index show]
      resources :users, only: %i[index show update] do
        collection do
          get 'me', to: 'users#me'
        end
        resource :statistics, only: [:show], controller: 'user_statistics'
      end

      resources :round_predictions, only: %i[index show create update]

      resources :join_requests, controller: :league_join_requests, only: [] do
        get :user_requests, on: :collection
      end

      resources :seasons, only: [] do
        member do
          get 'matchday_options'
          post 'archive_leagues'
          post 'transition_leagues'
        end
      end

      resources :leagues, only: %i[index show create update] do
        member do
          post 'archive'
          post 'unarchive'
          get 'standings'
        end
        resources :memberships, controller: :league_memberships, only: %i[destroy create]
        resources :table, controller: :league_table, only: %i[destroy create index]
        resources :join_requests, controller: :league_join_requests, only: %i[index create update]
      end

      resources :predictable_matches, only: [:index] do
        collection do
          get 'refresh'
        end
      end

      # Favorite Teams Routes
      resources :favorite_teams, only: %i[index create destroy]

      resources :match_predictions, only: %i[create] do
        collection do
          post 'bulk_create_or_update'
        end
      end

      # YouTube Integration Routes
      get 'auth/youtube/callback', to: 'youtube_auth#callback'

      # New YouTube auth endpoints for login/signup from FE
      post 'auth/youtube/login', to: 'youtube_auth#login'
      post 'auth/youtube/signup', to: 'youtube_auth#signup'

      resources :youtube_auth, only: [] do
        collection do
          post 'connect'
          post 'disconnect'
          get 'status'
          post 'verify_subscription'
        end
      end

      resources :youtube, only: [] do
        collection do
          get 'leagues'
          get 'subscription_status'
          post 'update_creator_status'
          get 'check_subscriber_league_eligibility'
        end
      end

      namespace :admin do # rubocop:disable Metrics/BlockLength
        resources :competitions, only: %i[index show update] do
          collection do
            post 'initialize_or_update'
          end
          member do
            post 'update_teams'
            post 'update_matches'
            post 'set_winner'
            post 'create_new_season'
            get 'new_season_requirements'
            get 'current_season_teams'
          end
        end

        resources :areas, only: %i[index show update]
        resources :teams, only: %i[index show update]
        resources :seasons, only: %i[index show update]

        # YouTube Admin Routes
        resources :youtube, only: [] do
          collection do
            get 'quota_usage'
          end
        end

        namespace :ksi do
          get 'team_matches/:team_id', to: 'ksi#team_matches'
          get 'tournament_standings/:tournament_id', to: 'ksi#tournament_standings'
          get 'tournament_matches/:tournament_id', to: 'ksi#tournament_matches'
          get 'top_scorers/:tournament_id', to: 'ksi#top_scorers'
          get 'groups', to: 'ksi#groups'
          get 'yellow_cards/:tournament_id', to: 'ksi#yellow_cards'
          get 'red_cards/:tournament_id', to: 'ksi#red_cards'
          get 'match_events/:match_id', to: 'ksi#match_events'
          get 'match_players/:match_id', to: 'ksi#match_players'
          get 'match_referees/:match_id', to: 'ksi#match_referees'
          get 'match_changes', to: 'ksi#match_changes'
          get 'score_changes', to: 'ksi#score_changes'
          get 'matches', to: 'ksi#matches'
        end
      end
    end
  end
end
