class Rack::Attack
  # Throttle confirmation attempts for a given email parameter
  throttle('confirmation_by_ip', limit: 5, period: 3600) do |req|
    req.ip if req.path == '/confirmation' && req.get?
  end

  # Throttle password reset requests
  throttle('forgot_password_by_ip', limit: 3, period: 3600) do |req|
    req.ip if req.path == '/forgot_password' && req.post?
  end

  # Throttle sign in attempts
  throttle('sign_in_by_ip', limit: 10, period: 3600) do |req|
    req.ip if req.path == '/users/sign_in' && req.post?
  end

  # Throttle content creator mode updates
  throttle('creator_mode_updates/ip', limit: 10, period: 1.hour) do |req|
    req.ip if req.path == '/api/v1/youtube/update_creator_status' && req.post?
  end

  throttle('creator_mode_updates/user', limit: 5, period: 1.hour) do |req|
    if req.path == '/api/v1/youtube/update_creator_status' && req.post?
      # Assuming you have access to the user id through your auth system
      req.env['warden'].user&.id
    end
  end
end
