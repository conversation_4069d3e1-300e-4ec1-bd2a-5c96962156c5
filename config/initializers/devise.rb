require 'devise/api/responses/token_response_decorator'

Devise.setup do |config|
  # ==> Mailer Configuration
  # Configure the e-mail address which will be shown in Devise::Mailer
  config.mailer_sender = '<EMAIL>'

  # Configure the class responsible for sending e-mails.
  config.mailer = 'CustomDeviseMailer'

  require 'devise/orm/active_record'

  config.case_insensitive_keys = [:email]

  config.strip_whitespace_keys = [:email]

  config.skip_session_storage = [:http_auth]

  config.stretches = Rails.env.test? ? 1 : 12

  config.reconfirmable = true
  config.confirm_within = 2.days
  config.allow_unconfirmed_access_for = 0.days

  config.expire_all_remember_me_on_sign_out = true
  config.password_length = 6..128

  config.email_regexp = /\A[^@\s]+@[^@\s]+\z/
  config.reset_password_within = 6.hours
  config.sign_out_via = :delete
  config.warden do |manager|
    manager.scope_defaults :user, store: false
  end

  config.responder.error_status = :unprocessable_entity
  config.responder.redirect_status = :see_other

  config.api.configure do |api|
    api.after_successful_sign_in = ->(_resource_owner, _token, _request) {}
    # Remove the unsupported configuration options
  end
end

Devise::Api::Responses::TokenResponse.prepend Devise::Api::Responses::TokenResponseDecorator
