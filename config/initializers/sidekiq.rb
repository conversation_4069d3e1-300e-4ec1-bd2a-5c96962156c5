require 'sidekiq'
require 'sidekiq-scheduler'
require 'rufus-scheduler'

Sidekiq.configure_server do |config|
  config.redis = {
    url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/0'),
    id: nil
  }

  # Set up scheduler
  schedule_file = Rails.root.join('config', 'sidekiq_scheduler.yml')

  if File.exist?(schedule_file)
    begin
      schedule = YAML.load_file(schedule_file)

      if schedule.is_a?(Hash)
        # Set global schedule first
        Sidekiq.schedule = schedule

        # Set scheduler configuration
        Sidekiq::Scheduler.enabled = true
        Sidekiq::Scheduler.dynamic = true

        config.on(:startup) do
          # Set scheduler options correctly for this version
          Sidekiq::Scheduler.rufus_scheduler_options = {
            lockable: true
            # If you need trigger_lock functionality, use a mutex instead:
            # trigger_lock: Mutex.new
          }

          # Force reload the schedule
          Sidekiq::Scheduler.reload_schedule!

          # Manually ensure next run times are set in Redis
          redis = Redis.new(url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/0'))

          schedule.each do |job_name, job_config|
            next unless job_config['cron']

            begin
              cron = job_config['cron']
              next_time = Rufus::Scheduler.parse(cron).next_time

              # Store next execution time in Redis directly to ensure it's set
              redis.set("sidekiq-scheduler:#{job_name}:next_time", next_time.to_f)
              redis.zadd('sidekiq-scheduler:next_times', next_time.to_f, job_name)

              Rails.logger.info "Job '#{job_name}' with cron '#{cron}' scheduled for: #{next_time}"
            rescue StandardError => e
              Rails.logger.error "Error setting next time for #{job_name}: #{e.message}"
            end
          end

          # Update last run time
          redis.set('sidekiq-scheduler:last_run', Time.now.to_f)
        end

        Rails.logger.info "Loaded scheduler with jobs: #{schedule.keys.join(', ')}"
      else
        Rails.logger.error "Sidekiq schedule must be a hash, got: #{schedule.class}"
      end
    rescue StandardError => e
      Rails.logger.error "Error loading Sidekiq schedule: #{e.message}\n#{e.backtrace.join("\n")}"
    end
  else
    Rails.logger.error "Schedule file not found at: #{schedule_file}"
  end
end

Sidekiq.configure_client do |config|
  config.redis = {
    url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/0'),
    id: nil
  }
end
