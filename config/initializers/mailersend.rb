# Define the custom delivery method class for MailerSend API integration
# This handles the conversion of ActionMailer mail objects to MailerSend API format
class MailerSendDeliveryMethod
  attr_accessor :settings

  # Initialize with settings from config
  # @param settings [Hash] Configuration settings including :api_key
  def initialize(settings)
    @settings = settings
  end

  # Verifies email address validity using MailerSend's Email Verification API
  # @param email [String] The email address to verify
  # @return [Hash] The verification result with status and details
  def verify_email(email)
    return { valid: true } if Rails.env.test? # Skip in test environment

    api_key = @settings[:api_key]
    return { valid: false, reason: 'API key missing' } unless api_key.present?

    # Set up the request to MailerSend's Email Verification API
    uri = URI('https://api.mailersend.com/v1/email-verification/verify')
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    request = Net::HTTP::Post.new(uri.path)
    request['Content-Type'] = 'application/json'
    request['Authorization'] = "Bearer #{api_key}"
    request.body = { email: }.to_json

    begin
      response = http.request(request)

      if response.code.to_i == 200
        result = JSON.parse(response.body)

        # Check verification result
        if result['data'] && result['data']['result']
          verification_data = result['data']['result']

          # Log verification results
          Rails.logger.info("Email verification for #{email}: #{verification_data.inspect}")

          # Return simplified verification status
          {
            valid: verification_data['is_deliverable'] && !verification_data['is_disposable'],
            reason: verification_reason(verification_data),
            full_result: verification_data
          }
        else
          { valid: false, reason: 'Invalid API response format', response: result }
        end
      else
        error_message = "Email verification API returned error status: #{response.code}"
        Rails.logger.error(error_message)
        { valid: false, reason: error_message }
      end
    rescue StandardError => e
      Rails.logger.error("Email verification failed: #{e.message}")
      { valid: false, reason: "Verification error: #{e.message}" }
    end
  end

  # Helper method to generate a human-readable reason for email validation status
  # @param verification_data [Hash] The verification result data from MailerSend
  # @return [String] A human-readable reason
  def verification_reason(verification_data)
    if verification_data['is_disposable']
      'Disposable email address'
    elsif !verification_data['is_deliverable']
      reason = []
      reason << 'Invalid syntax' unless verification_data['syntax']['is_valid']
      reason << 'Invalid MX records' unless verification_data['mx']['is_valid']
      reason << 'SMTP check failed' unless verification_data['smtp']['is_valid']
      reason.empty? ? 'Email not deliverable' : reason.join(', ')
    elsif verification_data['is_role']
      'Role-based email (e.g., info@, support@)'
    else
      'Valid email'
    end
  end

  # Method to check emails before delivery
  # @param mail [Mail::Message] The mail object to be delivered
  # @return [Boolean] True if all recipient emails are valid
  def verify_recipient_emails(mail)
    # Skip verification in development/test for faster testing
    return true if Rails.env.development? || Rails.env.test?

    # Check each recipient
    invalid_emails = []

    mail.to&.each do |email|
      result = verify_email(email)
      invalid_emails << { email:, reason: result[:reason] } unless result[:valid]
    end

    if invalid_emails.any?
      Rails.logger.warn("Invalid recipient emails detected: #{invalid_emails.inspect}")
      # You can store these in the database for review or notify admins
      store_invalid_emails(mail, invalid_emails)
    end

    # Return true if all emails are valid, false otherwise
    invalid_emails.empty?
  end

  # Store invalid emails for review
  # @param mail [Mail::Message] The original email
  # @param invalid_emails [Array] List of invalid emails with reasons
  def store_invalid_emails(mail, invalid_emails)
    # Create a log file for invalid emails
    log_file = File.join(Rails.root, 'log', 'invalid_emails.log')

    File.open(log_file, 'a') do |f|
      f.puts "#{Time.current} - INVALID EMAIL RECIPIENTS"
      f.puts "From: #{mail.from.inspect}"
      f.puts "Subject: #{mail.subject}"
      f.puts 'Invalid emails:'
      invalid_emails.each do |invalid|
        f.puts "  - #{invalid[:email]}: #{invalid[:reason]}"
      end
      f.puts '-------------------------------------------'
    end

    # You could also implement a notification system here to alert admins
    # Or create an InvalidEmail model to store these in the database
  end

  # Modify your deliver! method to include verification
  def deliver!(mail)
    # Verify recipient emails first
    # unless verify_recipient_emails(mail)
    #   Rails.logger.warn("Skipping email delivery due to invalid recipient emails: From: #{mail.from.inspect}, To: #{mail.to.inspect}")
    #   return { 'success' => false, 'error' => 'Invalid recipient emails' }
    # end

    raise ArgumentError, 'API key is missing' unless @settings[:api_key].present?

    # Initialize the MailerSend client
    client = Mailersend::Client.new(@settings[:api_key])

    # Initialize the Email class with the client
    ms_email = Mailersend::Email.new(client)

    # Extract email attributes and add to MailerSend email object
    add_email_attributes(ms_email, mail)

    # Send the email
    begin
      response = ms_email.send

      # Check if the response indicates a success (2xx status code)
      if response.is_a?(HTTP::Response) && response.status.success?
        Rails.logger.info("MailerSend delivery successful: #{response.inspect}")
      else
        # If it's an HTTP response with an error status code, log the body content
        error_message = if response.is_a?(HTTP::Response)
                          body = begin
                            JSON.parse(response.body.to_s)
                          rescue StandardError
                            response.body.to_s
                          end
                          "MailerSend API error (#{response.status}): #{body.inspect}"
                        else
                          "MailerSend delivery returned unexpected response: #{response.inspect}"
                        end

        Rails.logger.error(error_message)

        # Only raise in production to trigger retries
        raise error_message unless Rails.env.development? || Rails.env.test?
      end

      response
    rescue StandardError => e
      Rails.logger.error("MailerSend delivery failed: #{e.message}")

      # In development and test, don't raise errors to prevent app crashes
      # In production, raise to ensure proper error handling with Sidekiq retries, etc.
      raise e unless Rails.env.development? || Rails.env.test?

      # Return a failure response object for test environments
      { 'success' => false, 'error' => e.message }
    end
  end

  private

  # Adds all necessary attributes from the mail object to the MailerSend email object
  # @param ms_email [Mailersend::Email] The MailerSend email object
  # @param mail [Mail::Message] The mail object from ActionMailer
  def add_email_attributes(ms_email, mail)
    # Add sender information
    if mail.from.present?
      from_email = mail.from.first
      from_name = mail[:from]&.display_names&.first || 'Brag Rights'
      ms_email.add_from('email' => from_email, 'name' => from_name)
    end

    # Log detailed information for debugging
    Rails.logger.debug("MailerSend preparing email: From: #{mail.from.inspect}, To: #{mail.to.inspect}, Subject: #{mail.subject}")

    # Add recipients
    if mail.to.present?
      mail.to.each do |to_email|
        to_name = mail[:to]&.display_names&.first || to_email.split('@').first
        ms_email.add_recipients('email' => to_email, 'name' => to_name)
      end
    end

    # Add CC recipients if present
    if mail.cc.present?
      mail.cc.each do |cc_email|
        cc_name = mail[:cc]&.display_names&.first || cc_email.split('@').first
        ms_email.add_cc('email' => cc_email, 'name' => cc_name)
      end
    end

    # Add BCC recipients if present
    if mail.bcc.present?
      mail.bcc.each do |bcc_email|
        bcc_name = mail[:bcc]&.display_names&.first || bcc_email.split('@').first
        ms_email.add_bcc('email' => bcc_email, 'name' => bcc_name)
      end
    end

    # Add subject
    ms_email.add_subject(mail.subject) if mail.subject.present?

    # Add content
    if mail.html_part
      ms_email.add_html(mail.html_part.body.to_s)
    elsif mail.content_type&.include?('text/html')
      ms_email.add_html(mail.body.to_s)
    else
      ms_email.add_html("<div>#{mail.body}</div>")
    end

    if mail.text_part
      ms_email.add_text(mail.text_part.body.to_s)
    elsif mail.content_type&.include?('text/plain')
      ms_email.add_text(mail.body.to_s)
    else
      ms_email.add_text(mail.body.to_s)
    end

    # Add template_id if specified in the mail object
    ms_email.add_template_id(mail.header[:template_id].to_s) if mail.header[:template_id].present?

    # Add personalization variables if present
    add_personalization(ms_email, mail)

    # Add attachments if present
    add_attachments(ms_email, mail)
  end

  # Add personalization variables to the email
  # @param ms_email [Mailersend::Email] The MailerSend email object
  # @param mail [Mail::Message] The mail object
  def add_personalization(ms_email, mail)
    return unless mail.header[:variables].present?

    begin
      variables = JSON.parse(mail.header[:variables].to_s)
      variables.each do |recipient_vars|
        ms_email.add_personalization(recipient_vars)
      end
    rescue JSON::ParserError => e
      Rails.logger.error("Failed to parse variables: #{e.message}")
    end
  end

  # Add attachments to the email
  # @param ms_email [Mailersend::Email] The MailerSend email object
  # @param mail [Mail::Message] The mail object
  def add_attachments(ms_email, mail)
    return if mail.attachments.empty?

    mail.attachments.each do |attachment|
      # Extract just the base disposition without the filename parameter
      disposition = attachment.content_disposition&.split(';')&.first || 'attachment'

      ms_email.add_attachment(
        content: Base64.encode64(attachment.body.to_s),
        filename: attachment.filename,
        disposition:
      )
    end
  end
end

# Define a custom delivery method that falls back to SMTP when MailerSend has domain verification issues
class MailerSendWithFallbackDeliveryMethod
  attr_accessor :settings

  # Initialize with settings from config
  # @param settings [Hash] Configuration settings including :api_key
  def initialize(settings)
    @settings = settings
    @mailersend = MailerSendDeliveryMethod.new(settings)
    @smtp = Mail::SMTP.new(ActionMailer::Base.smtp_settings)
    @file = Mail::FileDelivery.new(ActionMailer::Base.file_settings)
  end

  # Delivers an email with MailerSend, falling back to SMTP if domain verification fails
  # @param mail [Mail::Message] The mail object to be delivered
  # @return [Hash, Mail::SMTP::Response] The response from the delivery method used
  def deliver!(mail)
    # First attempt delivery with MailerSend
    response = @mailersend.deliver!(mail)
    # If we get here, the delivery was successful
    Rails.logger.info("Email delivered successfully via MailerSend: From: #{mail.from.inspect}, To: #{mail.to.inspect}")
    response
  rescue StandardError => e
    error_message = e.message.to_s

    # Check if the error is related to domain verification
    raise e unless domain_verification_error?(error_message)

    Rails.logger.warn("MailerSend domain verification issue detected, falling back to SMTP: #{error_message}")

    # Log the fallback for monitoring
    Rails.logger.info("Email fallback: From: #{mail.from.inspect}, To: #{mail.to.inspect}, Subject: #{mail.subject}")

    # Verify SMTP authentication is properly configured
    if smtp_authentication_available?
      # Attempt delivery with SMTP
      begin
        smtp_response = @smtp.deliver!(mail)
        Rails.logger.info("Email delivered successfully via SMTP fallback: From: #{mail.from.inspect}, To: #{mail.to.inspect}")
        smtp_response
      rescue StandardError => smtp_error
        Rails.logger.error("SMTP fallback failed: #{smtp_error.message}")

        # If SMTP fails, try the file delivery method as a last resort
        return last_resort_fallback(mail, smtp_error) if use_last_resort_fallback?

        # If we don't want to use the last resort, re-raise the SMTP error
        raise smtp_error
      end
    else
      # If SMTP authentication is not available, go directly to the last resort
      Rails.logger.warn('SMTP authentication not properly configured, using last resort fallback')
      last_resort_fallback(mail, StandardError.new('SMTP authentication not configured'))
    end

    # For other types of errors, re-raise the original exception
  end

  private

  # Checks if the error message indicates a domain verification issue
  # @param error_message [String] The error message from MailerSend
  # @return [Boolean] True if the error is related to domain verification
  def domain_verification_error?(error_message)
    # Check for specific error codes or messages related to domain verification
    error_message.include?('domain must be verified') ||
      error_message.include?('#MS42207') ||
      error_message.include?('#MS42212') ||
      error_message.include?('recipient domain')
  end

  # Checks if SMTP authentication is properly configured
  # @return [Boolean] True if SMTP authentication is properly configured
  def smtp_authentication_available?
    smtp_settings = ActionMailer::Base.smtp_settings
    smtp_settings[:password].present? &&
      smtp_settings[:user_name].present? &&
      smtp_settings[:address].present?
  end

  # Determines if we should use the last resort fallback
  # @return [Boolean] True if we should use the last resort fallback
  def use_last_resort_fallback?
    # Only use the last resort in production to ensure emails don't block the application
    Rails.env.production?
  end

  # Uses the file delivery method as a last resort fallback
  # @param mail [Mail::Message] The mail object to be delivered
  # @param original_error [StandardError] The original error that triggered the fallback
  # @return [Object] The response from the file delivery method
  def last_resort_fallback(mail, original_error)
    # Create a directory to store emails if it doesn't exist
    FileUtils.mkdir_p(ActionMailer::Base.file_settings[:location])

    # Log that we're using the last resort fallback
    Rails.logger.warn("Using last resort file delivery for email: From: #{mail.from.inspect}, To: #{mail.to.inspect}")

    # Store the original error in the mail for debugging
    mail.header['X-Delivery-Error'] = original_error.message

    # Deliver the mail using the file delivery method
    @file.deliver!(mail)

    # Also log to a special file for admin attention
    log_email_for_admin_attention(mail, original_error)

    # Return a success response to prevent application errors
    { 'success' => true, 'fallback' => 'file', 'message' => 'Email saved to file for manual processing' }
  rescue StandardError => e
    # If even the last resort fails, log it but don't crash the application
    Rails.logger.error("Last resort fallback also failed: #{e.message}")
    { 'success' => false, 'error' => e.message }
  end

  # Logs email information to a special file for admin attention
  # @param mail [Mail::Message] The mail object to be delivered
  # @param original_error [StandardError] The original error that triggered the fallback
  def log_email_for_admin_attention(mail, original_error)
    log_file = File.join(ActionMailer::Base.file_settings[:location], 'failed_emails.log')

    File.open(log_file, 'a') do |f|
      f.puts "#{Time.current} - FAILED EMAIL DELIVERY"
      f.puts "From: #{mail.from.inspect}"
      f.puts "To: #{mail.to.inspect}"
      f.puts "Subject: #{mail.subject}"
      f.puts "Error: #{original_error.message}"
      f.puts '-------------------------------------------'
    end
  end
end

# Register the delivery method with ActionMailer
require 'mailersend'
require 'fileutils'

ActionMailer::Base.add_delivery_method :mailersend, MailerSendDeliveryMethod,
                                       api_key: ENV['MAILERSEND_API_KEY']

# Register the custom delivery method with ActionMailer
ActionMailer::Base.add_delivery_method :mailersend_with_fallback, MailerSendWithFallbackDeliveryMethod,
                                       api_key: ENV['MAILERSEND_API_KEY']
