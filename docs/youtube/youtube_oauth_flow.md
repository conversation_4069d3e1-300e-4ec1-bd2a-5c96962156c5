# YouTube OAuth Flow Documentation

## Overview

This document outlines the complete OAuth flow for connecting a user's YouTube account to BragRights. This integration enables features like subscriber-only leagues and content creator verification.

> **Note**: For important considerations regarding email matching, username display, and user registration via YouTube, see [Integration Considerations](./integration_considerations.md).

## OAuth Flow

### 1. Frontend Initiates OAuth Flow

The frontend initiates the OAuth flow by redirecting the user to Google's authorization page:

```typescript
export const initiateYoutubeAuth = () => {
  const clientId = process.env.NEXT_PUBLIC_YOUTUBE_CLIENT_ID;
  const redirectUri = process.env.NEXT_PUBLIC_YOUTUBE_REDIRECT_URI;

  // Define required scopes - use minimal permissions
  const scopes = [
    "https://www.googleapis.com/auth/youtube.readonly",
    "https://www.googleapis.com/auth/userinfo.email",
    "https://www.googleapis.com/auth/userinfo.profile",
  ].join(" ");

  // Note: We intentionally avoid requesting the youtube.force-ssl scope
  // as it requests excessive permissions including editing and deleting videos

  // Build the OAuth URL
  const authUrl =
    `https://accounts.google.com/o/oauth2/v2/auth?` +
    `client_id=${clientId}` +
    `&redirect_uri=${encodeURIComponent(redirectUri)}` +
    `&response_type=code` +
    `&scope=${encodeURIComponent(scopes)}` +
    `&access_type=offline` +
    `&prompt=consent`;

  // Redirect to the OAuth URL
  window.location.href = authUrl;
};
```

### 2. Google Redirects Back with Authorization Code

After the user authorizes the application, Google redirects back to the specified redirect URI with an authorization code.

### 3. Frontend Handles the Callback

The frontend receives the authorization code and needs to exchange it for tokens:

```typescript
// Next.js API route to handle the OAuth callback
// pages/api/auth/youtube/callback.ts
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { code } = req.query;

  if (!code || typeof code !== "string") {
    return res.status(400).json({ error: "Missing authorization code" });
  }

  try {
    // Get the auth token from cookies
    const token = getCookie("auth_token", { req, res });

    if (!token) {
      return res.status(401).json({ error: "Not authenticated" });
    }

    // Exchange the code for tokens
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/v1/youtube_auth/connect`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          code,
          redirect_uri: process.env.NEXT_PUBLIC_YOUTUBE_REDIRECT_URI,
          client_id: process.env.NEXT_PUBLIC_YOUTUBE_CLIENT_ID,
        }),
      }
    );

    const data = await response.json();

    // Redirect to the profile page or a success page
    return res.redirect(307, "/profile?youtube_connected=true");
  } catch (error) {
    console.error("Error handling YouTube callback:", error);
    return res.redirect(307, "/profile?youtube_error=true");
  }
}
```

### 4. Backend Exchanges Code for Tokens

The backend needs to exchange the authorization code for access and refresh tokens. This step is currently missing in the implementation and needs to be added to the `YoutubeAuthController#connect` method:

```ruby
# app/controllers/api/v1/youtube_auth_controller.rb
def connect
  # Validate the request
  unless params[:code].present? && params[:redirect_uri].present? && params[:client_id].present?
    render json: { error: 'Missing required parameters' }, status: :bad_request
    return
  end

  # Exchange the authorization code for tokens
  response = HTTP.post(
    'https://oauth2.googleapis.com/token',
    form: {
      code: params[:code],
      client_id: params[:client_id],
      client_secret: ENV['GOOGLE_CLIENT_SECRET'],
      redirect_uri: params[:redirect_uri],
      grant_type: 'authorization_code'
    }
  )

  if response.status.success?
    token_data = JSON.parse(response.body.to_s)

    # Get user info from YouTube API
    youtube_service = Google::Apis::YoutubeV3::YouTubeService.new
    youtube_service.key = ENV['YOUTUBE_API_KEY']
    youtube_service.authorization = token_data['access_token']

    # Get channel info
    channels = youtube_service.list_channels('snippet,statistics', mine: true)
    channel = channels.items.first

    # Connect the YouTube account
    auth_data = {
      channel_id: channel.id,
      channel_name: channel.snippet.title,
      avatar_url: channel.snippet.thumbnails.default.url,
      subscriber_count: channel.statistics.subscriber_count,
      credentials: {
        access_token: token_data['access_token'],
        refresh_token: token_data['refresh_token'],
        expires_in: token_data['expires_in'],
        token_type: token_data['token_type'],
        scope: token_data['scope']
      }
    }

    if current_devise_api_user.connect_youtube_account(auth_data)
      render json: {
        message: 'YouTube account connected successfully',
        user: {
          id: current_devise_api_user.id,
          youtube_channel_id: current_devise_api_user.youtube_channel_id,
          youtube_channel_name: current_devise_api_user.youtube_channel_name,
          is_content_creator: current_devise_api_user.is_content_creator
        }
      }, status: :ok
    else
      render json: { error: 'Failed to connect YouTube account' }, status: :unprocessable_entity
    end
  else
    render json: { error: "Failed to exchange authorization code: #{response.body}" }, status: :bad_request
  end
end
```

### 5. Store Tokens Securely

The `YoutubeTokenService` handles secure storage of tokens:

```ruby
# app/services/youtube_token_service.rb
def self.store_credentials(user, auth_data)
  # Extract token data
  credentials = {
    'access_token' => auth_data[:access_token],
    'refresh_token' => auth_data[:refresh_token],
    'expires_at' => Time.now.to_i + auth_data[:expires_in].to_i,
    'token_type' => auth_data[:token_type],
    'scope' => auth_data[:scope]
  }

  # Store as JSON string
  user.update(youtube_credentials: credentials.to_json)
end
```

### 6. Refresh Tokens When Needed

The `YoutubeTokenService` also handles token refresh:

```ruby
# app/services/youtube_token_service.rb
def self.refresh_token(user, credentials)
  # Make request to Google's token endpoint
  response = HTTP.post(
    'https://oauth2.googleapis.com/token',
    form: {
      client_id: ENV['GOOGLE_CLIENT_ID'],
      client_secret: ENV['GOOGLE_CLIENT_SECRET'],
      refresh_token: credentials['refresh_token'],
      grant_type: 'refresh_token'
    }
  )

  if response.status.success?
    new_credentials = JSON.parse(response.body.to_s)

    # Update credentials, keeping the refresh token which doesn't change
    credentials['access_token'] = new_credentials['access_token']
    credentials['expires_at'] = Time.now.to_i + new_credentials['expires_in']

    # Save updated credentials
    user.update(youtube_credentials: credentials.to_json)

    credentials['access_token']
  else
    # Handle refresh failure - could be invalid refresh token
    Rails.logger.error "Failed to refresh YouTube token: #{response.body}"
    handle_token_refresh_failure(user, response)
    nil
  end
end
```

## Required Environment Variables

### Backend

```
# Required for YouTube OAuth
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_client_secret
YOUTUBE_API_KEY=your_api_key
FRONTEND_URL=http://localhost:3000 (for development)

# IMPORTANT: Required for Active Record Encryption in production
# These must be set when the `encrypts :youtube_credentials` line is uncommented in the User model
ENCRYPTION_PRIMARY_KEY=your_primary_key
ENCRYPTION_DETERMINISTIC_KEY=your_deterministic_key
ENCRYPTION_KEY_DERIVATION_SALT=your_key_derivation_salt
```

> ⚠️ **ENCRYPTION CONFIGURATION WARNING**:
>
> Currently, encryption for YouTube credentials is disabled in development to simplify testing.
> Before deploying to production, you MUST:
>
> 1. Uncomment the `encrypts :youtube_credentials` line in the User model
> 2. Set secure values for the encryption environment variables
> 3. Migrate any existing unencrypted credentials to encrypted format
>
> Failure to properly configure encryption in production will expose sensitive OAuth tokens!

### Frontend

```
NEXT_PUBLIC_YOUTUBE_CLIENT_ID=your_client_id
NEXT_PUBLIC_YOUTUBE_REDIRECT_URI=http://localhost:3000/auth/youtube/callback (for development)
NEXT_PUBLIC_API_URL=http://localhost:3010 (for development)
```

## Security Considerations

1. **Token Storage**:

   - **IMPORTANT**: OAuth tokens should be encrypted at rest using Rails' Active Record Encryption.
   - In development, encryption is currently disabled by commenting out the `encrypts :youtube_credentials` line in the User model.
   - For production, encryption MUST be re-enabled by uncommenting this line and configuring the encryption keys.

2. **Client Secret**: The client secret is only stored on the backend and never exposed to the frontend.

3. **Token Refresh**: Tokens are automatically refreshed when they expire.

4. **Token Revocation**: When a user disconnects their YouTube account, tokens are properly revoked with Google.

5. **OAuth vs API Key**: The application uses OAuth tokens for all YouTube API calls instead of API keys when possible. This provides better security and avoids issues with HTTP referrer restrictions.

## Verifying the Integration

You can verify that the YouTube OAuth integration is working correctly by checking the following:

### 1. Check User's YouTube Connection Status

```ruby
# In Rails console
user = User.find_by(email: "<EMAIL>")
puts "YouTube connected: #{user.youtube_connected?}"
puts "YouTube channel ID: #{user.youtube_channel_id}"
puts "YouTube channel name: #{user.youtube_channel_name}"
puts "YouTube credentials present: #{user.youtube_credentials.present?}"
```

### 2. Verify Stored Credentials

```ruby
# In Rails console
credentials = JSON.parse(user.youtube_credentials) rescue nil
if credentials
  puts "Access token present: #{credentials['access_token'].present?}"
  puts "Refresh token present: #{credentials['refresh_token'].present?}"
  puts "Expires at: #{Time.at(credentials['expires_at'].to_i)}"
end
```

### 3. Test YouTube API Access

```ruby
# In Rails console
require 'google/apis/youtube_v3'

# Get access token
access_token = user.youtube_access_token
puts "Access token present: #{access_token.present?}"

# Create YouTube service
service = Google::Apis::YoutubeV3::YouTubeService.new
service.authorization = access_token

# Fetch channel info
channel = service.list_channels('snippet,statistics', id: user.youtube_channel_id)
if channel.items.any?
  channel_item = channel.items.first
  puts "Channel title: #{channel_item.snippet.title}"
  puts "Subscriber count: #{channel_item.statistics.subscriber_count}"
end
```

### 4. Check API Endpoint

```bash
# Using curl (replace YOUR_AUTH_TOKEN with a valid token)
curl -H "Authorization: Bearer YOUR_AUTH_TOKEN" http://localhost:3010/api/v1/youtube_auth/status
```

If all of these checks pass, your YouTube OAuth integration is working correctly!

## Troubleshooting

### Common Issues

1. **"Missing required parameters" error**:

   - Ensure you're sending all required parameters in the connect request: `code`, `redirect_uri`, and `client_id`.

2. **"Invalid grant" error**:

   - Authorization codes can only be used once and expire quickly.
   - Ensure you're using the code immediately after receiving it.
   - Check that the redirect URI exactly matches what was registered in Google Cloud Console.

3. **"Invalid client" error**:

   - Verify that the client ID and client secret are correct.
   - Ensure the client ID is authorized for the redirect URI being used.

4. **"Access denied" error**:

   - The user denied permission during the OAuth flow.
   - Ensure you're requesting the minimum necessary scopes.

5. **"Requests from referer <empty> are blocked" error**:
   - This occurs when the Google API key has HTTP referrer restrictions but the requests don't include a referrer header.
   - Solutions:
     - Use OAuth tokens instead of API keys for YouTube API calls (recommended)
     - Update the Google Cloud Console settings for your API key to allow requests without referrers
     - Add your backend domain to the allowed referrers in the Google Cloud Console

## Logging and Troubleshooting

The YouTube OAuth integration includes comprehensive logging to help diagnose issues:

### Controller Logging

The `YoutubeAuthController` logs detailed information at each step of the OAuth flow:

- Request parameters (excluding sensitive data)
- Environment variable checks
- Token exchange requests and responses
- YouTube API calls
- Success and error states

### Token Service Logging

The `YoutubeTokenService` logs detailed information about token management:

- Token validation and expiration checks
- Token refresh attempts and results
- Token revocation
- Credential storage

### User Model Logging

The `User` model logs YouTube account operations:

- Connection status checks
- Account connection and disconnection
- Token retrieval

### Log Levels

- `info`: Normal operation events (successful requests, token refreshes)
- `error`: Failures and exceptions (missing parameters, API errors)
- `debug`: Detailed status information (token presence checks)

### Viewing Logs

To view the logs in development:

```
tail -f log/development.log | grep YouTube
```

To view the logs in production:

```
tail -f log/production.log | grep YouTube
```

### Common Log Patterns

1. **Missing Parameters**:

   ```
   Missing required parameters: code, redirect_uri, client_id
   ```

   - Ensure all required parameters are being sent from the frontend

2. **Token Exchange Failures**:

   ```
   Failed to exchange authorization code. Status: 400, Body: {"error":"invalid_grant"}
   ```

   - Check that the authorization code is being used promptly (they expire quickly)
   - Verify the redirect URI matches exactly what's registered in Google Cloud Console

3. **Environment Variable Issues**:

   ```
   Missing required environment variables: GOOGLE_CLIENT_SECRET
   ```

   - Ensure all required environment variables are set

4. **Active Record Encryption Issues**:
   ```
   ActiveRecord::Encryption::Errors::Configuration: key_derivation_salt is not configured
   ```
   - **Current Status**: Encryption is currently disabled in development by commenting out the `encrypts :youtube_credentials` line in the User model.
   - **For Development**:
     - Keep encryption disabled to simplify testing
     - Ensure the `encrypts :youtube_credentials` line remains commented out
   - **For Production**:
     - Uncomment the `encrypts :youtube_credentials` line in the User model
     - Set secure values for all encryption environment variables:
       - `ENCRYPTION_PRIMARY_KEY` (use `rails secret` to generate)
       - `ENCRYPTION_DETERMINISTIC_KEY` (use `rails secret` to generate)
       - `ENCRYPTION_KEY_DERIVATION_SALT` (use `rails secret` to generate)
     - Add the encryption configuration to your production environment:
       ```ruby
       # config/application.rb or config/environments/production.rb
       config.active_record.encryption.primary_key = ENV.fetch('ENCRYPTION_PRIMARY_KEY')
       config.active_record.encryption.deterministic_key = ENV.fetch('ENCRYPTION_DETERMINISTIC_KEY')
       config.active_record.encryption.key_derivation_salt = ENV.fetch('ENCRYPTION_KEY_DERIVATION_SALT')
       ```
     - Migrate existing credentials:
       ```ruby
       # Run in Rails console on production after configuring encryption
       User.where.not(youtube_credentials: nil).find_each do |user|
         begin
           # Store current credentials
           creds = JSON.parse(user.youtube_credentials)
           # Re-save to encrypt
           user.update_column(:youtube_credentials, nil) # Clear first
           user.update(youtube_credentials: creds.to_json)
           puts "Migrated credentials for user #{user.id}"
         rescue => e
           puts "Error migrating credentials for user #{user.id}: #{e.message}"
         end
       end
       ```
