# YouTube Credentials Encryption Configuration

## Current Status

Currently, encryption for YouTube credentials is **disabled** in development to simplify testing. This is done by commenting out the `encrypts :youtube_credentials` line in the User model:

```ruby
# app/models/user.rb
class User < ApplicationRecord
  # TODO: Re-enable encryption once properly configured
  # encrypts :youtube_credentials
  
  # ... rest of the model
end
```

## Why Encryption is Important

YouTube OAuth credentials include sensitive tokens that can be used to access a user's YouTube account. These tokens should be encrypted at rest to prevent unauthorized access in case of a data breach.

## Configuring Encryption for Production

Before deploying to production, you **MUST** configure Active Record Encryption properly:

### 1. Generate Secure Keys

Generate secure random keys for encryption:

```bash
# Run these commands to generate secure keys
ENCRYPTION_PRIMARY_KEY=$(rails secret)
ENCRYPTION_DETERMINISTIC_KEY=$(rails secret)
ENCRYPTION_KEY_DERIVATION_SALT=$(rails secret)

echo "ENCRYPTION_PRIMARY_KEY=$ENCRYPTION_PRIMARY_KEY"
echo "ENCRYPTION_DETERMINISTIC_KEY=$ENCRYPTION_DETERMINISTIC_KEY"
echo "ENCRYPTION_KEY_DERIVATION_SALT=$ENCRYPTION_KEY_DERIVATION_SALT"
```

### 2. Add Keys to Environment

Add these keys to your production environment variables:

```bash
# For Heroku
heroku config:set ENCRYPTION_PRIMARY_KEY=your_generated_key
heroku config:set ENCRYPTION_DETERMINISTIC_KEY=your_generated_key
heroku config:set ENCRYPTION_KEY_DERIVATION_SALT=your_generated_key

# For Docker/Kubernetes
# Add to your environment configuration
```

### 3. Configure Active Record Encryption

Ensure your application is configured to use these keys:

```ruby
# config/application.rb or config/environments/production.rb
config.active_record.encryption.primary_key = ENV.fetch('ENCRYPTION_PRIMARY_KEY')
config.active_record.encryption.deterministic_key = ENV.fetch('ENCRYPTION_DETERMINISTIC_KEY')
config.active_record.encryption.key_derivation_salt = ENV.fetch('ENCRYPTION_KEY_DERIVATION_SALT')
```

### 4. Enable Encryption in the User Model

Uncomment the encryption line in the User model:

```ruby
# app/models/user.rb
class User < ApplicationRecord
  # Enable encryption for sensitive data
  encrypts :youtube_credentials
  
  # ... rest of the model
end
```

### 5. Migrate Existing Credentials

If you have existing unencrypted credentials in your database, you need to migrate them to the encrypted format:

```ruby
# Run in Rails console on production after configuring encryption
User.where.not(youtube_credentials: nil).find_each do |user|
  begin
    # Store current credentials
    creds = JSON.parse(user.youtube_credentials)
    # Re-save to encrypt
    user.update_column(:youtube_credentials, nil) # Clear first
    user.update(youtube_credentials: creds.to_json)
    puts "Migrated credentials for user #{user.id}"
  rescue => e
    puts "Error migrating credentials for user #{user.id}: #{e.message}"
  end
end
```

## Troubleshooting Encryption Issues

### Common Errors

1. **Missing Configuration Error**:
   ```
   ActiveRecord::Encryption::Errors::Configuration: key_derivation_salt is not configured
   ```
   - Ensure all three environment variables are set
   - Check that the configuration is loaded before the model is used

2. **Decryption Error**:
   ```
   ActiveRecord::Encryption::Errors::Decryption: Decryption failed
   ```
   - This usually happens when trying to decrypt data that was encrypted with different keys
   - Ensure you're using the same keys across deployments
   - If keys were rotated, use Active Record's key rotation features

### Testing Encryption

To verify that encryption is working correctly:

```ruby
# In Rails console
user = User.find_by(email: "<EMAIL>")

# Check if credentials are encrypted
encrypted_value = user.attributes_before_type_cast["youtube_credentials"]
puts "Credentials are encrypted: #{encrypted_value.include?('--encrypted--')}"

# Check if decryption works
begin
  decrypted = user.youtube_credentials
  creds = JSON.parse(decrypted)
  puts "Decryption successful: #{creds['access_token'].present?}"
rescue => e
  puts "Decryption failed: #{e.message}"
end
```

## Security Best Practices

1. **Never commit encryption keys to source control**
2. **Use different keys for each environment** (development, staging, production)
3. **Rotate keys periodically** for enhanced security
4. **Backup your encryption keys securely** - if you lose them, you cannot decrypt the data
5. **Limit access to encryption keys** to only those who absolutely need it
