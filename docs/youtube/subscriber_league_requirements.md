# Subscriber-Only League Requirements

This document outlines the requirements and restrictions for creating subscriber-only leagues in the BragRights application.

## Overview

Subscriber-only leagues are a special type of league that requires users to be subscribed to a specific YouTube channel in order to join. These leagues are intended for YouTube content creators to engage with their subscribers.

## Requirements for Creating Subscriber-Only Leagues

To create a subscriber-only league, a user must meet the following requirements:

1. **Connected YouTube Account**: The user must have connected their YouTube account to BragRights.
2. **Content Creator Mode**: The user must have enabled content creator mode in their profile.
3. **Verified YouTube Channel**: The user's YouTube channel must be verified.
4. **Minimum Subscriber Count**: The user's YouTube channel must have at least the minimum required number of subscribers (configurable via environment variable, default: 100).

## Implementation Details

### YouTube Eligibility Service

The `YoutubeEligibilityService` handles checking if a user is eligible to create subscriber-only leagues:

```ruby
# app/services/youtube_eligibility_service.rb
class YoutubeEligibilityService
  # Minimum number of subscribers required to create a subscriber-only league
  MIN_SUBSCRIBERS_FOR_LEAGUE = ENV.fetch('MIN_SUBSCRIBERS_FOR_LEAGUE', 100).to_i

  # Check if a user can create a subscriber-only league
  def self.can_create_subscriber_league?(user)
    # Implementation details...
  end
  
  # Get the reason why a user cannot create a subscriber-only league
  def self.subscriber_league_ineligibility_reason(user)
    # Implementation details...
  end
  
  # Get the minimum subscriber count required
  def self.min_subscribers_required
    MIN_SUBSCRIBERS_FOR_LEAGUE
  end
end
```

### League Creation Validation

When a user attempts to create a subscriber-only league, the system validates their eligibility:

```ruby
# In LeaguesController#create
if league.subscriber_only?
  # Check if user meets eligibility requirements for creating subscriber leagues
  unless YoutubeEligibilityService.can_create_subscriber_league?(current_devise_api_user)
    reason = YoutubeEligibilityService.subscriber_league_ineligibility_reason(current_devise_api_user)
    render json: { 
      errors: ["Cannot create subscriber-only league: #{reason}"],
      min_subscribers_required: YoutubeEligibilityService.min_subscribers_required
    }, status: :unprocessable_entity
    return
  end
end
```

### Checking Eligibility

The frontend can check if a user is eligible to create subscriber-only leagues using the following endpoint:

```
GET /api/v1/youtube/check_subscriber_league_eligibility
```

This endpoint returns:

```json
{
  "eligible": true|false,
  "message": "Reason for eligibility or ineligibility",
  "subscriber_count": 123,
  "min_subscribers_required": 100
}
```

## Configuration

The minimum subscriber count can be configured using the environment variable:

```
MIN_SUBSCRIBERS_FOR_LEAGUE=100
```

## Frontend Considerations

The frontend should:

1. Check the user's eligibility before showing the option to create a subscriber-only league
2. Display appropriate messages explaining why a user cannot create a subscriber-only league
3. Show the minimum subscriber count required
4. Provide guidance on how to meet the requirements (connecting YouTube account, enabling content creator mode, etc.)

## Security Considerations

1. **Subscriber Count Verification**: The subscriber count is verified using the YouTube API when the user connects their account.
2. **Periodic Updates**: The subscriber count should be periodically updated to ensure users still meet the requirements.
3. **API Rate Limiting**: The YouTube API has rate limits, so caching is used to minimize API calls.
