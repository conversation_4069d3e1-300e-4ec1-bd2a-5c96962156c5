# Frontend Changes for Basic-only Google OAuth Rollout

Objective: Launch with Google basic auth only (openid email profile). Defer YouTube Connect and Creator features until Google verification completes.

## Summary
- Keep Google Sign in/Sign up flows using only basic scopes.
- Do NOT initiate the "connect" flow in production.
- Handle 403 responses gracefully if any legacy UI calls a gated endpoint.

## Environment/config
- Ensure the FE feature toggle `youtubeConnect` is OFF in production. If you keep a .env flag, align it accordingly.
- Confirm these public envs:
  - NEXT_PUBLIC_GOOGLE_CLIENT_ID
  - NEXT_PUBLIC_YOUTUBE_REDIRECT_URI (used for basic auth callback page as well)

## OAuth init (client side)
- For signup/login, request scopes: `openid email profile` only.
- Do not include `youtube.readonly`.
- Include `prompt=consent` and `include_granted_scopes=true` to allow incremental auth later.

Example Google auth URL params:
- scope: `openid email profile`
- access_type: `online` (no refresh token needed on FE; <PERSON><PERSON> handles session tokens)
- include_granted_scopes: `true`

## Callback handling
- After Google redirects with `code`, post it to backend:
  - POST `${BACK_END_URL}/api/v1/auth/youtube/login` for existing users
  - POST `${BACK_END_URL}/api/v1/auth/youtube/signup` for new users
  - Body: `{ code, redirect_uri: NEXT_PUBLIC_YOUTUBE_REDIRECT_URI, client_id: NEXT_PUBLIC_GOOGLE_CLIENT_ID }`
- On success, store BE tokens in your existing HttpOnly cookie wrapper as today.

## Disable Connect and Creator in prod
- Do not render UI entry points for:
  - Connect YouTube (profile/settings)
  - Creator mode toggle
  - Subscriber-only actions
- If any code path still calls:
  - POST /api/v1/youtube_auth/connect
  - POST /api/v1/youtube_auth/disconnect
  - GET  /api/v1/youtube_auth/status
  - POST /api/v1/youtube_auth/verify_subscription
  - POST /api/v1/youtube/update_creator_status
  - GET  /api/v1/youtube/check_subscriber_league_eligibility
  then handle 403 by showing a non-blocking message: "YouTube features will be available soon."

## QA checklist (FE)
- [ ] Google login with basic scopes succeeds end-to-end (new and existing users)
- [ ] No UI paths initiate Connect in production
- [ ] Callback page works for `mode=login|signup` and does not attempt Connect
- [ ] Proper error message on 403 if a gated endpoint is accidentally hit

## Enabling later
- When verification completes, turn on FE feature toggle `youtubeConnect` and surface the Connect entry point in Profile. No FE code changes beyond toggles should be necessary.

