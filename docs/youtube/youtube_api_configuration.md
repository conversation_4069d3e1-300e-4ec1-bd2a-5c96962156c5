# YouTube API Configuration Guide

This document provides information about configuring and using the YouTube API in the BragRights application.

## API Key Configuration

The YouTube service is configured to use an API key from environment variables:

```ruby
@service.key = ENV['YOUTUBE_API_KEY']
```

This line is found in the `BragRightsYouTubeService` class and sets the API key for authenticating requests to the YouTube Data API.

## Environment Variables

To use the YouTube integration, you need to set up the following environment variables:

```
YOUTUBE_API_KEY=your_api_key
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_client_secret
YOUTUBE_OAUTH_CALLBACK_URL=https://your-domain.com/auth/youtube/callback
```

### Obtaining API Keys

1. **Create a project in Google Cloud Console**

   - Go to the [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select an existing one
   - Enable the YouTube Data API v3

2. **Create API credentials**

   - Navigate to "APIs & Services" > "Credentials"
   - Click "Create credentials" and select "API key"
   - Copy the generated API key

3. **Set up OAuth 2.0 credentials**
   - Click "Create credentials" and select "OAuth client ID"
   - Configure the OAuth consent screen
   - Set the application type to "Web application"
   - Add authorized redirect URIs:
     - `https://your-domain.com/auth/youtube/callback` (production)
     - `http://localhost:3000/auth/youtube/callback` (development)

## API Quotas and Limitations

The YouTube Data API has quota limits that you need to be aware of:

- Each API request consumes quota points
- The default quota is 10,000 units per day
- Different API methods consume different amounts of quota
- Subscription verification calls can be particularly quota-intensive

### Quota Management Strategies

1. **Caching**

   - Cache subscription status for at least 24 hours
   - Cache channel information to reduce API calls

2. **Batch Processing**

   - Run subscription verification jobs during off-peak hours
   - Process subscriptions in batches

3. **Monitoring**
   - Set up alerts for approaching quota limits
   - Monitor API usage regularly

## Security Considerations

1. **API Key Protection**

   - Store the API key securely in environment variables
   - Never expose the API key in client-side code
   - Restrict the API key to specific HTTP referrers in Google Cloud Console

2. **OAuth Token Storage**
   - Store OAuth tokens securely, preferably encrypted
   - Implement token refresh and rotation
   - Use the minimum required OAuth scopes

## Testing

When running tests, you should mock the YouTube API calls to avoid consuming quota and to ensure tests run consistently:

```ruby
# Example of mocking the YouTube API in tests
allow_any_instance_of(Google::Apis::YoutubeV3::YouTubeService)
  .to receive(:list_subscriptions)
  .and_return(mock_response)
```

## Troubleshooting

Common issues and solutions:

1. **API Key Invalid**

   - Verify the API key is correctly set in environment variables
   - Check if the API key has been restricted to specific IPs or referrers

2. **Quota Exceeded**

   - Implement caching to reduce API calls
   - Request a quota increase from Google if needed

3. **OAuth Errors**
   - Verify the correct scopes are being requested
   - Check that redirect URIs match exactly what's configured in Google Cloud Console
