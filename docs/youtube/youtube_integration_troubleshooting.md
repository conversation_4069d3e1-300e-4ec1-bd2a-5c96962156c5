# YouTube Integration Troubleshooting Guide

## Common Issues and Solutions

This guide provides solutions for common issues encountered with the YouTube integration in BragRights.

## OAuth Connection Issues

### 1. "PERMISSION_DENIED: Request had insufficient authentication scopes"

**Problem**: The OAuth token doesn't have the necessary permissions to perform the requested operation.

**Solution**:

- Ensure the frontend is requesting the correct scopes:
  ```javascript
  const scopes = [
    "https://www.googleapis.com/auth/youtube.readonly",
    "https://www.googleapis.com/auth/userinfo.email",
    "https://www.googleapis.com/auth/userinfo.profile",
  ].join(" ");
  ```
- Avoid requesting the `youtube.force-ssl` scope unless absolutely necessary, as it requests excessive permissions including the ability to edit and delete videos.
- If you're still getting permission errors, check the Google Cloud Console to ensure your project has the YouTube Data API v3 enabled.

### 2. "Failed to connect YouTube account for user: X"

**Problem**: The backend is unable to connect the YouTube account to the user record.

**Possible Causes and Solutions**:

1. **Unique Constraint Violation**:

   - Another user already has the same YouTube channel connected
   - Solution: Check if the YouTube channel is already connected to another account:
     ```ruby
     # In Rails console
     User.where(youtube_channel_id: 'CHANNEL_ID').pluck(:id, :email)
     ```
   - The application now checks for this and returns a 409 Conflict status with a clear error message.

2. **Redis Connection Issues**:

   - The application uses Redis for caching and quota tracking
   - Solution: Ensure Redis is running and properly configured:
     ```bash
     redis-cli ping
     ```
   - The application has been updated to gracefully handle Redis connection issues.

3. **Database Validation Errors**:
   - Check for validation errors when updating the user record:
     ```ruby
     # In Rails console
     user = User.find(X)
     user.errors.full_messages
     ```

### 3. "Invalid grant" Error

**Problem**: The authorization code is invalid or has expired.

**Solution**:

- Authorization codes can only be used once and expire quickly (usually within minutes)
- Ensure you're using the code immediately after receiving it
- Verify that the redirect URI exactly matches what was registered in Google Cloud Console
- Check that all required parameters are being sent: `code`, `redirect_uri`, and `client_id`

## API and Service Issues

### 1. YouTube API Quota Exceeded

**Problem**: You've reached the daily quota limit for the YouTube API.

**Solution**:

- The application now implements caching to reduce API calls
- The `YoutubeQuotaService` tracks API usage and prevents calls when near the quota limit
- Consider increasing your quota by requesting an increase from Google
- Monitor your quota usage in the Google Cloud Console

### 2. Redis Connection Issues

**Problem**: The application can't connect to Redis for caching or quota tracking.

**Solution**:

- The application has been updated to gracefully handle Redis connection issues
- Ensure Redis is running and properly configured:
  ```bash
  redis-cli ping
  ```
- Check Redis connection settings in `config/initializers/redis.rb`
- The application will continue to function even if Redis is unavailable, but with reduced performance

### 3. YouTube API Errors

**Problem**: The YouTube API returns errors when making requests.

**Solution**:

- The application now includes comprehensive error handling for YouTube API calls
- Check the logs for detailed error messages:
  ```bash
  tail -f log/development.log | grep "YouTube API error"
  ```
- Common API errors include:
  - `quotaExceeded`: You've reached your daily quota limit
  - `authError`: Authentication issues with the OAuth token
  - `invalidRequest`: Malformed request parameters
  - `accessNotConfigured`: The YouTube Data API is not enabled for your project

## Debugging Tools

### 1. YouTube API Test Script

Use the provided test script to diagnose YouTube API issues:

```bash
# Run the script for a specific user
rails runner scripts/test_youtube_api.rb USER_ID
```

This script:

- Checks if the user has YouTube connected
- Verifies the presence of YouTube credentials
- Tests token refresh
- Makes a test API call to fetch channel information
- Attempts to update the user with the channel information

### 2. Checking User's YouTube Connection

```ruby
# In Rails console
user = User.find(USER_ID)
puts "YouTube connected? #{user.youtube_connected?}"
puts "YouTube credentials present? #{user.youtube_credentials.present?}"

# Parse credentials (if present)
if user.youtube_credentials.present?
  credentials = JSON.parse(user.youtube_credentials)
  puts "Access token: #{credentials['access_token'][0..10]}... (truncated)"
  puts "Refresh token present: #{credentials['refresh_token'].present?}"
  puts "Token expires at: #{Time.at(credentials['expires_at'].to_i)}"
end
```

### 3. Testing YouTube API Access

```ruby
# In Rails console
require 'google/apis/youtube_v3'

user = User.find(USER_ID)
access_token = user.youtube_access_token

service = Google::Apis::YoutubeV3::YouTubeService.new
service.authorization = access_token

# Test API call
begin
  channels = service.list_channels('snippet,statistics', mine: true)
  if channels.items.any?
    channel = channels.items.first
    puts "Channel found: #{channel.id} (#{channel.snippet.title})"
    puts "Subscriber count: #{channel.statistics.subscriber_count}"
  else
    puts "No channels found"
  end
rescue Google::Apis::Error => e
  puts "API error: #{e.message}"
end
```

## Logging and Monitoring

The YouTube integration includes comprehensive logging to help diagnose issues:

### 1. Controller Logging

The `YoutubeAuthController` logs detailed information at each step of the OAuth flow:

- Request parameters (excluding sensitive data)
- Token exchange requests and responses
- YouTube API calls
- Success and error states

### 2. Service Logging

The `BragRightsYouTubeService` and `YoutubeTokenService` log detailed information about:

- API calls and responses
- Token validation and refresh
- Caching operations
- Error handling

### 3. Viewing Logs

To view YouTube-related logs:

```bash
# Development
tail -f log/development.log | grep -E "YouTube|youtube"

# Production
tail -f log/production.log | grep -E "YouTube|youtube"
```

## Recent Improvements

The YouTube integration has been improved with:

1. **Better Error Handling**:

   - Graceful handling of Redis connection issues
   - Improved error messages for common failure scenarios
   - Fallback mechanisms for caching failures

2. **Conflict Detection**:

   - The system now detects when a YouTube channel is already connected to another account
   - Returns a clear error message with a 409 Conflict status code

3. **Reduced Permission Requirements**:

   - Removed the `youtube.force-ssl` scope to request only the minimum necessary permissions
   - Users no longer see the concerning "edit and delete your YouTube videos" permission request

4. **Improved Logging**:

   - Added detailed logging throughout the YouTube integration
   - Better error messages to help diagnose issues

5. **Resilient Services**:
   - Services now gracefully handle external dependencies being unavailable
   - Added fallback mechanisms for caching and API calls

## Security Considerations

1. **OAuth Token Storage**:

   - YouTube credentials are stored in the `youtube_credentials` field of the User model
   - In production, these should be encrypted using Rails' Active Record Encryption
   - Ensure the encryption environment variables are properly configured in production

2. **API Key Protection**:

   - The YouTube API key is only used on the backend and never exposed to clients
   - API calls use OAuth tokens when possible instead of the API key

3. **Minimal Permissions**:
   - The application requests only the minimum necessary OAuth scopes
   - Avoids requesting sensitive permissions like `youtube.force-ssl` unless absolutely necessary

## Testing the Integration

A simple HTML test page is available at `/youtube_test.html` that allows you to:

1. Initiate the YouTube OAuth flow
2. Handle the callback and exchange the code for tokens
3. View the response from the backend

This is useful for testing the OAuth flow without needing to set up the entire frontend.
