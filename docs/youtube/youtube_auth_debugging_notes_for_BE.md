# YouTube Auth: Frontend findings and what Backend should verify

This note summarizes what we changed in the FE, what we observed, and what we need from the BE to finalize the YouTube login/signup/connect flows.

## TL;DR

- The 401 originally seen by FE was from the FE route GET /api/auth/token because the user did not yet have the HttpOnly cookie (Bragging<PERSON><PERSON><PERSON>Token).
- We updated the FE flow so that:
  - login/signup send the Google code to FE API routes, which call Rails to authenticate and then set the HttpOnly cookie.
  - connect uses an existing FE cookie to obtain a Devise access token and calls Rails /api/v1/youtube_auth/connect with Authorization: Bearer.
- Current BE error: POST /api/v1/auth/youtube/login returns 404/route missing in production. The FE expects this endpoint to exist for YouTube-based login (and similarly /api/v1/auth/youtube/signup for signup).

## What the FE now does

1. Callback page parses state.mode in { connect | login | signup }.

2. For login/signup:

   - FE Next.js API routes POST /api/auth/youtube/login and /api/auth/youtube/signup forward the code to <PERSON><PERSON>:
     - POST `${BACK_END_URL}/api/v1/auth/youtube/login`
     - POST `${BACK_END_URL}/api/v1/auth/youtube/signup`
   - On success, FE creates an HttpOnly cookie with a JWT that embeds the Devise access token and refresh token.

3. For connect:

   - FE uses GET /api/auth/token to get a current access token (or refresh via /users/tokens/refresh).
   - FE calls Rails POST `${NEXT_PUBLIC_BACK_END_URL}/api/v1/youtube_auth/connect` with Authorization: Bearer <Devise access token> and body: { code, redirect_uri, client_id }.

4. Feature toggle: the FE follows a `youtubeConnect` toggle. For debugging we temporarily commented out the blocking check inside the FE API routes; callback page still checks feature flag for UI.

## What we’re seeing now in BE prod logs

```
Started POST "/api/v1/auth/youtube/login" ...
ActionController::RoutingError (No route matches [POST] "/api/v1/auth/youtube/login")
```

This indicates the BE does not define routes for YouTube login (and likely signup) under /api/v1/auth/....

## What the BE should verify/adjust

1. Routes that FE expects for YouTube login/signup

   - POST /api/v1/auth/youtube/login (returns token payload compatible with FE TokenResponse)
   - POST /api/v1/auth/youtube/signup (same)

   If your intended endpoints differ, please share the correct paths and required bodies so FE can update.

2. Token shape returned by login/signup
   FE builds the HttpOnly cookie from the Rails token response. Expected keys (based on existing FE integrations):

   - token (Devise API access token)
   - refresh_token
   - expires_in (seconds)
   - resource_owner: { id, email }
   - admin (optional boolean)

3. Connect endpoint behavior

   - POST /api/v1/youtube_auth/connect requires Authorization: Bearer <Devise access token> (from cookie).
   - It validates body contains: code, redirect_uri, client_id.

4. Redirect URI and client id

   - FE sends redirect_uri = NEXT_PUBLIC_YOUTUBE_REDIRECT_URI and client_id = NEXT_PUBLIC_YOUTUBE_CLIENT_ID.
   - Please confirm these align with Google OAuth configuration on BE side and env vars GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, FRONTEND_URL, YOUTUBE_API_KEY.

5. Devise token vs a custom JWT
   - The FE stores your token/refresh_token inside a FE-signed JWT cookie for session mgmt.
   - When calling BE, FE always uses Devise access token in Authorization header.

## Helpful cURL for BE sanity checks

- Connect (requires a valid Devise access token):

curl -X POST "$BACK_END_URL/api/v1/youtube_auth/connect" \
 -H "Authorization: Bearer <ACCESS_TOKEN>" \
 -H "Content-Type: application/json" \
 -d '{
"code": "<code>",
"redirect_uri": "<NEXT_PUBLIC_YOUTUBE_REDIRECT_URI>",
"client_id": "<NEXT_PUBLIC_YOUTUBE_CLIENT_ID>"
}'

- Expected login/signup endpoints (please confirm actual):

curl -X POST "$BACK_END_URL/api/v1/auth/youtube/login" \
 -H "Content-Type: application/json" \
 -d '{ "code": "<code>" }'

curl -X POST "$BACK_END_URL/api/v1/auth/youtube/signup" \
 -H "Content-Type: application/json" \
 -d '{ "code": "<code>" }'

## FE logging added for debugging

- Next API routes now log:

  - Target BE URL and BACK_END_URL
  - Status codes
  - Error body (JSON or text) on failure
  - Payload key names on success

- Callback page logs:
  - Code prefix and raw state param
  - Parsed mode and origin

This should help correlate FE actions with BE logs.

## Open questions for BE

- Which exact endpoints should FE use for YouTube login/signup? If different from the above, please specify paths and required request/response shapes.
- Any additional headers BE requires for YouTube login/signup?
- Are redirect_uri/client_id validated against BE envs? If yes, please confirm their values in prod.

## Known risks/edge cases

- If the FE cookie domain/samesite settings need cross-subdomain, we would need domain=.bragrights.football and SameSite=None; Secure. Currently FE uses SameSite=strict for same-origin.
- If refresh_token issuance is conditional in BE, ensure YouTube login/signup flows return refresh_token to allow FE to refresh the session.

## Console logs I got when trying the youtube auth login in prod server

2228-835cc1f54002b5dc.js:1 POST https://bragrights.football/api/auth/youtube/login 404 (Not Found)
i @ 2228-835cc1f54002b5dc.js:1
(anonymous) @ page-2552e39e7b27dcd6.js:1
(anonymous) @ page-2552e39e7b27dcd6.js:1
(anonymous) @ 4bd1b696-cc729d47eba2cee4.js:1
w @ 5964-6d6a41800488ba64.js:1Understand this error
2228-835cc1f54002b5dc.js:1 Error handling YouTube login: Error: Failed to login with YouTube account: 404 - Failed to log in with YouTube
at i (2228-835cc1f54002b5dc.js:1:4697)
at async page-2552e39e7b27dcd6.js:1:7312
overrideMethod @ hook.js:608
i @ 2228-835cc1f54002b5dc.js:1
await in i
(anonymous) @ page-2552e39e7b27dcd6.js:1
(anonymous) @ 4bd1b696-cc729d47eba2cee4.js:1
w @ 5964-6d6a41800488ba64.js:1Understand this error
page-2552e39e7b27dcd6.js:1 Error processing YouTube callback: Error: Failed to login with YouTube account: 404 - Failed to log in with YouTube
at i (2228-835cc1f54002b5dc.js:1:4697)
at async page-2552e39e7b27dcd6.js:1:7312
overrideMethod @ hook.js:608
(anonymous) @ page-2552e39e7b27dcd6.js:1
await in (anonymous)
(anonymous) @ page-2552e39e7b27dcd6.js:1
(anonymous) @ 4bd1b696-cc729d47eba2cee4.js:1
w @ 5964-6d6a41800488ba64.js:1Understand this error

## FE youtube auth file

"use client";

import { getOrRefreshToken } from "@/src/libs/auth/client";
import { getFeatureToggle } from "@/src/utils/feature-toggle";

/\*\*

- Initiates the YouTube OAuth flow by redirecting to the Google authorization page
- @param mode The authentication mode: 'connect', 'signup', or 'login'
  \*/
  export const initiateYoutubeAuth = (
  mode: "connect" | "signup" | "login" = "connect"
  ) => {
  // Check if the YouTube connect feature is enabled
  const youtubeConnectEnabled = getFeatureToggle("youtubeConnect");
  if (!youtubeConnectEnabled) {
  console.error("YouTube connect feature is disabled");
  return;
  }

const clientId = process.env.NEXT_PUBLIC_YOUTUBE_CLIENT_ID;
const redirectUri = process.env.NEXT_PUBLIC_YOUTUBE_REDIRECT_URI;

if (!clientId || !redirectUri) {
throw new Error("YouTube OAuth configuration is missing");
}

// Define required scopes
const scopes = [
"https://www.googleapis.com/auth/youtube.readonly",
"https://www.googleapis.com/auth/userinfo.email",
"https://www.googleapis.com/auth/userinfo.profile",
].join(" ");

// Build the state parameter with mode and origin
const stateObj = {
mode,
origin: window.location.origin,
};

// Build the OAuth URL
const authUrl =
`https://accounts.google.com/o/oauth2/v2/auth?` +
`client_id=${clientId}` +
`&redirect_uri=${encodeURIComponent(redirectUri)}` +
`&response_type=code` +
`&scope=${encodeURIComponent(scopes)}` +
`&access_type=offline` +
`&prompt=consent` +
`&state=${encodeURIComponent(JSON.stringify(stateObj))}`;

// Redirect to the OAuth URL
window.location.href = authUrl;
};

/\*\*

- Handles the YouTube OAuth callback by exchanging the code for tokens
- @param code The authorization code from the OAuth callback
- @param mode The authentication mode: 'connect', 'signup', or 'login'
- @returns The response from the backend
  \*/
  export const handleYoutubeCallback = async (
  code: string,
  mode: "connect" | "signup" | "login" = "connect"
  ) => {
  // Check if the YouTube connect feature is enabled
  const youtubeConnectEnabled = getFeatureToggle("youtubeConnect");
  if (!youtubeConnectEnabled) {
  throw new Error("YouTube connect feature is disabled");
  }

try {
// For login and signup, call our Next.js API routes that set the HttpOnly cookie
if (mode === "login" || mode === "signup") {
const apiPath =
mode === "login"
? "/api/auth/youtube/login"
: "/api/auth/youtube/signup";

      const response = await fetch(apiPath, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "same-origin",
        body: JSON.stringify({ code }),
      });

      if (!response.ok) {
        let errorMessage = `Failed to ${mode} with YouTube account: ${response.status}`;
        try {
          const errorData = await response.json();
          if (errorData?.error) errorMessage += ` - ${errorData.error}`;
        } catch { }
        throw new Error(errorMessage);
      }

      const data = await response.json();
      return data;
    }

    // connect (requires existing auth token)
    const token = await getOrRefreshToken();
    const endpoint = `${process.env.NEXT_PUBLIC_BACK_END_URL}/api/v1/youtube_auth/connect`;
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    };

    // Include exactly the parameters shown in the example documentation
    const redirectUri = process.env.NEXT_PUBLIC_YOUTUBE_REDIRECT_URI;
    const clientId = process.env.NEXT_PUBLIC_YOUTUBE_CLIENT_ID;

    // Prepare request data with only the required parameters
    const requestData = {
      code,
      redirect_uri: redirectUri,
      client_id: clientId,
    };

    const response = await fetch(endpoint, {
      method: "POST",
      headers,
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      // Try to get more detailed error information
      let errorMessage = `Failed to ${mode} with YouTube account: ${response.status}`;
      let errorData;

      try {
        errorData = await response.json();
        console.error("Error response:", errorData);

        // Special handling for 409 Conflict (YouTube channel already connected)
        if (
          response.status === 409 &&
          errorData.error &&
          errorData.error.includes("already connected to another account")
        ) {
          // This is a special case we want to handle differently in the UI
          // Pass the entire error object so we can extract the email in the UI
          throw {
            status: 409,
            message: errorData.error,
            details: errorData.details,
            type: "youtube_already_connected",
            connectedEmail: errorData.error.match(/\(([^)]+)\)/)?.[1] || null,
          };
        }

        // Regular error handling
        if (errorData.error_description) {
          errorMessage += ` - ${Array.isArray(errorData.error_description)
            ? errorData.error_description.join(", ")
            : errorData.error_description
            }`;
        } else if (errorData.error) {
          errorMessage += ` - ${errorData.error}`;
        }
      } catch (e) {
        if (
          typeof e === "object" &&
          e !== null &&
          "type" in e &&
          (e as any).type === "youtube_already_connected"
        ) {
          // Re-throw our special error object
          throw e;
        }
      }

      throw new Error(errorMessage);
    }

    const data = await response.json();
    return data;

} catch (error) {
console.error(`Error handling YouTube ${mode}:`, error);
throw error;
}
};
