# Frontend YouTube OAuth Implementation Example

This document provides a complete example of implementing the YouTube OAuth flow in the frontend application.

## 1. Environment Setup

First, ensure you have the necessary environment variables in your `.env.local` file:

```
NEXT_PUBLIC_YOUTUBE_CLIENT_ID=your_client_id
NEXT_PUBLIC_YOUTUBE_REDIRECT_URI=http://localhost:3000/auth/youtube/callback
NEXT_PUBLIC_API_URL=http://localhost:3010
```

## 2. YouTube Auth Service

Create a service to handle YouTube authentication:

```typescript
// services/youtubeAuthService.ts
import { getCookie } from 'cookies-next';

export const youtubeAuthService = {
  // Initiate the OAuth flow
  initiateAuth: () => {
    const clientId = process.env.NEXT_PUBLIC_YOUTUBE_CLIENT_ID;
    const redirectUri = process.env.NEXT_PUBLIC_YOUTUBE_REDIRECT_URI;

    // Define required scopes
    const scopes = [
      "https://www.googleapis.com/auth/youtube.readonly",
      "https://www.googleapis.com/auth/userinfo.email",
      "https://www.googleapis.com/auth/userinfo.profile",
    ].join(" ");

    // Build the OAuth URL
    const authUrl =
      `https://accounts.google.com/o/oauth2/v2/auth?` +
      `client_id=${clientId}` +
      `&redirect_uri=${encodeURIComponent(redirectUri)}` +
      `&response_type=code` +
      `&scope=${encodeURIComponent(scopes)}` +
      `&access_type=offline` +
      `&prompt=consent`;

    // Redirect to the OAuth URL
    window.location.href = authUrl;
  },

  // Exchange the code for tokens
  handleCallback: async (code: string) => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/youtube_auth/connect`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getCookie("auth_token")}`,
          },
          body: JSON.stringify({
            code,
            redirect_uri: process.env.NEXT_PUBLIC_YOUTUBE_REDIRECT_URI,
            client_id: process.env.NEXT_PUBLIC_YOUTUBE_CLIENT_ID,
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to connect YouTube account');
      }

      return await response.json();
    } catch (error) {
      console.error("Error handling YouTube callback:", error);
      throw error;
    }
  },

  // Get YouTube connection status
  getStatus: async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/youtube_auth/status`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${getCookie("auth_token")}`,
          },
        }
      );

      return await response.json();
    } catch (error) {
      console.error("Error getting YouTube status:", error);
      throw error;
    }
  },

  // Disconnect YouTube account
  disconnect: async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/youtube_auth/disconnect`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${getCookie("auth_token")}`,
          },
        }
      );

      return await response.json();
    } catch (error) {
      console.error("Error disconnecting YouTube account:", error);
      throw error;
    }
  },
};
```

## 3. Connect Button Component

Create a component for the YouTube connect button:

```tsx
// components/YouTubeConnectButton.tsx
import { useState } from 'react';
import { youtubeAuthService } from '../services/youtubeAuthService';

export const YouTubeConnectButton = () => {
  const [isLoading, setIsLoading] = useState(false);

  const handleConnect = async () => {
    setIsLoading(true);
    try {
      youtubeAuthService.initiateAuth();
    } catch (error) {
      console.error('Error initiating YouTube auth:', error);
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleConnect}
      disabled={isLoading}
      className="btn btn-primary"
    >
      {isLoading ? 'Connecting...' : 'Connect YouTube Account'}
    </button>
  );
};
```

## 4. Callback Handler Page

Create a page to handle the OAuth callback:

```tsx
// pages/auth/youtube/callback.tsx
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { youtubeAuthService } from '../../../services/youtubeAuthService';

export default function YouTubeCallback() {
  const router = useRouter();
  const [status, setStatus] = useState('Processing YouTube authentication...');

  useEffect(() => {
    const processCallback = async () => {
      try {
        // Get the code from URL query parameters
        const { code } = router.query;

        if (!code || typeof code !== 'string') {
          setStatus('Error: No authorization code received');
          return;
        }

        // Process the code
        const result = await youtubeAuthService.handleCallback(code);
        
        // Success - redirect to profile or previous page
        setStatus('YouTube account connected successfully!');
        setTimeout(() => {
          router.push('/profile?youtube_connected=true');
        }, 2000);
      } catch (error) {
        console.error('Error processing YouTube callback:', error);
        setStatus(`Error connecting YouTube account: ${error.message || 'Unknown error'}`);
      }
    };

    if (router.isReady) {
      processCallback();
    }
  }, [router.isReady, router.query, router]);

  return (
    <div className="container mt-5">
      <div className="row justify-content-center">
        <div className="col-md-6">
          <div className="card">
            <div className="card-body text-center">
              <h3>YouTube Authentication</h3>
              <p>{status}</p>
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
```

## 5. Profile Page with YouTube Status

Update your profile page to show YouTube connection status:

```tsx
// pages/profile.tsx
import { useEffect, useState } from 'react';
import { YouTubeConnectButton } from '../components/YouTubeConnectButton';
import { youtubeAuthService } from '../services/youtubeAuthService';

export default function Profile() {
  const [youtubeStatus, setYoutubeStatus] = useState({
    connected: false,
    channel_name: '',
    channel_id: '',
    avatar_url: '',
    is_content_creator: false,
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchYouTubeStatus = async () => {
      try {
        const status = await youtubeAuthService.getStatus();
        setYoutubeStatus(status);
      } catch (error) {
        console.error('Error fetching YouTube status:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchYouTubeStatus();
  }, []);

  const handleDisconnect = async () => {
    try {
      await youtubeAuthService.disconnect();
      setYoutubeStatus({
        connected: false,
        channel_name: '',
        channel_id: '',
        avatar_url: '',
        is_content_creator: false,
      });
    } catch (error) {
      console.error('Error disconnecting YouTube account:', error);
    }
  };

  return (
    <div className="container mt-5">
      <div className="row">
        <div className="col-md-6">
          <h2>Profile</h2>
          {/* Other profile content */}
        </div>
        <div className="col-md-6">
          <div className="card">
            <div className="card-header">
              <h3>YouTube Integration</h3>
            </div>
            <div className="card-body">
              {isLoading ? (
                <p>Loading YouTube status...</p>
              ) : youtubeStatus.connected ? (
                <div>
                  <div className="d-flex align-items-center mb-3">
                    {youtubeStatus.avatar_url && (
                      <img
                        src={youtubeStatus.avatar_url}
                        alt="YouTube Avatar"
                        className="rounded-circle me-3"
                        width="50"
                        height="50"
                      />
                    )}
                    <div>
                      <h4>{youtubeStatus.channel_name}</h4>
                      <p className="text-muted">Channel ID: {youtubeStatus.channel_id}</p>
                    </div>
                  </div>
                  {youtubeStatus.is_content_creator && (
                    <div className="alert alert-success">
                      <strong>Content Creator Status:</strong> Verified
                    </div>
                  )}
                  <button
                    onClick={handleDisconnect}
                    className="btn btn-outline-danger"
                  >
                    Disconnect YouTube Account
                  </button>
                </div>
              ) : (
                <div>
                  <p>Connect your YouTube account to join subscriber-only leagues.</p>
                  <YouTubeConnectButton />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
```

## 6. Troubleshooting

If you encounter issues with the YouTube OAuth flow, check the following:

1. **Environment Variables**: Ensure all environment variables are correctly set.

2. **Network Requests**: Use browser developer tools to inspect the network requests and responses.

3. **Backend Logs**: Check the Rails server logs for any errors during the OAuth process.

4. **Common Errors**:
   - "Missing required parameters": Check that you're sending all required parameters.
   - "Invalid grant": Authorization codes expire quickly; ensure you're using them immediately.
   - "Invalid client": Verify your client ID and redirect URI match what's registered in Google Cloud Console.
