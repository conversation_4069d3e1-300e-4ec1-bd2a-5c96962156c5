# Content Creator Mode Error Handling Guide

## Overview

This guide explains how the BragRights system handles failures when a user attempts to enable content creator mode. It covers the various failure scenarios, how they're handled in the backend, how they're communicated to the frontend, and best practices for handling these errors in the user interface.

## Content Creator Mode: Prerequisites

Before a user can enable content creator mode, they must:

1. Have a registered account on BragRights
2. Have connected their YouTube account through the OAuth flow
3. Have a valid YouTube channel associated with their account

## Failure Scenarios and Handling

### 1. YouTube Account Not Connected

**Scenario**: User attempts to enable content creator mode without first connecting their YouTube account.

**Backend Handling**:

- The `update_creator_status` endpoint in `YoutubeController` checks if the user has a connected YouTube account using `current_devise_api_user.youtube_connected?`
- If not connected, it returns a 400 Bad Request response with the error message: "YouTube account not connected"

```ruby
# app/controllers/api/v1/youtube_controller.rb
def update_creator_status
  # Only allow users with connected YouTube accounts
  unless current_devise_api_user.youtube_connected?
    render json: { error: 'YouTube account not connected' }, status: :bad_request
    return
  end
  # ...
end
```

**Frontend Handling**:

- The frontend should catch this error and display a message explaining that the user needs to connect their YouTube account first
- Provide a direct link or button to initiate the YouTube connection flow
- After successful connection, allow the user to try enabling content creator mode again

**Example Frontend Implementation**:

```javascript
const enableContentCreatorMode = async () => {
  try {
    await youtubeService.updateCreatorStatus(true);
    // Success handling
    showSuccessMessage("Content creator mode enabled successfully!");
    updateUserState({ isContentCreator: true });
  } catch (error) {
    if (error.response?.data?.error === "YouTube account not connected") {
      // Show specific error with action button
      showErrorWithAction(
        "You need to connect your YouTube account first",
        "Connect YouTube",
        initiateYoutubeAuth
      );
    } else {
      // Generic error handling
      showErrorMessage(
        "Failed to enable content creator mode. Please try again."
      );
    }
  }
};
```

### 2. Database Update Failure

**Scenario**: The system fails to update the user's `is_content_creator` flag in the database.

**Backend Handling**:

- If the `update` method fails, it returns a 422 Unprocessable Entity response with the error message: "Failed to update content creator status"
- The system logs the error but doesn't provide detailed error information to the frontend

```ruby
# app/controllers/api/v1/youtube_controller.rb
def update_creator_status
  # ...
  # Update the user's content creator status
  if current_devise_api_user.update(is_content_creator: params[:is_content_creator] == 'true')
    render json: {
      message: 'Content creator status updated successfully',
      is_content_creator: current_devise_api_user.is_content_creator
    }, status: :ok
  else
    render json: { error: 'Failed to update content creator status' }, status: :unprocessable_entity
  end
end
```

**Frontend Handling**:

- Display a generic error message about the failure
- Provide a retry option
- If the problem persists, suggest contacting support

**Example Frontend Implementation**:

```javascript
const enableContentCreatorMode = async () => {
  try {
    await youtubeService.updateCreatorStatus(true);
    // Success handling
  } catch (error) {
    if (error.response?.status === 422) {
      // Database update failure
      showErrorWithRetry(
        "We couldn't enable content creator mode at this time. Please try again.",
        () => enableContentCreatorMode()
      );
    } else {
      // Other error handling
    }
  }
};
```

### 3. YouTube Token Issues

**Scenario**: The user's YouTube tokens are expired, invalid, or revoked when they try to enable content creator mode.

**Backend Handling**:

- The `youtube_connected?` method checks if the user has YouTube credentials stored
- If credentials exist but are invalid, this would only be discovered when trying to use them (not during the content creator mode toggle)
- The `YoutubeTokenService` handles token refresh and revocation, but these aren't directly tied to enabling content creator mode
- If tokens are invalid, the user might successfully enable content creator mode but fail later when trying to use YouTube-specific features

**Frontend Handling**:

- If token issues are detected during other operations, prompt the user to reconnect their YouTube account
- Provide clear messaging about why reconnection is necessary
- After reconnection, allow the user to retry enabling content creator mode

**Example Frontend Implementation**:

```javascript
// This would be part of a more general error handler for YouTube API operations
const handleYoutubeApiError = (error) => {
  if (
    error.response?.status === 401 ||
    error.response?.data?.error?.includes("token")
  ) {
    // Token issue detected
    showErrorWithAction(
      "Your YouTube connection has expired. Please reconnect your account.",
      "Reconnect YouTube",
      initiateYoutubeAuth
    );

    // Update user state to reflect disconnected status
    updateUserState({
      youtubeConnected: false,
      isContentCreator: false,
    });
  }
};
```

### 4. Network or Server Errors

**Scenario**: General network failures or server errors when trying to enable content creator mode.

**Backend Handling**:

- Standard HTTP error responses (500, 503, etc.) for server errors
- Comprehensive logging of errors for troubleshooting

**Frontend Handling**:

- Display appropriate error messages based on the type of error
- Provide retry options for transient errors
- For persistent errors, suggest trying again later or contacting support

**Example Frontend Implementation**:

```javascript
const enableContentCreatorMode = async () => {
  try {
    await youtubeService.updateCreatorStatus(true);
    // Success handling
  } catch (error) {
    if (error.response?.status >= 500) {
      // Server error
      showErrorWithRetry(
        "We're experiencing server issues. Please try again later.",
        () => enableContentCreatorMode()
      );
    } else if (!error.response && error.request) {
      // Network error
      showErrorWithRetry(
        "Network error. Please check your connection and try again.",
        () => enableContentCreatorMode()
      );
    } else {
      // Other error handling
    }
  }
};
```

### 5. Rate Limiting

**Scenario**: User attempts to toggle content creator mode too many times within a short period.

**Backend Handling**:

- Limits users to 5 attempts per hour
- Returns a 429 Too Many Requests status with retry information
- Uses Redis cache to track attempts

**Frontend Handling**:

- Display a message indicating the user needs to wait
- Show remaining time before next attempt is allowed
- Disable the toggle button during the cooldown period

**Example Frontend Implementation**:

```javascript
const enableContentCreatorMode = async () => {
  try {
    await youtubeService.updateCreatorStatus(true);
    showSuccessMessage("Content creator mode enabled successfully!");
    updateUserState({ isContentCreator: true });
  } catch (error) {
    if (error.response?.status === 429) {
      const retryAfter = error.response.data.retry_after;
      showRateLimitError(
        `Too many attempts. Please try again in ${Math.ceil(
          retryAfter / 60
        )} minutes.`
      );
      disableCreatorToggle(retryAfter);
    } else {
      handleOtherErrors(error);
    }
  }
};
```

## Best Practices for Frontend Error Handling

### 1. Clear and Actionable Error Messages

- Provide specific error messages that explain what went wrong
- Include clear instructions on how to resolve the issue
- Use non-technical language that all users can understand

### 2. Visual Feedback

- Use appropriate visual cues (icons, colors) to indicate errors
- Ensure error states are visually distinct from normal states
- Consider using toast notifications for transient errors and inline messages for form-related errors

### 3. Progressive Disclosure

- Start with simple error messages
- Provide "Learn more" or expandable sections for detailed troubleshooting
- Link to relevant documentation for complex issues

### 4. Graceful Degradation

- Disable YouTube-specific features when errors occur, but keep the rest of the application functional
- Provide alternative paths to achieve goals when possible
- Cache previous state to prevent data loss during errors

### 5. Retry Mechanisms

- Offer retry buttons for transient errors
- Implement exponential backoff for automatic retries
- Limit the number of retries to prevent excessive API calls

## Logging and Monitoring

The system includes comprehensive logging to help diagnose issues with content creator mode:

### Backend Logging

- The `YoutubeController` logs detailed information about content creator status updates
- The `User` model logs YouTube account operations
- The `YoutubeTokenService` logs token management operations

### Viewing Logs

To view the logs in development:

```bash
tail -f log/development.log | grep "content creator"
```

To view the logs in production:

```bash
tail -f log/production.log | grep "content creator"
```

## Troubleshooting Guide for Support Teams

### Common Issues and Solutions

1. **User can't enable content creator mode**:

   - Check if the user has connected their YouTube account
   - Verify that the YouTube connection is still valid
   - Check for database constraints or validation errors

2. **Content creator mode enabled but features not working**:

   - Verify that the user's YouTube tokens are still valid
   - Check if the user's YouTube channel meets eligibility requirements
   - Look for API quota issues or rate limiting

3. **Intermittent failures when enabling content creator mode**:
   - Check for network connectivity issues
   - Look for database locking or concurrency issues
   - Verify that all required services are operational

## Future Improvements

1. **Token Validation Before Enabling**: Consider validating the YouTube tokens before allowing a user to enable content creator mode to ensure they're still valid.

2. **Automatic Token Refresh**: Implement automatic token refresh when enabling content creator mode to ensure the user has valid tokens.

3. **More Detailed Error Messages**: Provide more specific error messages that explain exactly what went wrong and how to fix it.

4. **Eligibility Pre-check**: Check if the user meets all requirements for subscriber-only leagues before allowing them to enable content creator mode.

5. **Comprehensive Error Codes**: Implement a system of error codes for more precise error handling and troubleshooting.

## Conclusion

Proper error handling for content creator mode is essential for providing a smooth user experience. By implementing the strategies outlined in this guide, you can ensure that users understand what went wrong and how to resolve issues when they occur.

Remember that good error handling not only prevents user frustration but also reduces support tickets and improves overall platform adoption.
