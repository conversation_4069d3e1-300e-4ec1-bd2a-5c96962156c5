# Frontend Implementation Guide for YouTube Integration

This guide outlines the technical steps needed to implement YouTube integration with subscriber-only leagues in the frontend application. The backend has already been set up with the necessary endpoints and functionality.

## Key Concepts

1. **YouTube Account Connection**:

   - Any user should be able to connect their BragRights account to their YouTube account
   - This connection allows users to join subscriber-only leagues if they are subscribed to the league creator's channel
   - The connection process uses OAuth to securely authenticate with YouTube

2. **Content Creator Mode**:

   - Users with connected YouTube accounts can enable "Content Creator Mode"
   - This identifies them as YouTube content creators in the platform

3. **Subscriber-Only Leagues**:
   - Any YouTube-verified user who meets the minimum subscriber requirements can request to create a subscriber-only league
   - The minimum subscriber requirement is configurable (default: 100 subscribers)
   - The platform verifies eligibility before allowing creation of subscriber-only leagues
   - Users must be subscribed to the creator's channel to join these leagues

> **Note:** This implementation focuses on **subscriber-only** leagues (requiring the free "Subscribe" action) rather than **members-only** leagues (requiring paid channel memberships). Members-only functionality may be considered as a future enhancement.

## 1. Environment Setup

First, ensure your Next.js application has the necessary environment variables:

```javascript
// .env.local file or environment configuration
NEXT_PUBLIC_API_URL=http://api.bragrights.football
NEXT_PUBLIC_YOUTUBE_CLIENT_ID=your_youtube_client_id
NEXT_PUBLIC_YOUTUBE_REDIRECT_URI=https://bragrights.football/auth/youtube/callback
```

For different environments, you can use `.env.development`, `.env.production`, etc. Remember that only variables prefixed with `NEXT_PUBLIC_` will be available on the client side.

## 2. API Service Implementation

Create a YouTube service module to handle API interactions. For Next.js, we'll create an API service that works with both client and server components:

```typescript
// src/services/youtubeService.ts
import axios from "axios";
import { getCookie } from "cookies-next";

const API_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://api.bragrights.football";

// Create a configured axios instance
const createApiClient = (serverSide = false) => {
  const client = axios.create({
    baseURL: API_URL,
    headers: {
      "Content-Type": "application/json",
    },
  });

  // Add auth token to requests
  client.interceptors.request.use(async (config) => {
    // For client-side requests
    if (!serverSide) {
      const token = getCookie("auth_token");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  });

  return client;
};

// Client-side API functions
export const youtubeService = {
  // Connect YouTube account
  connectAccount: async (authData: any) => {
    const api = createApiClient();
    return api.post("/api/v1/youtube_auth/connect", authData);
  },

  // Disconnect YouTube account
  disconnectAccount: async () => {
    const api = createApiClient();
    return api.post("/api/v1/youtube_auth/disconnect");
  },

  // Get YouTube connection status
  getStatus: async () => {
    const api = createApiClient();
    return api.get("/api/v1/youtube_auth/status");
  },

  // Verify subscription to a channel
  verifySubscription: async (channelId: string) => {
    const api = createApiClient();
    return api.post("/api/v1/youtube_auth/verify_subscription", {
      channel_id: channelId,
    });
  },

  // Get subscription status for a channel
  getSubscriptionStatus: async (channelId: string) => {
    const api = createApiClient();
    return api.get(
      `/api/v1/youtube/subscription_status?channel_id=${channelId}`
    );
  },

  // Get YouTube leagues
  getYoutubeLeagues: async (params = {}) => {
    const api = createApiClient();
    return api.get("/api/v1/youtube/leagues", { params });
  },

  // Update content creator status
  updateCreatorStatus: async (isContentCreator: boolean) => {
    const api = createApiClient();
    return api.post("/api/v1/youtube/update_creator_status", {
      is_content_creator: isContentCreator,
    });
  },

  // Check if user is eligible to create subscriber-only leagues
  checkSubscriberLeagueEligibility: async () => {
    const api = createApiClient();
    return api.get("/api/v1/youtube/check_subscriber_league_eligibility");
  },
};

// Server-side API functions (for use in Server Components or API routes)
export const serverBragRightsYouTubeService = {
  // Get YouTube leagues (server-side)
  getYoutubeLeagues: async (params = {}, authToken: string) => {
    const api = createApiClient(true);
    return api.get("/api/v1/youtube/leagues", {
      params,
      headers: { Authorization: `Bearer ${authToken}` },
    });
  },

  // Add other server-side methods as needed
};
```

## 3. YouTube OAuth Integration

Implement the OAuth flow for connecting YouTube accounts in Next.js:

```typescript
// src/utils/youtubeAuth.ts
import { getCookie } from "cookies-next";

export const initiateYoutubeAuth = () => {
  const clientId = process.env.NEXT_PUBLIC_YOUTUBE_CLIENT_ID;
  const redirectUri = process.env.NEXT_PUBLIC_YOUTUBE_REDIRECT_URI;

  // Define required scopes
  const scopes = [
    "https://www.googleapis.com/auth/youtube.readonly",
    "https://www.googleapis.com/auth/userinfo.email",
    "https://www.googleapis.com/auth/userinfo.profile",
  ].join(" ");

  // Build the OAuth URL
  const authUrl =
    `https://accounts.google.com/o/oauth2/v2/auth?` +
    `client_id=${clientId}` +
    `&redirect_uri=${encodeURIComponent(redirectUri)}` +
    `&response_type=code` +
    `&scope=${encodeURIComponent(scopes)}` +
    `&access_type=offline` +
    `&prompt=consent`;

  // Redirect to the OAuth URL
  window.location.href = authUrl;
};

// Handle the OAuth callback
export const handleYoutubeCallback = async (code: string) => {
  try {
    // Exchange the code for tokens using your backend endpoint
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/v1/youtube_auth/connect`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${getCookie("auth_token")}`,
        },
        body: JSON.stringify({ code }),
      }
    );

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error handling YouTube callback:", error);
    throw error;
  }
};

// Next.js API route to handle the OAuth callback
// pages/api/auth/youtube/callback.ts
import type { NextApiRequest, NextApiResponse } from "next";
import { getCookie } from "cookies-next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { code } = req.query;

  if (!code || typeof code !== "string") {
    return res.status(400).json({ error: "Missing authorization code" });
  }

  try {
    // Get the auth token from cookies
    const token = getCookie("auth_token", { req, res });

    if (!token) {
      return res.status(401).json({ error: "Not authenticated" });
    }

    // Exchange the code for tokens
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/v1/youtube_auth/connect`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ code }),
      }
    );

    const data = await response.json();

    // Redirect to the profile page or a success page
    return res.redirect(307, "/profile?youtube_connected=true");
  } catch (error) {
    console.error("Error handling YouTube callback:", error);
    return res.redirect(307, "/profile?youtube_error=true");
  }
}
```

## 4. Redux Integration (if using Redux)

Create actions and reducers for YouTube state management:

```javascript
// src/redux/youtube/actions.js
import { youtubeService } from "../../services/youtubeService";

export const YOUTUBE_ACTIONS = {
  CONNECT_REQUEST: "YOUTUBE_CONNECT_REQUEST",
  CONNECT_SUCCESS: "YOUTUBE_CONNECT_SUCCESS",
  CONNECT_FAILURE: "YOUTUBE_CONNECT_FAILURE",
  DISCONNECT_REQUEST: "YOUTUBE_DISCONNECT_REQUEST",
  DISCONNECT_SUCCESS: "YOUTUBE_DISCONNECT_SUCCESS",
  DISCONNECT_FAILURE: "YOUTUBE_DISCONNECT_FAILURE",
  GET_STATUS_REQUEST: "YOUTUBE_GET_STATUS_REQUEST",
  GET_STATUS_SUCCESS: "YOUTUBE_GET_STATUS_SUCCESS",
  GET_STATUS_FAILURE: "YOUTUBE_GET_STATUS_FAILURE",
  VERIFY_SUBSCRIPTION_REQUEST: "YOUTUBE_VERIFY_SUBSCRIPTION_REQUEST",
  VERIFY_SUBSCRIPTION_SUCCESS: "YOUTUBE_VERIFY_SUBSCRIPTION_SUCCESS",
  VERIFY_SUBSCRIPTION_FAILURE: "YOUTUBE_VERIFY_SUBSCRIPTION_FAILURE",
};

// Connect YouTube account
export const connectYoutubeAccount = (authData) => async (dispatch) => {
  dispatch({ type: YOUTUBE_ACTIONS.CONNECT_REQUEST });

  try {
    const response = await youtubeService.connectAccount(authData);
    dispatch({
      type: YOUTUBE_ACTIONS.CONNECT_SUCCESS,
      payload: response.data,
    });
    return response.data;
  } catch (error) {
    dispatch({
      type: YOUTUBE_ACTIONS.CONNECT_FAILURE,
      payload:
        error.response?.data?.error || "Failed to connect YouTube account",
    });
    throw error;
  }
};

// Additional action creators for other YouTube operations...
```

```javascript
// src/redux/youtube/reducer.js
import { YOUTUBE_ACTIONS } from "./actions";

const initialState = {
  connected: false,
  isContentCreator: false,
  channelId: null,
  channelName: null,
  loading: false,
  error: null,
  subscriptionStatus: null,
};

export const youtubeReducer = (state = initialState, action) => {
  switch (action.type) {
    case YOUTUBE_ACTIONS.CONNECT_REQUEST:
    case YOUTUBE_ACTIONS.DISCONNECT_REQUEST:
    case YOUTUBE_ACTIONS.GET_STATUS_REQUEST:
    case YOUTUBE_ACTIONS.VERIFY_SUBSCRIPTION_REQUEST:
      return {
        ...state,
        loading: true,
        error: null,
      };

    case YOUTUBE_ACTIONS.CONNECT_SUCCESS:
      return {
        ...state,
        connected: true,
        channelId: action.payload.user.youtube_channel_id,
        channelName: action.payload.user.youtube_channel_name,
        isContentCreator: action.payload.user.is_content_creator,
        loading: false,
      };

    case YOUTUBE_ACTIONS.DISCONNECT_SUCCESS:
      return {
        ...state,
        connected: false,
        channelId: null,
        channelName: null,
        isContentCreator: false,
        loading: false,
      };

    case YOUTUBE_ACTIONS.GET_STATUS_SUCCESS:
      return {
        ...state,
        connected: action.payload.connected,
        channelId: action.payload.channel_id,
        channelName: action.payload.channel_name,
        isContentCreator: action.payload.is_content_creator,
        loading: false,
      };

    case YOUTUBE_ACTIONS.VERIFY_SUBSCRIPTION_SUCCESS:
      return {
        ...state,
        subscriptionStatus: {
          connected: action.payload.connected,
          subscribed: action.payload.subscribed,
        },
        loading: false,
      };

    case YOUTUBE_ACTIONS.CONNECT_FAILURE:
    case YOUTUBE_ACTIONS.DISCONNECT_FAILURE:
    case YOUTUBE_ACTIONS.GET_STATUS_FAILURE:
    case YOUTUBE_ACTIONS.VERIFY_SUBSCRIPTION_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.payload,
      };

    default:
      return state;
  }
};
```

## 5. League Creation with YouTube Integration

Extend your existing league creation functionality:

```javascript
// src/services/leagueService.js
import api from "./api";

export const createLeague = async (leagueData) => {
  try {
    const response = await api.post("/api/v1/leagues", { league: leagueData });
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Additional league-related functions...
```

## 6. Handling Subscription Verification

Implement subscription verification when joining a league:

```javascript
// src/services/leagueMembershipService.js
import api from "./api";
import { youtubeService } from "./youtubeService";

export const joinLeague = async (leagueId) => {
  try {
    const response = await api.post(`/api/v1/leagues/${leagueId}/memberships`);
    return response.data;
  } catch (error) {
    // Handle subscription-required errors
    if (
      error.response?.status === 403 &&
      error.response?.data?.youtube_channel_id
    ) {
      // Store the channel ID for subscription flow
      const channelId = error.response.data.youtube_channel_id;
      throw {
        ...error,
        requiresSubscription: true,
        channelId,
      };
    }
    throw error;
  }
};

export const verifyAndJoinLeague = async (leagueId, channelId) => {
  // First verify subscription
  const subscriptionStatus = await youtubeService.verifySubscription(channelId);

  if (subscriptionStatus.data.subscribed) {
    // If subscribed, try joining again
    return joinLeague(leagueId);
  } else {
    // Not subscribed, throw error
    throw {
      requiresSubscription: true,
      channelId,
      message: "You must be subscribed to join this league",
    };
  }
};
```

## 7. Handling OAuth Callback

Create a Next.js page to handle the OAuth callback:

```tsx
// app/auth/youtube/callback/page.tsx
"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { handleYoutubeCallback } from "@/utils/youtubeAuth";

export default function YouTubeCallbackPage() {
  const [status, setStatus] = useState("Processing...");
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    const processCallback = async () => {
      try {
        // Get the code from URL query parameters
        const code = searchParams.get("code");

        if (!code) {
          setStatus("Error: No authorization code received");
          return;
        }

        // Process the code
        await handleYoutubeCallback(code);

        // Success - redirect to profile or previous page
        setStatus("YouTube account connected successfully!");
        setTimeout(() => {
          router.push("/profile");
        }, 2000);
      } catch (error) {
        console.error("Error processing YouTube callback:", error);
        setStatus("Error connecting YouTube account");
      }
    };

    processCallback();
  }, [searchParams, router]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen py-2">
      <main className="flex flex-col items-center justify-center w-full flex-1 px-20 text-center">
        <h1 className="text-2xl font-bold mb-4">Connecting YouTube Account</h1>
        <p className="text-lg">{status}</p>
      </main>
    </div>
  );
}
```

## 8. League Join Flow with Subscription Check

Implement the flow for joining a members-only league:

```javascript
// src/hooks/useJoinLeague.js
import { useState } from "react";
import {
  joinLeague,
  verifyAndJoinLeague,
} from "../services/leagueMembershipService";
import { youtubeService } from "../services/youtubeService";

export const useJoinLeague = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [subscriptionRequired, setSubscriptionRequired] = useState(false);
  const [channelId, setChannelId] = useState(null);

  const attemptJoinLeague = async (leagueId) => {
    setLoading(true);
    setError(null);
    setSubscriptionRequired(false);

    try {
      await joinLeague(leagueId);
      setLoading(false);
      return true;
    } catch (error) {
      if (error.requiresSubscription) {
        setSubscriptionRequired(true);
        setChannelId(error.channelId);
        setLoading(false);
        return false;
      }

      setError(error.message || "Failed to join league");
      setLoading(false);
      return false;
    }
  };

  const checkSubscriptionStatus = async (channelId) => {
    try {
      const response = await youtubeService.getSubscriptionStatus(channelId);
      return response.data;
    } catch (error) {
      setError("Failed to check subscription status");
      return { connected: false, subscribed: false };
    }
  };

  const connectYouTubeAccount = () => {
    // Redirect to YouTube OAuth flow
    window.location.href = `/api/v1/auth/youtube/callback`;
  };

  return {
    loading,
    error,
    subscriptionRequired,
    channelId,
    attemptJoinLeague,
    checkSubscriptionStatus,
    connectYouTubeAccount,
  };
};
```

## 9. League Creation with YouTube Options

Extend your league creation form to include YouTube options:

```javascript
// src/hooks/useCreateLeague.js
import { useState } from "react";
import { createLeague } from "../services/leagueService";
import { youtubeService } from "../services/youtubeService";

export const useCreateLeague = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [youtubeStatus, setYoutubeStatus] = useState(null);
  const [subscriberLeagueEligibility, setSubscriberLeagueEligibility] =
    useState(null);

  // Check if user is a YouTube creator
  const checkYoutubeStatus = async () => {
    try {
      const response = await youtubeService.getStatus();
      setYoutubeStatus(response.data);
      return response.data;
    } catch (error) {
      setError("Failed to check YouTube status");
      return null;
    }
  };

  // Check if user is eligible to create subscriber-only leagues
  const checkSubscriberLeagueEligibility = async () => {
    try {
      const response = await youtubeService.checkSubscriberLeagueEligibility();
      setSubscriberLeagueEligibility(response.data);
      return response.data;
    } catch (error) {
      setError("Failed to check subscriber league eligibility");
      return null;
    }
  };

  // Create a league with optional YouTube settings
  const submitLeague = async (leagueData) => {
    setLoading(true);
    setError(null);

    try {
      // If this is a subscriber-only league, check eligibility first
      if (leagueData.youtube_league && leagueData.subscriber_only) {
        const eligibility = await checkSubscriberLeagueEligibility();
        if (!eligibility.eligible) {
          setError(eligibility.message);
          setLoading(false);
          throw new Error(eligibility.message);
        }
      }

      const response = await createLeague(leagueData);
      setLoading(false);
      return response;
    } catch (error) {
      setError(
        error.response?.data?.errors ||
          (error.message ? [error.message] : ["Failed to create league"])
      );
      setLoading(false);
      throw error;
    }
  };

  return {
    loading,
    error,
    youtubeStatus,
    subscriberLeagueEligibility,
    checkYoutubeStatus,
    checkSubscriberLeagueEligibility,
    submitLeague,
  };
};
```

## 10. Next.js App Router Configuration

With Next.js App Router, you don't need to manually configure routes. Instead, create the appropriate directory structure:

```
app/
├── auth/
│   └── youtube/
│       └── callback/
│           └── page.tsx  # YouTube OAuth callback page
├── leagues/
│   ├── create/
│   │   └── page.tsx      # League creation page
│   ├── [id]/
│   │   ├── page.tsx      # League details page
│   │   └── join/
│   │       └── page.tsx  # Join league page
└── profile/
    └── page.tsx          # User profile page with YouTube connection options
```

For API routes, create the following structure:

```
app/
└── api/
    └── auth/
        └── youtube/
            └── callback/
                └── route.ts  # API route for handling YouTube OAuth callback
```

## 11. User Flows and Permissions

### YouTube Account Connection Flow

1. **Any User Can Connect Their YouTube Account**:

   - All users should have access to connect their YouTube account from their profile page
   - The connection process should be simple and clearly explained
   - Implement a "Connect YouTube Account" button that initiates the OAuth flow

2. **Content Creator Mode Activation**:

   - After connecting their YouTube account, users should see an option to enable "Content Creator Mode"
   - This should be presented as a toggle or checkbox in their profile settings
   - Explain that this identifies them as a YouTube creator on the platform

3. **Subscriber-Only League Creation**:
   - When creating a league, show YouTube options only to users with connected YouTube accounts
   - For the "Subscriber-Only" option:
     - Check eligibility before allowing selection (`checkSubscriberLeagueEligibility` endpoint)
     - If user is not eligible, show the reason and requirements
     - If user is eligible, allow them to create the subscriber-only league

### Eligibility and Permission Checks

1. **Connecting YouTube Account**:

   ```javascript
   // Any user can connect their YouTube account
   const connectYouTubeAccount = () => {
     initiateYoutubeAuth(); // Redirects to YouTube OAuth flow
   };
   ```

2. **Checking Content Creator Eligibility**:

   ```javascript
   // Check if user can enable content creator mode
   const checkContentCreatorEligibility = async () => {
     const status = await youtubeService.getStatus();
     return status.data.connected; // User only needs a connected account
   };
   ```

3. **Checking Subscriber League Eligibility**:

   ```javascript
   // Check if user can create subscriber-only leagues
   const checkSubscriberLeagueEligibility = async () => {
     const response = await youtubeService.checkSubscriberLeagueEligibility();

     if (response.data.eligible) {
       // User can create subscriber-only leagues
       return {
         canCreate: true,
         message: "You can create subscriber-only leagues",
       };
     } else {
       // User cannot create subscriber-only leagues
       return {
         canCreate: false,
         message: response.data.message,
         subscriberCount: response.data.subscriber_count,
         minRequired: response.data.min_subscribers_required,
       };
     }
   };
   ```

## 12. Technical Implementation Notes

1. **API Key Handling**:

   - The backend handles the YouTube API key (`ENV['YOUTUBE_API_KEY']`), so you don't need to include it in frontend code
   - Only use the Google Client ID in the frontend for OAuth flow

2. **Error Handling**:

   - Implement specific error handling for subscription verification failures
   - Handle YouTube connection errors gracefully
   - Show clear messages when users don't meet eligibility requirements

3. **State Management**:

   - Store YouTube connection status in your state management system
   - Cache subscription status to minimize API calls
   - Store eligibility status to avoid unnecessary API calls

4. **Security Considerations**:

   - Never store YouTube API tokens in localStorage or sessionStorage
   - Use secure HTTP-only cookies for authentication
   - Implement CSRF protection for all API calls

5. **Performance Optimization**:
   - Cache YouTube channel information
   - Implement debouncing for subscription verification checks
   - Check eligibility only when needed (e.g., when showing league creation form)

## 13. User Experience Guidelines

When implementing YouTube integration, follow these guidelines to ensure a smooth user experience:

### 1. Universal YouTube Connection

- **Make YouTube Connection Available to All Users**:
  - Place the YouTube connection option in the user profile settings
  - Use clear, simple language explaining the benefits of connecting
  - Show a prominent "Connect YouTube Account" button
  - After connection, show channel details (name, avatar, subscriber count)

### 2. Clear Eligibility Communication

- **Transparent Requirements**:
  - Clearly communicate the requirements for creating subscriber-only leagues
  - Show the user's current subscriber count alongside the minimum requirement
  - Use visual indicators (icons, colors) to show eligibility status
  - Provide helpful guidance on how to meet requirements

### 3. Progressive Disclosure

- **Reveal Options Gradually**:
  1. First, allow any user to connect their YouTube account
  2. After connection, show the option to enable Content Creator mode
  3. For Content Creators, show YouTube league options during league creation
  4. Only show subscriber-only options to eligible creators

### 4. Error Handling

- **Graceful Failure**:
  - Handle YouTube API errors without breaking the application
  - Provide clear error messages when connection fails
  - Offer retry options and troubleshooting guidance
  - Cache previous state to prevent data loss during errors

### 5. Subscription Verification

- **Smooth Verification Flow**:
  - When a user tries to join a subscriber-only league:
    1. Check if they have a connected YouTube account
    2. If not, prompt them to connect (with a clear explanation why)
    3. After connection, automatically verify subscription
    4. If subscribed, complete the join process
    5. If not subscribed, show a prompt to subscribe with a direct link

## 14. Testing

Implement tests for your YouTube integration using Jest and React Testing Library:

```typescript
// __tests__/services/youtubeService.test.ts
import axios from "axios";
import { youtubeService } from "@/services/youtubeService";
import { getCookie } from "cookies-next";

// Mock axios and cookies
jest.mock("axios");
jest.mock("cookies-next", () => ({
  getCookie: jest.fn(),
}));

const mockedAxios = axios as jest.Mocked<typeof axios>;

describe("YouTube Service", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock axios create to return a mocked instance
    mockedAxios.create.mockReturnValue(mockedAxios);

    // Mock getCookie to return a token
    (getCookie as jest.Mock).mockReturnValue("test-token");
  });

  test("connectAccount should call the correct endpoint", async () => {
    // Arrange
    const mockData = { channel_id: "123", channel_name: "Test Channel" };
    mockedAxios.post.mockResolvedValue({ data: { success: true } });

    // Act
    await youtubeService.connectAccount(mockData);

    // Assert
    expect(mockedAxios.post).toHaveBeenCalledWith(
      "/api/v1/youtube_auth/connect",
      mockData
    );
  });

  test("verifySubscription should call the correct endpoint", async () => {
    // Arrange
    const channelId = "123";
    mockedAxios.post.mockResolvedValue({ data: { subscribed: true } });

    // Act
    await youtubeService.verifySubscription(channelId);

    // Assert
    expect(mockedAxios.post).toHaveBeenCalledWith(
      "/api/v1/youtube_auth/verify_subscription",
      {
        channel_id: channelId,
      }
    );
  });

  // Additional tests...
});

// __tests__/app/auth/youtube/callback/page.test.tsx
import { render, screen, waitFor } from "@testing-library/react";
import { useRouter, useSearchParams } from "next/navigation";
import YouTubeCallbackPage from "@/app/auth/youtube/callback/page";
import { handleYoutubeCallback } from "@/utils/youtubeAuth";

// Mock Next.js hooks and utilities
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

jest.mock("@/utils/youtubeAuth", () => ({
  handleYoutubeCallback: jest.fn(),
}));

describe("YouTubeCallbackPage", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock router
    (useRouter as jest.Mock).mockReturnValue({
      push: jest.fn(),
    });

    // Mock search params
    (useSearchParams as jest.Mock).mockReturnValue({
      get: jest.fn().mockReturnValue("test-code"),
    });
  });

  test("renders loading state initially", () => {
    // Arrange & Act
    render(<YouTubeCallbackPage />);

    // Assert
    expect(screen.getByText("Processing...")).toBeInTheDocument();
  });

  test("handles successful callback", async () => {
    // Arrange
    (handleYoutubeCallback as jest.Mock).mockResolvedValue({ success: true });
    const mockPush = jest.fn();
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    });

    // Act
    render(<YouTubeCallbackPage />);

    // Assert
    await waitFor(() => {
      expect(
        screen.getByText("YouTube account connected successfully!")
      ).toBeInTheDocument();
    });

    // Wait for navigation
    await waitFor(
      () => {
        expect(mockPush).toHaveBeenCalledWith("/profile");
      },
      { timeout: 3000 }
    );
  });

  // Additional tests...
});
```

## 14. UI Components for YouTube Integration

When implementing the YouTube integration in your frontend, consider the following UI components:

### 1. YouTube Connection Button

Create a prominent button in the user profile section:

```jsx
const YouTubeConnectButton = ({ isConnected, onConnect }) => {
  return (
    <button
      className={`youtube-connect-btn ${isConnected ? "connected" : ""}`}
      onClick={onConnect}
      disabled={isConnected}
    >
      {isConnected ? "YouTube Connected ✓" : "Connect YouTube Account"}
    </button>
  );
};
```

### 2. Content Creator Toggle

For users with connected YouTube accounts, show a toggle to enable content creator mode:

```jsx
const ContentCreatorToggle = ({
  isContentCreator,
  onChange,
  isYoutubeConnected,
}) => {
  if (!isYoutubeConnected) return null;

  return (
    <div className="creator-toggle">
      <label>
        <input
          type="checkbox"
          checked={isContentCreator}
          onChange={(e) => onChange(e.target.checked)}
        />
        Enable Content Creator Mode
      </label>
      <p className="helper-text">
        This identifies you as a YouTube creator and allows you to create
        YouTube leagues.
      </p>
    </div>
  );
};
```

### 3. Subscriber League Eligibility Indicator

When creating a league, show eligibility status for subscriber-only leagues:

```jsx
const SubscriberLeagueEligibility = ({ eligibility }) => {
  if (!eligibility) return null;

  return (
    <div
      className={`eligibility-indicator ${
        eligibility.canCreate ? "eligible" : "ineligible"
      }`}
    >
      <h4>Subscriber-Only League Eligibility</h4>
      <p>{eligibility.message}</p>

      {!eligibility.canCreate && (
        <div className="requirements">
          <p>Your channel: {eligibility.subscriberCount} subscribers</p>
          <p>Required: {eligibility.minRequired} subscribers</p>
        </div>
      )}
    </div>
  );
};
```

### 4. League Creation Form with YouTube Options

Extend your league creation form to include YouTube options:

```jsx
const YouTubeLeagueOptions = ({
  isYoutubeConnected,
  isContentCreator,
  eligibility,
  values,
  onChange,
}) => {
  if (!isYoutubeConnected || !isContentCreator) return null;

  const canCreateSubscriberLeague = eligibility?.canCreate;

  return (
    <div className="youtube-league-options">
      <h3>YouTube League Options</h3>

      <label>
        <input
          type="checkbox"
          checked={values.youtubeLeague}
          onChange={(e) =>
            onChange({ ...values, youtubeLeague: e.target.checked })
          }
        />
        Make this a YouTube league
      </label>

      {values.youtubeLeague && (
        <label className={!canCreateSubscriberLeague ? "disabled" : ""}>
          <input
            type="checkbox"
            checked={values.subscriberOnly}
            onChange={(e) =>
              onChange({ ...values, subscriberOnly: e.target.checked })
            }
            disabled={!canCreateSubscriberLeague}
          />
          Subscriber-only league
          {!canCreateSubscriberLeague && (
            <span className="tooltip">{eligibility?.message}</span>
          )}
        </label>
      )}
    </div>
  );
};
```

## 15. API Endpoints Reference

Here's a quick reference of all the backend endpoints available for YouTube integration:

| Endpoint                                              | Method | Description                                 | Parameters                                                                    |
| ----------------------------------------------------- | ------ | ------------------------------------------- | ----------------------------------------------------------------------------- |
| `/api/v1/auth/youtube/callback`                       | GET    | OAuth callback handler                      | `code` (query param)                                                          |
| `/api/v1/youtube_auth/connect`                        | POST   | Connect YouTube account                     | `channel_id`, `channel_name`, `avatar_url`, `subscriber_count`, `credentials` |
| `/api/v1/youtube_auth/disconnect`                     | POST   | Disconnect YouTube account                  | None                                                                          |
| `/api/v1/youtube_auth/status`                         | GET    | Get YouTube connection status               | None                                                                          |
| `/api/v1/youtube_auth/verify_subscription`            | POST   | Verify channel subscription                 | `channel_id`                                                                  |
| `/api/v1/youtube/subscription_status`                 | GET    | Get detailed subscription status            | `channel_id` (query param)                                                    |
| `/api/v1/youtube/leagues`                             | GET    | Get YouTube leagues                         | `user_id`, `subscriber_only` (optional query params)                          |
| `/api/v1/youtube/update_creator_status`               | POST   | Update creator status                       | `is_content_creator`                                                          |
| `/api/v1/youtube/check_subscriber_league_eligibility` | GET    | Check if user can create subscriber leagues | None                                                                          |

The `check_subscriber_league_eligibility` endpoint returns:

```json
{
  "eligible": true|false,
  "message": "You are eligible to create subscriber-only leagues" or "Reason for ineligibility",
  "subscriber_count": 123,
  "min_subscribers_required": 100
}
```

This endpoint is crucial for implementing the requirement that any YouTube-verified user who meets the minimum subscriber count can request to create a subscriber-only league.
