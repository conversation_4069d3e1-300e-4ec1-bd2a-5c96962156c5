# Secure YouTube OAuth Token Storage

This document outlines the implementation of secure token storage for YouTube OAuth integration in the BragRights application.

## Overview

When users connect their YouTube accounts to BragRights, we need to securely store and manage their OAuth tokens. This includes:

1. Encrypting sensitive token data
2. Refreshing expired tokens automatically
3. Properly revoking tokens when users disconnect their accounts
4. Handling token errors gracefully

## Implementation Details

### Token Storage

YouTube OAuth credentials are stored in the `youtube_credentials` field of the User model. This field is encrypted using Rails' Active Record Encryption:

```ruby
# app/models/user.rb
class User < ApplicationRecord
  # Encrypt sensitive data
  encrypts :youtube_credentials

  # Other model code...
end
```

The credentials are stored as a JSON string with the following structure:

```json
{
  "access_token": "ya29.a0AfB_...",
  "refresh_token": "1//04dK...",
  "expires_at": **********,
  "token_type": "Bearer",
  "scope": "https://www.googleapis.com/auth/youtube.readonly https://www.googleapis.com/auth/userinfo.email"
}
```

### Token Management Service

The `YoutubeTokenService` handles all token-related operations:

```ruby
# app/services/youtube_token_service.rb
class YoutubeTokenService
  # Ensure a user has a fresh access token
  def self.ensure_fresh_token(user)
    # Implementation details...
  end

  # Refresh an expired token
  def self.refresh_token(user, credentials)
    # Implementation details...
  end

  # Revoke tokens when disconnecting
  def self.revoke_tokens(user)
    # Implementation details...
  end

  # Store new credentials
  def self.store_credentials(user, auth_data)
    # Implementation details...
  end
end
```

### YouTube Service Integration

The `BragRightsYouTubeService` uses the token service to ensure it always has a valid token:

```ruby
# app/services/brag_rights_you_tube_service.rb
class BragRightsYouTubeService
  def initialize(user = nil)
    @service = Google::Apis::YoutubeV3::YouTubeService.new
    @service.key = ENV['YOUTUBE_API_KEY']
    @user = user

    # Set up authorization if user is provided and has YouTube connected
    if @user&.youtube_connected?
      access_token = @user.youtube_access_token
      @service.authorization = access_token if access_token
    end
  end

  # Service methods...
end
```

### User Model Integration

The User model provides methods to interact with YouTube tokens:

```ruby
# app/models/user.rb
def youtube_connected?
  youtube_credentials.present?
end

def youtube_access_token
  YoutubeTokenService.ensure_fresh_token(self)
end

def connect_youtube_account(auth_data)
  # Implementation details...
end

def disconnect_youtube_account
  # Implementation details...
end
```

## Security Considerations

1. **Encryption**: All token data is encrypted at rest using Rails' Active Record Encryption.
2. **Token Refresh**: Tokens are automatically refreshed when they expire.
3. **Token Revocation**: When a user disconnects their YouTube account, tokens are properly revoked with Google.
4. **Error Handling**: Token errors are logged and handled gracefully.
5. **Minimal Exposure**: Access tokens are never exposed to the frontend.

## Testing

The token management system is thoroughly tested:

- Unit tests for `YoutubeTokenService`
- Unit tests for `BragRightsYouTubeService`
- Integration tests for YouTube-related functionality

## Environment Variables

The following environment variables are required:

```
YOUTUBE_API_KEY=your_api_key
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_client_secret
YOUTUBE_OAUTH_CALLBACK_URL=https://your-domain.com/auth/youtube/callback
```

## Troubleshooting

Common issues and solutions:

1. **Token Refresh Failures**: Check that `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET` are correctly set.
2. **Encryption Errors**: Ensure the `RAILS_MASTER_KEY` is properly set in production.
3. **API Errors**: Verify that the YouTube API is enabled in your Google Cloud Console project.
