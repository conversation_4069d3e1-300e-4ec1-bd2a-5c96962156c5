# YouTube Integration Consolidated Guide

This document provides a comprehensive overview of the YouTube integration in the Brag Rights application, consolidating information from various related documents.

## Table of Contents

1. [Overview](#overview)
2. [Implementation Strategy](#implementation-strategy)
3. [Subscriber-Only vs. Members-Only Leagues](#subscriber-only-vs-members-only-leagues)
4. [API Cost Considerations](#api-cost-considerations)
5. [Implementation Details](#implementation-details)
6. [Security Considerations](#security-considerations)
7. [Frontend Integration](#frontend-integration)
8. [Testing Strategy](#testing-strategy)
9. [Future Enhancements](#future-enhancements)
10. [References](#references)

## Overview

The YouTube integration allows content creators to connect with their communities through football predictions and leagues in the Brag Rights application. The primary feature is subscriber-only leagues, which are restricted to users who have subscribed to a creator's YouTube channel.

### Key Features

- YouTube account verification for creators
- Subscriber-only leagues (restricted to YouTube channel subscribers)
- YouTube community integration
- Special creator badges and features
- Subscriber verification system
- YouTube OAuth signup and account connection

## Implementation Strategy

We are following a phased approach:

### Phase 1: Subscriber-Only Leagues (Current)

- Implement basic YouTube integration
- Support subscriber-only leagues
- Establish monitoring and cost controls

### Phase 2: Evaluation and Planning

- Gather usage data and feedback
- Assess demand for members-only functionality
- Analyze costs and benefits

### Phase 3: Members-Only Leagues (Future)

- Apply for Members API access
- Implement members-only verification
- Update documentation and user interfaces

## Subscriber-Only vs. Members-Only Leagues

| Feature                       | Subscriber-Only Leagues         | Members-Only Leagues           |
| ----------------------------- | ------------------------------- | ------------------------------ |
| **User Action Required**      | Click "Subscribe" button (free) | Join channel membership (paid) |
| **Cost to User**              | Free                            | $4.99 - $24.99 per month       |
| **API Used**                  | YouTube Data API v3             | YouTube Members API            |
| **API Endpoint**              | `subscriptions.list`            | `members.list`                 |
| **Quota Cost**                | 100 units per verification      | Higher (restricted API)        |
| **Developer Access**          | Available to all developers     | Requires special approval      |
| **Implementation Complexity** | Moderate                        | High                           |

### Benefits of Starting with Subscriber-Only Leagues

1. **Lower Technical Barrier**

   - Uses standard YouTube Data API v3
   - No special approval required
   - Simpler OAuth scopes

2. **Cost Efficiency**

   - Lower quota usage
   - Predictable costs
   - Can operate within free tier for small to medium implementations

3. **Faster Time-to-Market**

   - Quicker implementation
   - No approval delays
   - Simpler user experience

4. **Broader Reach**
   - Available to all YouTube creators
   - Lower barrier for users (free subscription)
   - Higher potential adoption rate

## API Cost Considerations

### YouTube Data API v3 Quota System

The YouTube Data API v3 uses a quota system to ensure fair usage across all applications:

- **Free Tier**: By default, each project receives 10,000 quota units per day
- **Paid Tier**: Additional quota can be purchased through Google Cloud Platform

### Quota Usage by API Method

| API Method           | Quota Cost (units) | Description                                         |
| -------------------- | ------------------ | --------------------------------------------------- |
| `subscriptions.list` | 100                | Used to verify if a user is subscribed to a channel |
| `channels.list`      | 1                  | Used to get information about a channel             |
| `videos.list`        | 1                  | Used to get information about videos                |
| `search.list`        | 100                | Used to search for channels or videos               |

### Cost Estimation

With the free tier (10,000 units per day), you can perform:

- 100 subscription verifications per day (100 units each)
- OR 10,000 channel information lookups (1 unit each)

For paid tier costs:

- Additional quota costs $5 USD per 1,000,000 units
- Minimum purchase of 100,000 units ($0.50)

### Cost Optimization Strategies

1. **Intelligent Caching**

   - Cache subscription status for 24 hours
   - Cache channel information for 1 hour
   - Cache user subscriptions for 6 hours

2. **Quota Monitoring and Circuit Breaking**

   - Track all API calls and their quota usage
   - Prevent API calls when approaching daily quota limits
   - Provide admin dashboard for monitoring quota usage

3. **Tiered Verification Frequency**

   - Verify users based on their activity level
   - Active users: verify weekly on Sundays
   - Semi-active users: verify weekly on Wednesdays
   - Inactive users: verify monthly on the 1st

4. **Batch Processing**
   - Process users in batches to avoid memory issues
   - Check quota availability before processing each batch

## Implementation Details

### Setup Requirements

1. **Google Cloud Console Setup**

   - Create a new project in Google Cloud Console
   - Enable YouTube Data API v3
   - Configure OAuth 2.0 credentials with appropriate scopes
   - Set up API keys

2. **Environment Variables**

   ```
   GOOGLE_CLIENT_ID=your_client_id
   GOOGLE_CLIENT_SECRET=your_client_secret
   YOUTUBE_API_KEY=your_api_key
   YOUTUBE_OAUTH_CALLBACK_URL=https://your-domain.com/auth/youtube/callback
   ```

3. **Required Gems**
   ```ruby
   # Gemfile
   gem 'google-api-client'
   gem 'omniauth-google-oauth2'
   gem 'omniauth-rails_csrf_protection'
   ```

### Database Schema Updates

#### User Model Extensions

```ruby
# db/migrate/[timestamp]_add_youtube_fields_to_users.rb
class AddYoutubeFieldsToUsers < ActiveRecord::Migration[7.0]
  def change
    add_column :users, :youtube_channel_id, :string
    add_column :users, :youtube_channel_name, :string
    add_column :users, :is_content_creator, :boolean, default: false
    add_column :users, :youtube_verified_at, :datetime
    add_column :users, :youtube_subscriber_count, :integer
    add_column :users, :youtube_avatar_url, :string

    add_index :users, :youtube_channel_id, unique: true
  end
end
```

#### League Model Extensions

```ruby
# db/migrate/[timestamp]_add_youtube_fields_to_leagues.rb
class AddYoutubeFieldsToLeagues < ActiveRecord::Migration[7.0]
  def change
    add_column :leagues, :youtube_league, :boolean, default: false
    add_column :leagues, :youtube_channel_id, :string
    add_column :leagues, :subscriber_only, :boolean, default: false
    add_column :leagues, :min_subscriber_date, :datetime
    add_column :leagues, :subscriber_requirement_type, :string
    add_column :leagues, :unsubscribe_policy, :string, default: 'grace_period'

    add_index :leagues, :youtube_channel_id
  end
end
```

#### Membership Model Extensions

```ruby
# db/migrate/[timestamp]_add_subscription_fields_to_memberships.rb
class AddSubscriptionFieldsToMemberships < ActiveRecord::Migration[7.0]
  def change
    add_column :memberships, :subscription_status, :string
    add_column :memberships, :subscription_verified_at, :datetime
    add_column :memberships, :grace_period_ends_at, :datetime

    add_index :memberships, :subscription_status
    add_index :memberships, :grace_period_ends_at
  end
end
```

### YouTube Service Implementation

```ruby
# app/services/brag_rights_you_tube_service.rb
class BragRightsYouTubeService
  def initialize(user = nil)
    @user = user
    @service = Google::Apis::YoutubeV3::YouTubeService.new
    @service.key = ENV['YOUTUBE_API_KEY']
  end

  # Verify if a user is subscribed to a channel
  def verify_subscription?(channel_id, user_token)
    # Cache subscription status to reduce API calls
    cache_key = "youtube_subscription_#{user_token}_#{channel_id}"

    Rails.cache.fetch(cache_key, expires_in: 1.day) do
      begin
        # Set up the YouTube API client with user's OAuth token
        @service.authorization = user_token

        # Call the subscriptions.list method to check if user is subscribed
        response = @service.list_subscriptions(
          'snippet',
          mine: true,
          max_results: 50,
          force_ssl: true
        )

        # Check if the channel is in the user's subscriptions
        response.items.any? { |item| item.snippet.resource_id.channel_id == channel_id }
      rescue Google::Apis::Error => e
        Rails.logger.error "YouTube API error: #{e.message}"
        false
      end
    end
  end

  # Get information about a channel
  def get_channel_info(channel_id)
    cache_key = "youtube_channel_#{channel_id}"

    Rails.cache.fetch(cache_key, expires_in: 1.hour) do
      begin
        response = @service.list_channels(
          'snippet,statistics',
          id: channel_id
        )

        if response.items.any?
          channel = response.items.first
          {
            id: channel.id,
            title: channel.snippet.title,
            description: channel.snippet.description,
            thumbnail_url: channel.snippet.thumbnails.default.url,
            subscriber_count: channel.statistics.subscriber_count,
            video_count: channel.statistics.video_count
          }
        else
          nil
        end
      rescue Google::Apis::Error => e
        Rails.logger.error "YouTube API error: #{e.message}"
        nil
      end
    end
  end
end
```

### Periodic Subscription Verification

```ruby
# app/jobs/subscription_verification_job.rb
class SubscriptionVerificationJob < ApplicationJob
  queue_as :youtube

  def perform
    # Find all subscriber-only leagues
    subscriber_only_leagues = League.subscriber_only

    subscriber_only_leagues.each do |league|
      # Get all members of the league
      league.memberships.each do |membership|
        user = membership.user

        # Skip users without YouTube credentials
        next unless user.youtube_credentials.present?

        # Check if user is still subscribed
        is_subscribed = league.user_meets_subscription_requirements?(user)

        # Update membership status
        if is_subscribed
          # Update verification timestamp
          membership.update(
            subscription_status: 'active',
            subscription_verified_at: Time.current
          )
        else
          handle_unsubscribed_user(membership, league)
        end
      end
    end

    # Also check users in grace period whose time has expired
    grace_period_memberships = Membership.where(
      subscription_status: 'grace_period',
      grace_period_ends_at: ..Time.current
    )

    grace_period_memberships.each do |membership|
      # Remove user from league if grace period has expired
      user = membership.user
      league = membership.league

      membership.destroy

      # Notify user
      UserMailer.removed_from_league_email(user, league).deliver_later
    end
  end

  private

  def handle_unsubscribed_user(membership, league)
    case league.unsubscribe_policy
    when 'remove_immediately'
      # Remove user from league
      user = membership.user
      membership.destroy

      # Notify user
      UserMailer.subscription_required_email(user, league).deliver_later
    when 'grace_period'
      # Mark user as pending removal and set grace period
      membership.update(
        subscription_status: 'grace_period',
        grace_period_ends_at: Time.current + 7.days
      )

      # Notify user about grace period
      UserMailer.subscription_grace_period_email(membership.user, league).deliver_later
    end
  end
end
```

## Security Considerations

### API Key Protection

- Store keys in credentials.yml.enc
- Use environment variables
- Implement API key rotation
- Set up proper OAuth scopes

### Rate Limiting

- YouTube API quotas
- Request throttling
- Cache frequently accessed data

### Data Privacy

- Handle YouTube data according to GDPR
- Implement data retention policies
- Provide data export functionality
- Secure OAuth tokens storage

## Frontend Integration

The frontend should:

1. Allow content creators to mark leagues as subscriber-only when creating them
2. Show appropriate messaging for subscriber-only leagues
3. Guide users through connecting their YouTube accounts when needed
4. Handle subscription verification errors gracefully
5. Display subscription status and grace period information to users
6. Provide clear instructions on how to subscribe to the channel if needed

### Example React Component

```typescript
// Example YouTube league creation form
const YoutubeLeagueForm: React.FC = () => {
  const [isYoutubeLeague, setIsYoutubeLeague] = useState(false);
  const [isSubscriberOnly, setIsSubscriberOnly] = useState(false);
  const [requirementType, setRequirementType] = useState("all");
  const [unsubscribePolicy, setUnsubscribePolicy] = useState("grace_period");

  // Form submission handler
  const handleSubmit = async (values) => {
    // Add YouTube-specific fields if this is a YouTube league
    if (isYoutubeLeague) {
      values.youtube_league = true;
      values.youtube_channel_id = currentUser.youtube_channel_id;
      values.subscriber_only = isSubscriberOnly;

      // Add subscriber requirements if applicable
      if (isSubscriberOnly) {
        values.subscriber_requirement_type = requirementType;
        values.unsubscribe_policy = unsubscribePolicy;

        if (requirementType === "before_date") {
          values.min_subscriber_date = values.min_subscriber_date || null;
        }
      }
    }

    // Submit the form
    const response = await createLeague(values);
  };

  return (
    <Form onSubmit={handleSubmit}>
      {/* Regular league fields */}
      <TextField name="name" label="League Name" required />

      {/* YouTube-specific fields */}
      {isYoutubeCreator && (
        <div className="youtube-options">
          <CheckboxField
            name="youtube_league"
            label="Create as YouTube League"
            onChange={(e) => setIsYoutubeLeague(e.target.checked)}
          />

          {isYoutubeLeague && (
            <>
              <CheckboxField
                name="subscriber_only"
                label="Subscriber-Only League"
                onChange={(e) => setIsSubscriberOnly(e.target.checked)}
              />

              {isSubscriberOnly && (
                <div className="subscriber-options">
                  <SelectField
                    name="subscriber_requirement_type"
                    label="Subscriber Requirement"
                    options={[
                      { value: "all", label: "All Subscribers" },
                      { value: "before_date", label: "Subscribed Before Date" },
                    ]}
                    onChange={(e) => setRequirementType(e.target.value)}
                  />

                  {requirementType === "before_date" && (
                    <DateField
                      name="min_subscriber_date"
                      label="Minimum Subscription Date"
                    />
                  )}

                  <SelectField
                    name="unsubscribe_policy"
                    label="Unsubscribe Policy"
                    options={[
                      { value: "grace_period", label: "Grace Period (7 days)" },
                      {
                        value: "remove_immediately",
                        label: "Remove Immediately",
                      },
                    ]}
                    onChange={(e) => setUnsubscribePolicy(e.target.value)}
                  />
                </div>
              )}
            </>
          )}
        </div>
      )}

      <Button type="submit">Create League</Button>
    </Form>
  );
};
```

## Testing Strategy

### Unit Tests

```ruby
# spec/services/youtube_service_spec.rb
require 'rails_helper'

RSpec.describe BragRightsYouTubeService do
  let(:service) { BragRightsYouTubeService.new }

  describe '#verify_subscription?' do
    let(:channel_id) { 'UC1234567890abcdef' }
    let(:user_token) { 'valid_token' }

    context 'when the user is subscribed' do
      before do
        # Mock the YouTube API response
        allow_any_instance_of(Google::Apis::YoutubeV3::YouTubeService)
          .to receive(:list_subscriptions)
          .and_return(
            OpenStruct.new(
              items: [
                OpenStruct.new(
                  snippet: OpenStruct.new(
                    resource_id: OpenStruct.new(
                      channel_id: channel_id
                    )
                  )
                )
              ]
            )
          )
      end

      it 'returns true' do
        expect(service.verify_subscription?(channel_id, user_token)).to be true
      end
    end

    context 'when the user is not subscribed' do
      before do
        # Mock the YouTube API response with no matching channel
        allow_any_instance_of(Google::Apis::YoutubeV3::YouTubeService)
          .to receive(:list_subscriptions)
          .and_return(
            OpenStruct.new(
              items: [
                OpenStruct.new(
                  snippet: OpenStruct.new(
                    resource_id: OpenStruct.new(
                      channel_id: 'different_channel_id'
                    )
                  )
                )
              ]
            )
          )
      end

      it 'returns false' do
        expect(service.verify_subscription?(channel_id, user_token)).to be false
      end
    end
  end
end
```

### Integration Tests

```ruby
# spec/requests/youtube_subscription_spec.rb
require 'rails_helper'

RSpec.describe "YouTube Subscription Verification", type: :request do
  let(:user) { create(:user, youtube_credentials: 'valid_token') }
  let(:owner) { create(:user, is_content_creator: true, youtube_channel_id: 'UC1234567890abcdef') }
  let(:league) do
    create(
      :league,
      owner: owner,
      youtube_league: true,
      youtube_channel_id: owner.youtube_channel_id,
      subscriber_only: true
    )
  end

  before do
    sign_in user
  end

  context 'when joining a subscriber-only league' do
    context 'when the user is subscribed' do
      before do
        allow_any_instance_of(BragRightsYouTubeService)
          .to receive(:verify_subscription?)
          .with(owner.youtube_channel_id, user.youtube_credentials)
          .and_return(true)
      end

      it 'allows the user to join the league' do
        post "/api/v1/leagues/#{league.id}/memberships"

        expect(response).to have_http_status(:created)
        expect(user.leagues).to include(league)
      end
    end

    context 'when the user is not subscribed' do
      before do
        allow_any_instance_of(BragRightsYouTubeService)
          .to receive(:verify_subscription?)
          .with(owner.youtube_channel_id, user.youtube_credentials)
          .and_return(false)
      end

      it 'prevents the user from joining the league' do
        post "/api/v1/leagues/#{league.id}/memberships"

        expect(response).to have_http_status(:forbidden)
        expect(user.leagues).not_to include(league)
      end
    end
  end
end
```

## Future Enhancements

1. **Members-Only Leagues**

   - Implement paid channel membership verification
   - Support different membership tiers
   - Integrate with YouTube Members API

2. **Enhanced Creator Features**

   - YouTube community post integration
   - Live stream predictions
   - Creator analytics dashboard
   - Automated highlight clips

3. **Advanced Subscription Management**

   - More granular subscription requirements
   - Subscription duration tracking
   - Special rewards for long-term subscribers

4. **Performance Optimizations**
   - More efficient API usage
   - Advanced caching strategies
   - Batch processing improvements

## References

- [YouTube Data API Documentation](https://developers.google.com/youtube/v3)
- [OAuth 2.0 Implementation Guide](https://developers.google.com/identity/protocols/oauth2)
- [API Quotas and Pricing](https://developers.google.com/youtube/v3/getting-started#quota)
- [YouTube API Quota Calculator](https://developers.google.com/youtube/v3/determine_quota_cost)
- [Google Cloud Platform Pricing](https://cloud.google.com/youtube/pricing)
- [YouTube API Quota Management Best Practices](https://developers.google.com/youtube/v3/guides/quota_and_compliance_audits)
