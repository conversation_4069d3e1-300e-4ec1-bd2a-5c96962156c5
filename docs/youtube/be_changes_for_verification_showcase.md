# Backend Changes for Verification Showcase (if needed)

Goal: Ensure the verification video can demonstrate all uses of the requested scopes.

## Essential behaviors

1. Basic login/signup

- Keep existing endpoints `POST /api/v1/auth/youtube/login` and `POST /api/v1/auth/youtube/signup` exchanging `code` for your app tokens. These use basic scopes and should work at scale post-consent-screen publishing.

2. Connect YouTube (incremental auth)

- `POST /api/v1/youtube_auth/connect` should exchange `code` granted with `youtube.readonly` and link channel metadata to the authenticated user.
- Persist: channel_id, channel_name, (optionally) refresh_token if you perform server-side YouTube calls.

3. Creator eligibility and subscriber verification

- Creator eligibility endpoint (optional but useful in demo):
  - `GET /api/v1/youtube/check_subscriber_league_eligibility` → { eligible, message, subscriber_count, min_subscribers_required }
- Subscription verification endpoints (for join flow):
  - `POST /api/v1/youtube_auth/verify_subscription` with { channel_id }
  - `GET  /api/v1/youtube/subscription_status?channel_id=...` returns { connected, subscribed }

4. Status and disconnect

- `GET /api/v1/youtube_auth/status` → { connected, channel_id, channel_name, is_content_creator }
- `POST /api/v1/youtube_auth/disconnect` unlinks channel

5. Creator mode toggle

- `POST /api/v1/youtube/update_creator_status` { is_content_creator: boolean }
- Return 422 with clear message if eligibility not met (e.g., <100 subscribers)

## Responses and errors (friendly for demo)

- 409 conflict when channel already linked to another account. Include that email if possible; FE surfaces it.
- 403 for gated features if toggles off; FE shows non-blocking message.
- 422 with human-friendly message for eligibility failures.

## Data minimization note

- Use YouTube data strictly for: (a) creator eligibility via subscriber count; (b) verifying a users subscription to a creators channel. No publishing, uploads, or write scopes.

## Admin/testing helpers (optional)

- Environment flag NEXT_PUBLIC_HIDE_YOUTUBE_CONNECT (FE) to momentarily hide the Connect button during review if needed; default visible for recording
- Test-only flag to bypass real API calls and return deterministic demo data (clearly labeled in UI as "Test environment").

## Security

- Store OAuth tokens only server-side; return only your apps tokens to FE
- Validate `redirect_uri` and `client_id` on exchanges
- Enforce HTTPS; set CORS for FE origin

## What to capture in the video

- OAuth URL with `client_id` visible; consent screen scopes
- Creator flow: connect, see subscriber count ≥100, create league
- Regular user flow: verify subscription, join league
- Privacy Policy and Terms links
