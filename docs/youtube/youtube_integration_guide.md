# YouTube Integration Guide for BragRights

## Overview

This guide details the implementation of YouTube creator integration in BragRights, allowing content creators to connect with their communities through football predictions and leagues.

Note on rollout: We are currently launching with Google basic auth only (openid email profile). YouTube Connect and Creator features are deferred until Google verification completes. Backend endpoints for Connect/Creator exist but are gated behind ENV flags and will return 403 in production.

## Features

- YouTube account verification for creators
- Subscriber-only leagues (restricted to YouTube channel subscribers)
- YouTube community integration
- Special creator badges and features
- Subscriber verification system
- YouTube OAuth signup and account connection (Connect flow gated off in production for now)

## Setup Requirements

1. **Google Cloud Console Setup**

   - Create a new project in Google Cloud Console
   - Enable YouTube Data API v3
   - Configure OAuth 2.0 credentials with these settings:
     - Authorized redirect URIs:
       - `https://your-domain.com/auth/youtube/callback` (production)
       - `http://localhost:3000/auth/youtube/callback` (development)
     - Scopes needed:
       - `youtube.readonly` (for reading subscriptions and channel info)
       - `userinfo.email` (for user identification)
       - `userinfo.profile` (for user profile information)
     - Important: We intentionally avoid requesting the `youtube.force-ssl` scope as it requests excessive permissions including editing and deleting videos
   - Set up API keys

2. **Environment Variables**

   ```
   GOOGLE_CLIENT_ID=your_client_id
   GOOGLE_CLIENT_SECRET=your_client_secret
   YOUTUBE_API_KEY=your_api_key
   YOUTUBE_OAUTH_CALLBACK_URL=https://your-domain.com/auth/youtube/callback
   YOUTUBE_CONNECT_ENABLED=false  # prod default
   YOUTUBE_CREATOR_ENABLED=false  # prod default
   ```

3. **Required Gems**
   ```ruby
   # Gemfile
   gem 'google-api-client'
   gem 'omniauth-google-oauth2'
   gem 'omniauth-rails_csrf_protection'
   ```

## Database Schema Updates

### User Model Extensions

```ruby
# db/migrate/[timestamp]_add_youtube_fields_to_users.rb
class AddYoutubeFieldsToUsers < ActiveRecord::Migration[7.0]
  def change
    add_column :users, :youtube_channel_id, :string
    add_column :users, :youtube_channel_name, :string
    add_column :users, :is_content_creator, :boolean, default: false
    add_column :users, :youtube_verified_at, :datetime
    add_column :users, :youtube_subscriber_count, :integer
    add_column :users, :youtube_avatar_url, :string

    add_index :users, :youtube_channel_id, unique: true
  end
end
```

### League Model Extensions

```ruby
# db/migrate/[timestamp]_add_youtube_fields_to_leagues.rb
class AddYoutubeFieldsToLeagues < ActiveRecord::Migration[7.0]
  def change
    add_column :leagues, :youtube_league, :boolean, default: false
    add_column :leagues, :youtube_channel_id, :string
    add_column :leagues, :subscriber_only, :boolean, default: false
    add_column :leagues, :min_subscriber_date, :datetime
    add_column :leagues, :subscriber_requirement_type, :string
    add_column :leagues, :unsubscribe_policy, :string, default: 'grace_period'

    add_index :leagues, :youtube_channel_id
  end
end
```

### Membership Model Extensions

For tracking subscription status and grace periods:

```ruby
# db/migrate/[timestamp]_add_subscription_fields_to_memberships.rb
class AddSubscriptionFieldsToMemberships < ActiveRecord::Migration[7.0]
  def change
    add_column :memberships, :subscription_status, :string
    add_column :memberships, :subscription_verified_at, :datetime
    add_column :memberships, :grace_period_ends_at, :datetime

    add_index :memberships, :subscription_status
    add_index :memberships, :grace_period_ends_at
  end
end
```

## Subscriber-Only Leagues Implementation

Subscriber-only leagues allow YouTube content creators to create exclusive leagues that only their channel subscribers can join. This feature creates a stronger connection between creators and their communities and provides an incentive for viewers to subscribe to the creator's channel.

> **Note:** This implementation focuses on **subscriber-only** leagues (requiring the free "Subscribe" action) rather than **members-only** leagues (requiring paid channel memberships). Members-only functionality may be considered as a future enhancement.

### 1. Overview

Subscriber-only leagues have the following characteristics:

- Only accessible to users who have subscribed to the creator's YouTube channel
- Can have additional requirements like minimum subscription date
- Automatically verify subscription status when users attempt to join
- Periodically verify that members remain subscribed
- Configurable grace period for users who unsubscribe

### 2. League Model Fields

The following fields in the League model are used to implement members-only functionality:

- `youtube_league` (boolean): Indicates if this is a YouTube creator's league
- `youtube_channel_id` (string): Links the league to a specific YouTube channel
- `subscriber_only` (boolean): Flag to indicate if the league is restricted to subscribers only
- `min_subscriber_date` (datetime): Optional date requirement (subscribers must have subscribed before this date)
- `subscriber_requirement_type` (string): Type of subscriber requirement (e.g., "all", "before_date")
- `unsubscribe_policy` (string): How to handle users who unsubscribe (options: "grace_period", "remove_immediately")

### 3. Implementation Steps

#### 3.1 Database Migration

First, create a migration to add the necessary fields to the League model:

```ruby
# db/migrate/[timestamp]_add_youtube_fields_to_leagues.rb
class AddYoutubeFieldsToLeagues < ActiveRecord::Migration[7.0]
  def change
    add_column :leagues, :youtube_league, :boolean, default: false
    add_column :leagues, :youtube_channel_id, :string
    add_column :leagues, :subscriber_only, :boolean, default: false
    add_column :leagues, :min_subscriber_date, :datetime
    add_column :leagues, :subscriber_requirement_type, :string
    add_column :leagues, :unsubscribe_policy, :string, default: 'grace_period'

    add_index :leagues, :youtube_channel_id
  end
end
```

Also add fields to the Membership model to track subscription status:

```ruby
# db/migrate/[timestamp]_add_subscription_fields_to_memberships.rb
class AddSubscriptionFieldsToMemberships < ActiveRecord::Migration[7.0]
  def change
    add_column :memberships, :subscription_status, :string
    add_column :memberships, :subscription_verified_at, :datetime
    add_column :memberships, :grace_period_ends_at, :datetime

    add_index :memberships, :subscription_status
    add_index :memberships, :grace_period_ends_at
  end
end
```

#### 3.2 League Model Updates

Update the League model to include methods for handling subscriber-only functionality:

```ruby
# app/models/league.rb
class League < ApplicationRecord
  # Existing code...

  # Scope for finding subscriber-only leagues
  scope :subscriber_only, -> { where(subscriber_only: true, youtube_channel_id: present?) }

  # Check if this is a subscriber-only league
  def subscriber_only_league?
    subscriber_only? && youtube_channel_id.present?
  end

  # Check if a user meets the subscription requirements
  def user_meets_subscription_requirements?(user)
    return true unless subscriber_only_league?
    return false unless user.youtube_credentials.present?

    # Verify subscription
    subscription_verified = BragRightsYouTubeService.new.verify_subscription?(
      youtube_channel_id,
      user.youtube_credentials
    )

    # Check additional requirements if needed
    if subscription_verified && subscriber_requirement_type == 'before_date' && min_subscriber_date.present?
      # This would require additional YouTube API calls to check subscription date
      # For now, we'll assume all subscribers meet the date requirement
      return true
    end

    subscription_verified
  end
end
```

#### 3.3 League Controller Updates

Update the `LeaguesController` to permit YouTube-related parameters:

```ruby
# app/controllers/api/v1/leagues_controller.rb
def league_params
  params.require(:league).permit(
    :name,
    :competition_id,
    :open,
    :season_id,
    :starting_matchday,
    :youtube_league,
    :youtube_channel_id,
    :subscriber_only,
    :min_subscriber_date,
    :subscriber_requirement_type,
    :unsubscribe_policy
  )
end
```

#### 3.4 YouTube Service Implementation

Create a YouTube service to handle API interactions, including subscription verification:

```ruby
# app/services/brag_rights_you_tube_service.rb
class BragRightsYouTubeService
  def initialize(user = nil)
    @user = user
    @service = Google::Apis::YoutubeV3::YouTubeService.new
    @service.key = ENV['YOUTUBE_API_KEY']
  end

  # Verify if a user is subscribed to a channel
  def verify_subscription?(channel_id, user_token)
    # Cache subscription status to reduce API calls
    cache_key = "youtube_subscription_#{user_token}_#{channel_id}"

    Rails.cache.fetch(cache_key, expires_in: 1.day) do
      begin
        # Set up the YouTube API client with user's OAuth token
        @service.authorization = user_token

        # Call the subscriptions.list method to check if user is subscribed
        # Note: We're using the youtube.readonly scope, so we don't need force_ssl: true
        response = @service.list_subscriptions(
          'snippet',
          mine: true,
          max_results: 50
        )

        # Check if the channel is in the user's subscriptions
        response.items.any? { |item| item.snippet.resource_id.channel_id == channel_id }
      rescue Google::Apis::Error => e
        Rails.logger.error "YouTube API error: #{e.message}"
        false
      end
    end
  end

  # Get information about a channel
  def get_channel_info(channel_id)
    cache_key = "youtube_channel_#{channel_id}"

    Rails.cache.fetch(cache_key, expires_in: 1.hour) do
      begin
        response = @service.list_channels(
          'snippet,statistics',
          id: channel_id
        )

        if response.items.any?
          channel = response.items.first
          {
            id: channel.id,
            title: channel.snippet.title,
            description: channel.snippet.description,
            thumbnail_url: channel.snippet.thumbnails.default.url,
            subscriber_count: channel.statistics.subscriber_count,
            video_count: channel.statistics.video_count
          }
        else
          nil
        end
      rescue Google::Apis::Error => e
        Rails.logger.error "YouTube API error: #{e.message}"
        nil
      end
    end
  end
end
```

#### 3.5 Membership Controller Updates

Update the `LeagueMembershipsController` to check subscription status before allowing users to join a members-only league:

```ruby
# app/controllers/api/v1/league_memberships_controller.rb
def create
  league = League.find(params[:league_id])

  # Check if user has reached maximum leagues
  if current_devise_api_user.memberships.count >= 5
    render json: { errors: 'MAXIMUM_LEAGUES_REACHED' }, status: :unprocessable_entity
    return
  end

  # Check if this is a subscriber-only league
  if league.subscriber_only_league?
    unless league.user_meets_subscription_requirements?(current_devise_api_user)
      render json: {
        error: 'This league is only available to YouTube subscribers of the channel'
      }, status: :forbidden
      return
    end
  end

  if league.open?
    handle_open_league_join(league)
  else
    render json: {
      error: 'This is a private league. Please submit a join request.'
    }, status: :unprocessable_entity
  end
end

private

def handle_open_league_join(league)
  membership = league.memberships.new(user: current_devise_api_user)

  # If this is a subscriber-only league, set subscription status
  if league.subscriber_only_league?
    membership.subscription_status = 'active'
    membership.subscription_verified_at = Time.current
  end

  if membership.save
    render json: {
      message: 'Successfully joined the league',
      data: ActiveModelSerializers::SerializableResource.new(
        membership, serializer: MembershipSerializer
      )
    }, status: :created
  else
    render json: { errors: membership.errors.full_messages },
           status: :unprocessable_entity
  end
end
```

#### 3.6 League Join Request Controller Updates

Similarly, update the `LeagueJoinRequestsController` to check subscription status:

```ruby
# app/controllers/api/v1/league_join_requests_controller.rb
def create
  return render_already_member if @league.users.include?(current_devise_api_user)
  return render_maximum_leagues if current_devise_api_user.memberships.count >= 5

  # Check if this is a subscriber-only league
  if @league.subscriber_only_league?
    unless @league.user_meets_subscription_requirements?(current_devise_api_user)
      render json: {
        error: 'This league is only available to YouTube subscribers of the channel'
      }, status: :forbidden
      return
    end
  end

  request = @league.league_join_requests.new(user: current_devise_api_user)

  if request.save
    render json: { message: 'Join request submitted successfully' },
           status: :created
  else
    render json: { errors: request.errors.full_messages },
           status: :unprocessable_entity
  end
end
```

#### 3.7 Periodic Subscription Verification

Create a background job to periodically verify that members of subscriber-only leagues are still subscribed:

```ruby
# app/jobs/subscription_verification_job.rb
class SubscriptionVerificationJob < ApplicationJob
  queue_as :youtube

  def perform
    # Find all subscriber-only leagues
    subscriber_only_leagues = League.subscriber_only

    subscriber_only_leagues.each do |league|
      # Get all members of the league
      league.memberships.each do |membership|
        user = membership.user

        # Skip users without YouTube credentials
        next unless user.youtube_credentials.present?

        # Check if user is still subscribed
        is_subscribed = league.user_meets_subscription_requirements?(user)

        # Update membership status
        if is_subscribed
          # Update verification timestamp
          membership.update(
            subscription_status: 'active',
            subscription_verified_at: Time.current
          )
        else
          handle_unsubscribed_user(membership, league)
        end
      end
    end

    # Also check users in grace period whose time has expired
    grace_period_memberships = Membership.where(
      subscription_status: 'grace_period',
      grace_period_ends_at: ..Time.current
    )

    grace_period_memberships.each do |membership|
      # Remove user from league if grace period has expired
      user = membership.user
      league = membership.league

      membership.destroy

      # Notify user
      UserMailer.removed_from_league_email(user, league).deliver_later
    end
  end

  private

  def handle_unsubscribed_user(membership, league)
    case league.unsubscribe_policy
    when 'remove_immediately'
      # Remove user from league
      user = membership.user
      membership.destroy

      # Notify user
      UserMailer.subscription_required_email(user, league).deliver_later
    when 'grace_period'
      # Mark user as pending removal and set grace period
      membership.update(
        subscription_status: 'grace_period',
        grace_period_ends_at: Time.current + 7.days
      )

      # Notify user about grace period
      UserMailer.subscription_grace_period_email(membership.user, league).deliver_later
    end
  end
end
```

Schedule this job to run regularly:

```ruby
# config/sidekiq.yml
:schedule:
  subscription_verification_job:
    cron: '0 0 * * 0'  # Weekly on Sunday at midnight
    class: SubscriptionVerificationJob
    queue: youtube
```

#### 3.8 User Notifications

Create email templates to notify users about subscription requirements:

```ruby
# app/mailers/user_mailer.rb
class UserMailer < ApplicationMailer
  def subscription_required_email(user, league)
    @user = user
    @league = league
    @channel_info = BragRightsYouTubeService.new.get_channel_info(league.youtube_channel_id)

    mail(
      to: @user.email,
      subject: "Subscription Required for #{@league.name}"
    )
  end

  def subscription_grace_period_email(user, league)
    @user = user
    @league = league
    @channel_info = BragRightsYouTubeService.new.get_channel_info(league.youtube_channel_id)
    @grace_period_ends_at = user.memberships.find_by(league_id: league.id).grace_period_ends_at

    mail(
      to: @user.email,
      subject: "Action Required: Subscription Needed for #{@league.name}"
    )
  end

  def removed_from_league_email(user, league)
    @user = user
    @league = league
    @channel_info = BragRightsYouTubeService.new.get_channel_info(league.youtube_channel_id)

    mail(
      to: @user.email,
      subject: "You've Been Removed from #{@league.name}"
    )
  end
end
```

### 4. Frontend Implementation

#### 4.1 Frontend Considerations

The frontend should:

1. Allow content creators to mark leagues as members-only when creating them
2. Show appropriate messaging for members-only leagues
3. Guide users through connecting their YouTube accounts when needed
4. Handle subscription verification errors gracefully
5. Display subscription status and grace period information to users
6. Provide clear instructions on how to subscribe to the channel if needed

#### 4.2 League Creation Form

Example React component for creating a YouTube league with members-only option:

```typescript
import React, { useState, useEffect } from "react";
import { Form, Button, Alert } from "react-bootstrap";
import { useSelector } from "react-redux";
import {
  TextField,
  SelectField,
  CheckboxField,
  DateField,
} from "../components/FormFields";
import { createLeague } from "../services/leagueService";

const YoutubeLeagueForm: React.FC = () => {
  const currentUser = useSelector((state) => state.auth.user);
  const [isYoutubeLeague, setIsYoutubeLeague] = useState(false);
  const [isSubscriberOnly, setIsSubscriberOnly] = useState(false);
  const [requirementType, setRequirementType] = useState("all");
  const [unsubscribePolicy, setUnsubscribePolicy] = useState("grace_period");
  const [competitions, setCompetitions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Check if user is a YouTube creator
  const isYoutubeCreator =
    currentUser?.is_content_creator && currentUser?.youtube_channel_id;

  useEffect(() => {
    // Fetch competitions
    // ...
  }, []);

  // Form submission handler
  const handleSubmit = async (values) => {
    setLoading(true);
    setError(null);

    try {
      // Add YouTube-specific fields if this is a YouTube league
      if (isYoutubeLeague) {
        values.youtube_league = true;
        values.youtube_channel_id = currentUser.youtube_channel_id;
        values.subscriber_only = isSubscriberOnly;

        // Add subscriber requirements if applicable
        if (isSubscriberOnly) {
          values.subscriber_requirement_type = requirementType;
          values.unsubscribe_policy = unsubscribePolicy;

          if (requirementType === "before_date") {
            values.min_subscriber_date = values.min_subscriber_date || null;
          }
        }
      }

      // Submit the form
      const response = await createLeague(values);

      // Handle success (redirect, show message, etc.)
    } catch (err) {
      setError(err.message || "Failed to create league");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="league-form-container">
      <h2>Create New League</h2>

      {error && <Alert variant="danger">{error}</Alert>}

      <Form onSubmit={handleSubmit}>
        {/* Regular league fields */}
        <TextField name="name" label="League Name" required />

        <SelectField
          name="competition_id"
          label="Competition"
          options={competitions}
          required
        />

        <CheckboxField
          name="open"
          label="Open League (anyone can join)"
          defaultChecked={true}
        />

        <SelectField
          name="season_id"
          label="Season"
          options={seasons}
          required
        />

        {/* YouTube-specific fields */}
        {isYoutubeCreator && (
          <div className="youtube-options mt-4">
            <h4>YouTube Integration</h4>

            <CheckboxField
              name="youtube_league"
              label="Create as YouTube League"
              onChange={(e) => setIsYoutubeLeague(e.target.checked)}
            />

            {isYoutubeLeague && (
              <>
                <div className="youtube-league-info alert alert-info">
                  <p>
                    <strong>YouTube League:</strong> This league will be
                    associated with your YouTube channel "
                    {currentUser.youtube_channel_name}".
                  </p>
                </div>

                <CheckboxField
                  name="subscriber_only"
                  label="Members-Only League (restrict to YouTube subscribers only)"
                  onChange={(e) => setIsSubscriberOnly(e.target.checked)}
                />

                {isSubscriberOnly && (
                  <div className="subscriber-options ml-4 mt-3">
                    <SelectField
                      name="subscriber_requirement_type"
                      label="Subscriber Requirement"
                      options={[
                        { value: "all", label: "All Subscribers" },
                        {
                          value: "before_date",
                          label: "Subscribed Before Date",
                        },
                      ]}
                      onChange={(e) => setRequirementType(e.target.value)}
                    />

                    {requirementType === "before_date" && (
                      <DateField
                        name="min_subscriber_date"
                        label="Minimum Subscription Date"
                        helpText="Only users who subscribed before this date can join"
                      />
                    )}

                    <SelectField
                      name="unsubscribe_policy"
                      label="Unsubscribe Policy"
                      options={[
                        {
                          value: "grace_period",
                          label: "Grace Period (7 days to resubscribe)",
                        },
                        {
                          value: "remove_immediately",
                          label: "Remove Immediately",
                        },
                      ]}
                      onChange={(e) => setUnsubscribePolicy(e.target.value)}
                    />

                    <div className="members-only-info alert alert-warning mt-3">
                      <p>
                        <strong>Members-Only League:</strong> Users must be
                        subscribed to your YouTube channel to join this league.
                        The system will periodically verify that members remain
                        subscribed.
                      </p>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        )}

        <div className="form-actions mt-4">
          <Button type="submit" variant="primary" disabled={loading}>
            {loading ? "Creating..." : "Create League"}
          </Button>
        </div>
      </Form>
    </div>
  );
};
```

#### 4.3 Handling Subscription Verification

When a user attempts to join a members-only league, the frontend needs to handle subscription verification:

```typescript
import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Button, Alert, Card } from "react-bootstrap";
import { useSelector } from "react-redux";
import { joinLeague, getLeagueDetails } from "../services/leagueService";
import {
  verifyYoutubeSubscription,
  connectYoutubeAccount,
} from "../services/youtubeService";

const JoinLeague: React.FC = () => {
  const { leagueId } = useParams();
  const navigate = useNavigate();
  const currentUser = useSelector((state) => state.auth.user);
  const [league, setLeague] = useState(null);
  const [loading, setLoading] = useState(true);
  const [joining, setJoining] = useState(false);
  const [error, setError] = useState(null);
  const [subscriptionStatus, setSubscriptionStatus] = useState(null);

  useEffect(() => {
    const fetchLeague = async () => {
      try {
        const data = await getLeagueDetails(leagueId);
        setLeague(data);

        // If this is a subscriber-only league, check subscription status
        if (data.subscriber_only && data.youtube_channel_id) {
          checkSubscriptionStatus(data.youtube_channel_id);
        }
      } catch (err) {
        setError("Failed to load league details");
      } finally {
        setLoading(false);
      }
    };

    fetchLeague();
  }, [leagueId]);

  const checkSubscriptionStatus = async (channelId) => {
    try {
      // Only check if user has YouTube connected
      if (currentUser.youtube_credentials) {
        const status = await verifyYoutubeSubscription(channelId);
        setSubscriptionStatus(status);
      } else {
        setSubscriptionStatus({ connected: false });
      }
    } catch (err) {
      console.error("Failed to check subscription status", err);
      setSubscriptionStatus({ error: true });
    }
  };

  const handleJoinLeague = async () => {
    setJoining(true);
    setError(null);

    try {
      await joinLeague(leagueId);
      navigate(`/leagues/${leagueId}`);
    } catch (err) {
      if (err.response?.status === 403 && league.subscriber_only) {
        setError(
          "You must be subscribed to the YouTube channel to join this league"
        );
      } else {
        setError(err.message || "Failed to join league");
      }
    } finally {
      setJoining(false);
    }
  };

  const handleConnectYoutube = async () => {
    try {
      await connectYoutubeAccount();
      // This will redirect to YouTube OAuth flow
    } catch (err) {
      setError("Failed to connect YouTube account");
    }
  };

  if (loading) return <div>Loading...</div>;
  if (!league) return <div>League not found</div>;

  const isSubscriberOnlyLeague =
    league.subscriber_only && league.youtube_channel_id;
  const canJoin =
    !isSubscriberOnlyLeague ||
    (subscriptionStatus?.connected && subscriptionStatus?.subscribed);

  return (
    <div className="join-league-container">
      <h2>Join {league.name}</h2>

      {error && <Alert variant="danger">{error}</Alert>}

      <Card className="mb-4">
        <Card.Body>
          <Card.Title>{league.name}</Card.Title>
          <Card.Subtitle className="mb-2 text-muted">
            {league.competition_name} - {league.season_name}
          </Card.Subtitle>

          <div className="league-details mt-3">
            <p>
              <strong>Owner:</strong> {league.owner_username}
            </p>
            <p>
              <strong>Members:</strong> {league.member_count}
            </p>
            <p>
              <strong>Type:</strong> {league.open ? "Open" : "Private"}
            </p>

            {isSubscriberOnlyLeague && (
              <div className="youtube-info mt-3">
                <Alert variant="info">
                  <strong>Members-Only League:</strong> This is a YouTube
                  members-only league. You must be subscribed to the channel to
                  join.
                </Alert>

                {subscriptionStatus && (
                  <div className="subscription-status mt-3">
                    {!subscriptionStatus.connected && (
                      <>
                        <Alert variant="warning">
                          You need to connect your YouTube account first.
                        </Alert>
                        <Button
                          variant="outline-danger"
                          onClick={handleConnectYoutube}
                        >
                          Connect YouTube Account
                        </Button>
                      </>
                    )}

                    {subscriptionStatus.connected &&
                      !subscriptionStatus.subscribed && (
                        <Alert variant="warning">
                          <p>You are not subscribed to this channel.</p>
                          <a
                            href={`https://www.youtube.com/channel/${league.youtube_channel_id}?sub_confirmation=1`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="btn btn-danger"
                          >
                            Subscribe to Channel
                          </a>
                          <Button
                            variant="outline-secondary"
                            className="ml-2"
                            onClick={() =>
                              checkSubscriptionStatus(league.youtube_channel_id)
                            }
                          >
                            Verify Subscription
                          </Button>
                        </Alert>
                      )}

                    {subscriptionStatus.connected &&
                      subscriptionStatus.subscribed && (
                        <Alert variant="success">
                          You are subscribed to this channel and eligible to
                          join!
                        </Alert>
                      )}
                  </div>
                )}
              </div>
            )}
          </div>
        </Card.Body>
      </Card>

      <div className="actions">
        <Button
          variant="primary"
          onClick={handleJoinLeague}
          disabled={joining || !canJoin}
        >
          {joining ? "Joining..." : "Join League"}
        </Button>

        {!canJoin && isSubscriberOnlyLeague && (
          <p className="text-muted mt-2">
            You must meet the subscription requirements to join this league.
          </p>
        )}
      </div>
    </div>
  );
};
```

### 5. Testing Strategy

#### 5.1 Unit Tests

Create unit tests for the YouTube service and subscription verification:

```ruby
# spec/services/youtube_service_spec.rb
require 'rails_helper'

RSpec.describe YoutubeService do
  let(:service) { YoutubeService.new }

  describe '#verify_subscription?' do
    let(:channel_id) { 'UC1234567890abcdef' }
    let(:user_token) { 'valid_token' }

    context 'when the user is subscribed' do
      before do
        # Mock the YouTube API response
        allow_any_instance_of(Google::Apis::YoutubeV3::YouTubeService)
          .to receive(:list_subscriptions)
          .and_return(
            OpenStruct.new(
              items: [
                OpenStruct.new(
                  snippet: OpenStruct.new(
                    resource_id: OpenStruct.new(
                      channel_id: channel_id
                    )
                  )
                )
              ]
            )
          )
      end

      it 'returns true' do
        expect(service.verify_subscription?(channel_id, user_token)).to be true
      end
    end

    context 'when the user is not subscribed' do
      before do
        # Mock the YouTube API response with no matching channel
        allow_any_instance_of(Google::Apis::YoutubeV3::YouTubeService)
          .to receive(:list_subscriptions)
          .and_return(
            OpenStruct.new(
              items: [
                OpenStruct.new(
                  snippet: OpenStruct.new(
                    resource_id: OpenStruct.new(
                      channel_id: 'different_channel_id'
                    )
                  )
                )
              ]
            )
          )
      end

      it 'returns false' do
        expect(service.verify_subscription?(channel_id, user_token)).to be false
      end
    end

    context 'when the API call fails' do
      before do
        allow_any_instance_of(Google::Apis::YoutubeV3::YouTubeService)
          .to receive(:list_subscriptions)
          .and_raise(Google::Apis::Error.new('API error'))
      end

      it 'returns false and logs the error' do
        expect(Rails.logger).to receive(:error).with(/YouTube API error/)
        expect(service.verify_subscription?(channel_id, user_token)).to be false
      end
    end
  end
end
```

#### 5.2 Controller Tests

Test the league membership controller with subscription verification:

```ruby
# spec/controllers/api/v1/league_memberships_controller_spec.rb
require 'rails_helper'

RSpec.describe Api::V1::LeagueMembershipsController, type: :controller do
  let(:user) { create(:user) }
  let(:owner) { create(:user) }
  let(:competition) { create(:competition) }
  let(:season) { create(:season, competition: competition) }

  describe 'POST #create' do
    context 'with a subscriber-only league' do
      let(:league) do
        create(
          :league,
          owner: owner,
          competition: competition,
          season: season,
          youtube_league: true,
          youtube_channel_id: 'UC1234567890abcdef',
          subscriber_only: true
        )
      end

      before do
        sign_in user
      end

      context 'when the user meets subscription requirements' do
        before do
          allow_any_instance_of(League)
            .to receive(:user_meets_subscription_requirements?)
            .and_return(true)
        end

        it 'creates a membership' do
          expect {
            post :create, params: { league_id: league.id }
          }.to change(Membership, :count).by(1)

          expect(response).to have_http_status(:created)
          expect(Membership.last.subscription_status).to eq('active')
        end
      end

      context 'when the user does not meet subscription requirements' do
        before do
          allow_any_instance_of(League)
            .to receive(:user_meets_subscription_requirements?)
            .and_return(false)
        end

        it 'returns forbidden status' do
          expect {
            post :create, params: { league_id: league.id }
          }.not_to change(Membership, :count)

          expect(response).to have_http_status(:forbidden)
          expect(JSON.parse(response.body)).to have_key('error')
        end
      end
    end
  end
end
```

#### 5.3 Integration Tests

Test the full subscription verification flow:

```ruby
# spec/requests/youtube_subscription_spec.rb
require 'rails_helper'

RSpec.describe "YouTube Subscription Verification", type: :request do
  let(:user) { create(:user, youtube_credentials: 'valid_token') }
  let(:owner) { create(:user, is_content_creator: true, youtube_channel_id: 'UC1234567890abcdef') }
  let(:competition) { create(:competition) }
  let(:season) { create(:season, competition: competition) }
  let(:league) do
    create(
      :league,
      owner: owner,
      competition: competition,
      season: season,
      youtube_league: true,
      youtube_channel_id: owner.youtube_channel_id,
      subscriber_only: true
    )
  end

  before do
    sign_in user
  end

  context 'when joining a subscriber-only league' do
    context 'when the user is subscribed' do
      before do
        allow_any_instance_of(YoutubeService)
          .to receive(:verify_subscription?)
          .with(owner.youtube_channel_id, user.youtube_credentials)
          .and_return(true)
      end

      it 'allows the user to join the league' do
        post "/api/v1/leagues/#{league.id}/memberships"

        expect(response).to have_http_status(:created)
        expect(user.leagues).to include(league)
      end
    end

    context 'when the user is not subscribed' do
      before do
        allow_any_instance_of(YoutubeService)
          .to receive(:verify_subscription?)
          .with(owner.youtube_channel_id, user.youtube_credentials)
          .and_return(false)
      end

      it 'prevents the user from joining the league' do
        post "/api/v1/leagues/#{league.id}/memberships"

        expect(response).to have_http_status(:forbidden)
        expect(user.leagues).not_to include(league)
      end
    end
  end
end
```

### 6. Maintenance Considerations

#### 6.1 API Quota Management

YouTube API has quota limits that need to be managed carefully:

1. **Cache Subscription Status**: Cache subscription verification results for at least 24 hours
2. **Batch Processing**: Run subscription verification jobs during off-peak hours
3. **Exponential Backoff**: Implement retry logic with exponential backoff for API failures
4. **Quota Monitoring**: Set up alerts for approaching quota limits

#### 6.2 Security Considerations

1. **OAuth Token Storage**: Store YouTube OAuth tokens securely using encryption
2. **Regular Token Rotation**: Implement token refresh and rotation
3. **Scope Limitation**: Request only the minimum required OAuth scopes
4. **Rate Limiting**: Implement rate limiting for YouTube-related endpoints

#### 6.3 Error Handling

1. **Graceful Degradation**:

   - If YouTube API is unavailable, allow users to continue using the app
   - Services now gracefully handle external dependencies being unavailable
   - Added fallback mechanisms for caching and API calls

2. **User-Friendly Messages**:

   - Provide clear error messages when subscription verification fails
   - Added specific error handling for common scenarios like:
     - YouTube channel already connected to another account (409 Conflict)
     - Redis connection issues
     - API quota exceeded

3. **Comprehensive Logging**:

   - Log all YouTube API interactions for troubleshooting
   - Added detailed logging throughout the YouTube integration
   - Better error messages to help diagnose issues

4. **Resilient Services**:

   - Services now gracefully handle external dependencies being unavailable
   - Added fallback mechanisms for caching and API calls
   - Improved error recovery for API failures

5. **Monitoring**:
   - Set up monitoring for YouTube API errors and response times
   - Track API quota usage to prevent exceeding limits

## Implementation

### 1. OAuth Configuration

```ruby
# config/initializers/devise.rb
Devise.setup do |config|
  config.omniauth :google_oauth2,
    ENV['GOOGLE_CLIENT_ID'],
    ENV['GOOGLE_CLIENT_SECRET'],
    {
      scope: 'email,profile,youtube.readonly',
      prompt: 'select_account',
      image_aspect_ratio: 'square',
      image_size: 50,
      access_type: 'offline'
    }
end
```

### 2. Routes Configuration

```ruby
# config/routes.rb
Rails.application.routes.draw do
  # YouTube OAuth routes (not used for login/signup; FE posts code directly to BE)
  get '/auth/youtube/callback', to: 'api/v1/youtube_auth#callback'

  namespace :api do
    namespace :v1 do
      resources :youtube_auth, only: [] do
        collection do
          post 'connect'
          post 'disconnect'
        end
      end

      resources :youtube do
        collection do
          # Creator + subscription endpoints exist but are gated by ENV in production
          post 'update_creator_status'
          get 'check_subscriber_league_eligibility'
          get 'subscription_status'
        end
      end

      resources :youtube_leagues
    end
  end
end
```

### 3. Controllers Implementation

```ruby
# app/controllers/api/v1/youtube_auth_controller.rb
module Api
  module V1
    class YoutubeAuthController < ApplicationController
      before_action :authenticate_devise_api_token!, only: [:connect, :disconnect]

      def create
        # Implementation as shown in previous response
      end

      def connect
        # Implementation as shown in previous response
      end

      def disconnect
        # Implementation as shown in previous response
      end
    end
  end
end
```

### 4. Frontend Implementation

```typescript
// app/javascript/components/YoutubeAuth.tsx
// Implementation as shown in previous response
```

## API Endpoints

### YouTube Authentication

```
POST /api/v1/auth/youtube/login    # basic Google login (openid email profile)
POST /api/v1/auth/youtube/signup   # basic Google signup
GET  /api/v1/auth/youtube/callback # optional pass-through; FE posts code directly to BE endpoints above

# Connect flow (deferred; gated by YOUTUBE_CONNECT_ENABLED)
POST /api/v1/youtube_auth/connect
POST /api/v1/youtube_auth/disconnect
GET  /api/v1/youtube_auth/status
POST /api/v1/youtube_auth/verify_subscription
```

### YouTube Leagues

```
POST   /api/v1/youtube/leagues
GET    /api/v1/youtube/leagues
GET    /api/v1/youtube/leagues/:id
PATCH  /api/v1/youtube/leagues/:id
DELETE /api/v1/youtube/leagues/:id
```

### Subscriber Verification

```
POST /api/v1/youtube/verify_subscription
GET  /api/v1/youtube/subscription_status
```

#### Subscription Verification Flow

1. **User Attempts to Join a Subscriber-Only League**:

   - When a user tries to join a subscriber-only league, the system checks if they have connected their YouTube account
   - If not, they are prompted to connect their YouTube account first

2. **YouTube Account Connection**:

   - User is redirected to the YouTube OAuth flow
   - After authorization, the user's YouTube credentials are stored securely
   - The system verifies if the user is subscribed to the channel

3. **Subscription Verification**:

   - The system calls the YouTube API to check if the user is subscribed to the channel
   - Results are cached to minimize API calls and stay within quota limits
   - If verified, the user can join the league; if not, they receive a message to subscribe first

4. **Periodic Verification**:
   - For subscriber-only leagues, the system periodically verifies that members are still subscribed
   - Users who have unsubscribed may receive a grace period before being removed from the league

#### API Endpoints Details

**POST /api/v1/youtube/verify_subscription**

Request body:

```json
{
  "channel_id": "UC1234567890abcdef"
}
```

Response (success):

```json
{
  "status": "success",
  "subscribed": true,
  "message": "User is subscribed to this channel"
}
```

Response (not subscribed):

```json
{
  "status": "success",
  "subscribed": false,
  "message": "User is not subscribed to this channel"
}
```

**GET /api/v1/youtube/subscription_status**

Query parameters:

- `channel_id`: The YouTube channel ID to check subscription status for

Response:

```json
{
  "status": "success",
  "subscribed": true,
  "subscription_date": "2023-01-15T12:34:56Z",
  "channel": {
    "id": "UC1234567890abcdef",
    "name": "Channel Name"
  }
}
```

## Implementation Checklist

1. **Backend Setup**

   - [x] Configure OAuth2 authentication
   - [x] Set up YouTube API service
   - [x] Implement subscriber verification
   - [x] Create necessary controllers
   - [x] Implement error handling for YouTube API
   - [x] Add Redis connection error handling
   - [x] Implement conflict detection for YouTube channels
   - [ ] Set up background jobs for stats updates
   - [ ] Update League model with YouTube-related fields
   - [ ] Implement subscription verification in LeagueMembershipsController
   - [ ] Implement subscription verification in LeagueJoinRequestsController
   - [ ] Add periodic subscription verification job

2. **Frontend Requirements**

   - [x] YouTube connection flow
   - [x] Minimal permission scopes implementation
   - [x] Basic error handling for connection failures
   - [x] Test page for YouTube OAuth flow
   - [ ] Creator dashboard
   - [ ] Subscriber verification UI
   - [ ] League creation/management interface with subscriber-only options
   - [ ] YouTube-specific badges and indicators
   - [ ] User guidance for connecting YouTube accounts
   - [ ] Comprehensive error handling for subscription verification failures

3. **Testing Requirements**
   - [x] Basic OAuth flow testing
   - [x] Test script for YouTube API diagnostics
   - [x] Error handling tests
   - [ ] Comprehensive OAuth flow tests
   - [ ] YouTube API integration tests
   - [ ] Subscriber verification tests
   - [ ] League management tests
   - [ ] Rate limiting tests
   - [ ] Redis connection failure tests

## Rate Limiting and Caching

```ruby
# config/initializers/rack_attack.rb
class Rack::Attack
  throttle('youtube_api_requests', limit: 100, period: 1.day) do |req|
    if req.path.start_with?('/api/v1/youtube')
      req.ip
    end
  end
end
```

## YouTube API Service

```ruby
# app/services/brag_rights_you_tube_service.rb
class BragRightsYouTubeService
  def initialize(user = nil)
    @user = user
    @service = Google::Apis::YoutubeV3::YouTubeService.new
    @service.key = ENV['YOUTUBE_API_KEY']
  end

  def verify_subscription?(channel_id, subscriber_id)
    # Implementation
  end

  def get_channel_info(channel_id)
    # Implementation
  end

  def get_subscriber_count(channel_id)
    # Implementation
  end
end
```

## Security Considerations

1. **API Key Protection**

   - Store keys in credentials.yml.enc
   - Use environment variables
   - Implement API key rotation
   - Set up proper OAuth scopes

2. **Rate Limiting**

   - YouTube API quotas
   - Request throttling
   - Cache frequently accessed data

3. **Data Privacy**
   - Handle YouTube data according to GDPR
   - Implement data retention policies
   - Provide data export functionality
   - Secure OAuth tokens storage

## Caching Strategy

```ruby
# config/initializers/youtube_cache.rb
Rails.cache.fetch("youtube_channel_#{channel_id}", expires_in: 1.hour) do
  YoutubeService.new.get_channel_info(channel_id)
end
```

## Background Jobs

### YouTube Stats Update Job

```ruby
# app/jobs/youtube_stats_update_job.rb
class YoutubeStatsUpdateJob < ApplicationJob
  queue_as :youtube

  def perform(user_id)
    user = User.find(user_id)
    # Update YouTube statistics
  end
end
```

### Subscription Verification Job

This job periodically verifies that members of subscriber-only leagues are still subscribed to the respective YouTube channels:

```ruby
# app/jobs/subscription_verification_job.rb
class SubscriptionVerificationJob < ApplicationJob
  queue_as :youtube

  def perform
    # Find all subscriber-only leagues
    subscriber_only_leagues = League.where(subscriber_only: true, youtube_channel_id: present?)

    subscriber_only_leagues.each do |league|
      # Get all members of the league
      league.users.each do |user|
        # Skip users without YouTube credentials
        next unless user.youtube_credentials.present?

        # Check if user is still subscribed
        is_subscribed = BragRightsYouTubeService.new.verify_subscription?(
          league.youtube_channel_id,
          user.youtube_credentials
        )

        unless is_subscribed
          # Handle unsubscribed users based on league policy
          case league.unsubscribe_policy
          when 'remove_immediately'
            # Remove user from league
            league.memberships.find_by(user_id: user.id)&.destroy

            # Notify user
            UserMailer.subscription_required_email(user, league).deliver_later
          when 'grace_period'
            # Mark user as pending removal and set grace period
            membership = league.memberships.find_by(user_id: user.id)
            if membership
              membership.update(
                subscription_status: 'grace_period',
                grace_period_ends_at: Time.current + 7.days
              )

              # Notify user about grace period
              UserMailer.subscription_grace_period_email(user, league).deliver_later
            end
          end
        end
      end
    end

    # Also check users in grace period whose time has expired
    grace_period_memberships = Membership.where(
      subscription_status: 'grace_period',
      grace_period_ends_at: ..Time.current
    )

    grace_period_memberships.each do |membership|
      # Remove user from league if grace period has expired
      user = membership.user
      league = membership.league

      membership.destroy

      # Notify user
      UserMailer.removed_from_league_email(user, league).deliver_later
    end
  end
end
```

### Scheduling the Jobs

Configure the jobs to run on a schedule using Sidekiq Scheduler:

```ruby
# config/sidekiq.yml
:schedule:
  youtube_stats_update_job:
    cron: '0 0 * * *'  # Daily at midnight
    class: YoutubeStatsUpdateJob
    queue: youtube

  subscription_verification_job:
    cron: '0 0 * * 0'  # Weekly on Sunday at midnight
    class: SubscriptionVerificationJob
    queue: youtube
```

## Error Handling

```ruby
# app/controllers/concerns/youtube_error_handler.rb
module YoutubeErrorHandler
  extend ActiveSupport::Concern

  included do
    rescue_from Google::Apis::Error do |e|
      handle_youtube_error(e)
    end
  end

  private

  def handle_youtube_error(error)
    # Error handling implementation
  end
end
```

## Frontend Integration Examples

```typescript
// Example React component for YouTube connection
const YoutubeConnect: React.FC = () => {
  // Implementation
};

// Example YouTube league creation form
const YoutubeLeagueForm: React.FC = () => {
  // Implementation
};
```

## Monitoring and Analytics

1. **Metrics to Track**

   - Active YouTube creators
   - OAuth success/failure rates
   - Subscriber verification rates
   - API quota usage
   - Connection/disconnection events

2. **Logging**
   - OAuth flow events
   - YouTube API interactions
   - Authentication events
   - Subscription verifications

## Future Enhancements

1. **Planned Features**

   - YouTube community post integration
   - Live stream predictions
   - Creator analytics dashboard
   - Automated highlight clips

2. **Scalability Considerations**
   - API quota management
   - Cache optimization
   - Database indexing

## Support and Troubleshooting

Common issues and solutions:

1. OAuth state mismatch
2. API quota exceeded
3. Invalid credentials
4. Rate limiting
5. Subscription verification failures

## Additional Resources

- [YouTube Data API Documentation](https://developers.google.com/youtube/v3)
- [OAuth 2.0 Implementation Guide](https://developers.google.com/identity/protocols/oauth2)
- [API Quotas and Pricing](https://developers.google.com/youtube/v3/getting-started#quota)
