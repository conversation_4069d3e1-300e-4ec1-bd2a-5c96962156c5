# OAuth Verification Video Script (2–3 minutes, silent with text overlays)

Purpose: Demonstrate a subscriber league platform that uses YouTube OAuth for two cases:

1. Content creators sign in to verify they have 100+ subscribers (to enable league creation)
2. Regular users sign in to verify they are subscribed to specific channels (to join leagues)

Show: Legitimate purpose, requested scopes, client_id in URL, consent screen, and actual functionality.

---

[Scene 1 – 15–20s]
Screen Action: Open the landing page. Scroll past hero to the new "Creators" section.
Text Overlay: "Creators with 100+ subscribers can host leagues. Fans join by verifying their subscription."
Technical Note: No OAuth yet.

[Scene 2 – 45s]
Screen Action: Click "Sign in with YouTube" (creator path). <PERSON><PERSON><PERSON> navigates to Google OAuth screen.
Text Overlay: "Creator authenticates with Google"
Technical Note: Show the full OAuth URL in the browser bar briefly with client_id visible. Then, on the consent screen: show scopes requested.

- If verifying youtube.readonly now: consent screen shows access to view YouTube account data (read-only) plus basic profile.
- If verifying basic scopes first: run the connect flow later in Scene 3 to show youtube.readonly.

Click "Allow" (blue button). Redirect back to the site.

[Scene 3 – 60–75s]
Screen Action: Creator dashboard appears.

- Show channel name and subscriber count (≥100). (If mocked, label as "Test environment")
- Click "Create League" and complete a short form. Submit and see the new league listed.
- Open the league page to show it exists and is active.
  Text Overlay: "We read channel subscriber count to enable creator features (read-only)."
  Technical Note: Demonstrates use of youtube.readonly for creator eligibility.

[Scene 4 – 30–45s]
Screen Action: Open a new private/incognito window (regular user).

- Click "Sign in with YouTube"; approve basic login.
- Navigate to the creator's league; click "Join League".
- App prompts to verify subscription to the creator's channel; click "Verify".
- Show success state when subscription is confirmed.
  Text Overlay: "We read subscription status to allow subscribers to join leagues (read-only)."
  Technical Note: Demonstrates use of youtube.readonly for subscription verification.

[Scene 5 – 15–20s]
Screen Action: Navigate to the Privacy Policy and Terms pages from the footer.
Text Overlay: "Data is used only to verify channel ownership and subscription status. No publishing." and "See Privacy Policy for details."
Technical Note: Reinforces limited scope and compliance.

---

Appendix: On-screen checklist for reviewers (show briefly at end or in description)

- Client ID visible in OAuth URL
- Consent screen with scopes
- Creator: subscriber count check (≥100) and league creation
- User: subscription verification and league join
- Links to policy pages
