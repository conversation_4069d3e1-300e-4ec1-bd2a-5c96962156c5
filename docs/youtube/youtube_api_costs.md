# YouTube API Cost Considerations

This document outlines the cost considerations for implementing YouTube integration in the BragRights application, particularly for subscriber-only leagues.

> **Note:** This implementation focuses on **subscriber-only** leagues (requiring the free "Subscribe" action) rather than **members-only** leagues (requiring paid channel memberships). Members-only functionality may be considered as a future enhancement.

## YouTube Data API v3 Quota System

The YouTube Data API v3 uses a quota system to ensure fair usage across all applications. Understanding this quota system is crucial for planning your implementation and estimating costs.

### Daily Quota Allocation

- **Free Tier**: By default, each project receives 10,000 quota units per day
- **Paid Tier**: Additional quota can be purchased through Google Cloud Platform

### Quota Usage by API Method

Different API methods consume different amounts of quota. Here are the quota costs for the methods we use in our implementation:

| API Method           | Quota Cost (units) | Description                                         |
| -------------------- | ------------------ | --------------------------------------------------- |
| `subscriptions.list` | 100                | Used to verify if a user is subscribed to a channel |
| `channels.list`      | 1                  | Used to get information about a channel             |
| `videos.list`        | 1                  | Used to get information about videos                |
| `search.list`        | 100                | Used to search for channels or videos               |

### Quota Calculation Examples

For our subscriber-only leagues implementation, the primary quota usage comes from subscription verification:

1. **Single Subscription Verification**:

   - One call to `subscriptions.list`: 100 units
   - Total: 100 units per verification

2. **Weekly Verification Job for 1,000 Members**:

   - 1,000 calls to `subscriptions.list`: 100,000 units
   - This exceeds the daily free quota and would require a paid tier

3. **Channel Information Lookup**:
   - One call to `channels.list`: 1 unit
   - This is very inexpensive and can be done frequently

## Cost Estimation

### Free Tier Limitations

With the free tier (10,000 units per day), you can perform:

- 100 subscription verifications per day (100 units each)
- OR 10,000 channel information lookups (1 unit each)

### Paid Tier Costs

If you need additional quota:

1. **Google Cloud Platform Pricing**:

   - Additional quota costs $5 USD per 1,000,000 units
   - Minimum purchase of 100,000 units ($0.50)

2. **Monthly Cost Estimation**:

   - For 1,000 members with weekly verification: ~400,000 units per month
   - Estimated cost: ~$2.00 USD per month

3. **Scaling Considerations**:
   - 10,000 members with weekly verification: ~4,000,000 units per month
   - Estimated cost: ~$20.00 USD per month

## Cost Optimization Strategies

To minimize costs while maintaining functionality, we've implemented the following strategies:

### 1. Intelligent Caching

- Cache subscription status for 24 hours
- Cache channel information for 1 hour
- Cache user subscriptions for 6 hours
- This reduces the number of API calls needed for frequent verifications
- Implementation in our code:

  ```ruby
  # Subscription verification caching
  Rails.cache.fetch(cache_key, expires_in: 1.day) do
    # API call here
  end

  # Channel information caching
  Rails.cache.fetch(cache_key, expires_in: 1.hour) do
    # API call here
  end

  # User subscriptions caching
  Rails.cache.fetch(cache_key, expires_in: 6.hours) do
    # API call here
  end
  ```

### 2. Quota Monitoring and Circuit Breaking

- Track all API calls and their quota usage
- Prevent API calls when approaching daily quota limits
- Provide admin dashboard for monitoring quota usage
- Implementation:

  ```ruby
  # Check if we have enough quota before making the API call
  return false unless YoutubeQuotaService.can_make_api_call?('subscriptions.list', 100)

  # Track API usage after successful call
  YoutubeQuotaService.track_api_call('subscriptions.list', 100)
  ```

### 3. Tiered Verification Frequency

- Verify users based on their activity level:
  - Active users (active in last 7 days): verify weekly on Sundays
  - Semi-active users (active in last 30 days): verify weekly on Wednesdays
  - Inactive users (inactive for 30+ days): verify monthly on the 1st
- Implementation:

  ```ruby
  # Scheduled jobs in config/sidekiq_scheduler.yml
  subscription_verification_active_users:
    cron: "0 0 * * 0" # Weekly on Sunday at midnight
    class: SubscriptionVerificationJob
    queue: youtube
    args:
      - "active"

  subscription_verification_semi_active_users:
    cron: "0 0 * * 3" # Weekly on Wednesday at midnight
    class: SubscriptionVerificationJob
    queue: youtube
    args:
      - "semi_active"

  subscription_verification_inactive_users:
    cron: "0 0 1 * *" # Monthly on the 1st at midnight
    class: SubscriptionVerificationJob
    queue: youtube
    args:
      - "inactive"
  ```

### 4. Batch Processing

- Process users in batches to avoid memory issues
- Check quota availability before processing each batch
- Implementation:
  ```ruby
  # Process users in batches
  users.find_in_batches(batch_size: 50) do |batch|
    batch.each do |user|
      verify_user_subscriptions(user)
    end
  end
  ```

## Monitoring and Alerting

We've implemented the following monitoring and alerting features:

1. **Quota Monitoring Dashboard**:

   - Admin endpoint at `/api/v1/admin/youtube/quota_usage`
   - Shows current usage, daily limit, and remaining quota
   - Implementation:
     ```ruby
     # app/controllers/api/v1/admin/youtube_controller.rb
     def quota_usage
       render json: {
         current_usage: YoutubeQuotaService.get_current_usage,
         daily_limit: YoutubeQuotaService::DAILY_QUOTA_LIMIT,
         remaining: YoutubeQuotaService.get_remaining_quota,
         percentage_used: (YoutubeQuotaService.get_current_usage.to_f / YoutubeQuotaService::DAILY_QUOTA_LIMIT * 100).round(2)
       }
     end
     ```

2. **Automatic Circuit Breaking**:

   - Automatically prevents API calls when approaching quota limits
   - Includes a buffer to ensure we never exceed limits
   - Implementation:

     ```ruby
     def self.can_make_api_call?(method_name, units_needed)
       today = Date.today.to_s
       key = "youtube_api:usage:#{today}"

       # Get current usage
       current_usage = RedisConnection.redis.get(key).to_i

       # Check if we have enough quota remaining
       remaining = DAILY_QUOTA_LIMIT - current_usage
       can_proceed = remaining > (units_needed + QUOTA_BUFFER)

       unless can_proceed
         Rails.logger.warn "YouTube API quota nearly exhausted: #{current_usage}/#{DAILY_QUOTA_LIMIT} units. Blocking call to #{method_name} (#{units_needed} units)"
       end

       can_proceed
     end
     ```

3. **Detailed Logging**:
   - Log all API calls with quota usage
   - Log when API calls are blocked due to quota limits
   - Track which features consume the most quota
   - Implementation:
     ```ruby
     Rails.logger.info "YouTube API usage: #{current_usage}/#{DAILY_QUOTA_LIMIT} units (#{method_name}: +#{units_used})"
     ```

## Conclusion

The YouTube API integration for members-only leagues is very affordable for small to medium-sized applications. With proper optimization strategies, you can likely operate within the free tier limits until you reach thousands of active users.

For larger applications, the cost scales reasonably with usage, and the paid tier provides good value for the functionality it enables.

## References

- [YouTube Data API Quota Calculator](https://developers.google.com/youtube/v3/determine_quota_cost)
- [Google Cloud Platform Pricing](https://cloud.google.com/youtube/pricing)
- [YouTube API Quota Management Best Practices](https://developers.google.com/youtube/v3/guides/quota_and_compliance_audits)
