# YouTube API Cost Optimization Implementation

This document outlines the implementation of cost optimization strategies for YouTube API usage in the BragRights application, particularly for subscriber-only leagues.

## Current Implementation

We've implemented two key strategies to keep YouTube API costs down:

### 1. Quota Monitoring and Circuit Breaking System

The `YoutubeQuotaService` provides a comprehensive system for tracking and managing API quota usage:

```ruby
# app/services/youtube_quota_service.rb
class YoutubeQuotaService
  DAILY_QUOTA_LIMIT = ENV.fetch('YOUTUBE_API_DAILY_QUOTA', 10_000).to_i
  QUOTA_BUFFER = ENV.fetch('YOUTUBE_API_QUOTA_BUFFER', 1_000).to_i

  def self.track_api_call(method_name, units_used)
    today = Date.today.to_s
    key = "youtube_api:usage:#{today}"

    # Increment usage counter
    current_usage = RedisConnection.redis.incrby(key, units_used)

    # Set expiry to ensure counter resets
    RedisConnection.redis.expire(key, 48.hours)

    # Log usage
    Rails.logger.info "YouTube API usage: #{current_usage}/#{DAILY_QUOTA_LIMIT} units (#{method_name}: +#{units_used})"

    # Return current usage
    current_usage
  end

  def self.can_make_api_call?(method_name, units_needed)
    today = Date.today.to_s
    key = "youtube_api:usage:#{today}"

    # Get current usage
    current_usage = RedisConnection.redis.get(key).to_i

    # Check if we have enough quota remaining
    remaining = DAILY_QUOTA_LIMIT - current_usage
    can_proceed = remaining > (units_needed + QUOTA_BUFFER)

    unless can_proceed
      Rails.logger.warn "YouTube API quota nearly exhausted: #{current_usage}/#{DAILY_QUOTA_LIMIT} units. Blocking call to #{method_name} (#{units_needed} units)"
    end

    can_proceed
  end

  def self.get_current_usage
    today = Date.today.to_s
    key = "youtube_api:usage:#{today}"
    RedisConnection.redis.get(key).to_i
  end

  def self.get_remaining_quota
    DAILY_QUOTA_LIMIT - get_current_usage
  end

  def self.reset_usage
    # For testing purposes only
    today = Date.today.to_s
    RedisConnection.redis.del("youtube_api:usage:#{today}")
  end
end
```

All YouTube API calls have been updated to check quota availability before making calls and to track usage after successful calls:

```ruby
# Check if we have enough quota before making the API call
return false unless YoutubeQuotaService.can_make_api_call?('subscriptions.list', 100)

# Make the API call
response = @service.list_subscriptions(...)

# Track API usage
YoutubeQuotaService.track_api_call('subscriptions.list', 100)
```

An admin endpoint has been created to monitor quota usage:

```ruby
# app/controllers/api/v1/admin/youtube_controller.rb
def quota_usage
  render json: {
    current_usage: YoutubeQuotaService.get_current_usage,
    daily_limit: YoutubeQuotaService::DAILY_QUOTA_LIMIT,
    remaining: YoutubeQuotaService.get_remaining_quota,
    percentage_used: (YoutubeQuotaService.get_current_usage.to_f / YoutubeQuotaService::DAILY_QUOTA_LIMIT * 100).round(2)
  }
end
```

### 2. Tiered Verification Frequency

We've implemented a tiered approach to subscription verification based on user activity:

```ruby
# app/jobs/subscription_verification_job.rb
def perform(tier = nil)
  # Process grace period expirations regardless of tier
  process_expired_grace_periods

  # Only verify a specific tier if provided
  # This relies on scheduled jobs with explicit tier arguments
  verify_tier(tier) if tier
end

def verify_tier(tier)
  Rails.logger.info "Starting subscription verification for #{tier} users"

  # Get users to verify based on tier
  users = case tier.to_sym
          when :active
            # Users active in the last 7 days
            User.where('last_active_at > ?', 7.days.ago)
          when :semi_active
            # Users active in the last 30 days but not in the last 7 days
            User.where('last_active_at > ? AND last_active_at <= ?', 30.days.ago, 7.days.ago)
          when :inactive
            # Users not active in the last 30 days
            User.where('last_active_at <= ? OR last_active_at IS NULL', 30.days.ago)
          else
            User.none
          end

  # Only verify users with YouTube credentials
  users = users.where.not(youtube_credentials: nil)

  # Check if we have enough quota for all verifications
  total_quota_needed = users.count * 100 # 100 units per verification
  if users.count.positive? && !YoutubeQuotaService.can_make_api_call?("batch_verification_#{tier}", total_quota_needed)
    Rails.logger.error "Not enough quota to verify all #{tier} users. Needed: #{total_quota_needed}"
    return
  end

  # Process users in batches to avoid memory issues
  users.find_in_batches(batch_size: 50) do |batch|
    batch.each do |user|
      verify_user_subscriptions(user)
    end
  end
end
```

The Sidekiq scheduler has been updated to run verification jobs on different schedules:

```yaml
# config/sidekiq_scheduler.yml
subscription_verification_active_users:
  cron: "0 0 * * 0" # Weekly on Sunday at midnight
  class: SubscriptionVerificationJob
  queue: youtube
  args:
    - "active"
  description: "Weekly job to verify YouTube subscriptions for active users"

subscription_verification_semi_active_users:
  cron: "0 0 * * 3" # Weekly on Wednesday at midnight
  class: SubscriptionVerificationJob
  queue: youtube
  args:
    - "semi_active"
  description: "Weekly job to verify YouTube subscriptions for semi-active users"

subscription_verification_inactive_users:
  cron: "0 0 1 * *" # Monthly on the 1st at midnight
  class: SubscriptionVerificationJob
  queue: youtube
  args:
    - "inactive"
  description: "Monthly job to verify YouTube subscriptions for inactive users"
```

User activity is tracked using the `UserActivityTracker` concern:

```ruby
# app/controllers/concerns/user_activity_tracker.rb
module UserActivityTracker
  extend ActiveSupport::Concern

  included do
    before_action :track_user_activity
  end

  private

  def track_user_activity
    return unless current_devise_api_user

    # Only update if last activity was more than 30 minutes ago
    if current_devise_api_user.last_active_at.nil? ||
       current_devise_api_user.last_active_at < 30.minutes.ago

      current_devise_api_user.update_column(:last_active_at, Time.current)
    end
  end
end
```

### 3. Intelligent Caching

We've implemented caching for all YouTube API calls:

```ruby
# Subscription verification caching (24 hours)
Rails.cache.fetch(cache_key, expires_in: 1.day) do
  # API call here
end

# Channel information caching (1 hour)
Rails.cache.fetch(cache_key, expires_in: 1.hour) do
  # API call here
end

# User subscriptions caching (6 hours)
Rails.cache.fetch(cache_key, expires_in: 6.hours) do
  # API call here
end
```

## Future Improvements

While the current implementation significantly reduces API usage, there are several additional strategies that could be implemented in the future:

### 1. User-Initiated Verification

Instead of automatically verifying all users, implement a user-initiated verification process:

```ruby
# app/controllers/api/v1/youtube_controller.rb
def verify_my_subscriptions
  # Only allow once per day per user
  cache_key = "user_initiated_verification:#{current_devise_api_user.id}"

  if Rails.cache.exist?(cache_key)
    render json: { error: 'Verification already performed today' }, status: :too_many_requests
    return
  end

  # Get user's leagues that require subscription
  leagues = League.subscriber_only.joins(:memberships).where(memberships: { user_id: current_devise_api_user.id })

  results = {}
  leagues.each do |league|
    is_subscribed = BragRightsYouTubeService.new(current_devise_api_user).verify_subscription?(league.youtube_channel_id)

    membership = current_devise_api_user.memberships.find_by(league: league)
    if is_subscribed
      membership.update(subscription_status: 'active', subscription_verified_at: Time.current)
      results[league.id] = { status: 'subscribed', league_name: league.name }
    else
      # Handle unsubscribed user
      if league.unsubscribe_policy == 'remove_immediately'
        membership.destroy
        results[league.id] = { status: 'removed', league_name: league.name }
      else
        membership.update(subscription_status: 'grace_period', grace_period_ends_at: Time.current + 7.days)
        results[league.id] = { status: 'grace_period', league_name: league.name, grace_period_ends_at: membership.grace_period_ends_at }
      end
    end
  end

  # Set cache to prevent frequent verification
  Rails.cache.write(cache_key, true, expires_in: 1.day)

  render json: { results: results }
end
```

This puts users in control of verification, reducing unnecessary API calls.

### 2. Implement Batch API Requests

Use the YouTube API's batch request feature to combine multiple API calls:

```ruby
# app/services/youtube_batch_service.rb
class YoutubeBatchService
  def initialize
    @service = Google::Apis::YouTubeV3::YouTubeService.new
    @service.key = ENV['YOUTUBE_API_KEY']
    @batch = Google::Apis::BatchRequest.new
  end

  def add_channel_request(channel_id, callback)
    @batch.add(
      @service.list_channels_request('snippet,statistics', id: channel_id),
      callback: callback
    )
  end

  def execute
    @service.execute(@batch)
  end
end
```

Usage example:

```ruby
batch = YoutubeBatchService.new

# Add multiple channel requests
channel_ids = ['UC123', 'UC456', 'UC789']
results = {}

channel_ids.each do |channel_id|
  batch.add_channel_request(channel_id) do |response, error|
    if error
      results[channel_id] = { error: error.message }
    else
      channel = response.items.first
      results[channel_id] = {
        title: channel.snippet.title,
        subscriber_count: channel.statistics.subscriber_count
      }
    end
  end
end

# Execute all requests in a single HTTP request
batch.execute
```

This reduces HTTP overhead and can be more efficient for multiple requests.

### 3. Implement Adaptive Cache Duration

Adjust cache duration based on channel size and activity level:

```ruby
# app/services/youtube_service.rb
def get_channel_info(channel_id)
  # Determine cache duration based on channel size
  cache_duration = determine_cache_duration(channel_id)

  cache_key = "youtube_channel_#{channel_id}"

  Rails.cache.fetch(cache_key, expires_in: cache_duration) do
    # API call...
  end
end

private

def determine_cache_duration(channel_id)
  # Check if we have previous info about this channel
  previous_info = Rails.cache.read("youtube_channel_#{channel_id}")

  if previous_info
    subscriber_count = previous_info[:subscriber_count].to_i

    # Large channels change less frequently percentage-wise
    if subscriber_count > 1_000_000
      return 24.hours
    elsif subscriber_count > 100_000
      return 12.hours
    elsif subscriber_count > 10_000
      return 6.hours
    else
      return 3.hours
    end
  end

  # Default duration if no previous info
  6.hours
end
```

This optimizes cache duration based on how frequently channel information is likely to change.

### 4. Implement Subscription Webhooks

Instead of polling for subscription status, implement webhooks to receive notifications when users subscribe or unsubscribe:

```ruby
# app/controllers/api/v1/youtube_webhooks_controller.rb
class Api::V1::YoutubeWebhooksController < ApplicationController
  skip_before_action :verify_authenticity_token

  def subscription_update
    # Verify webhook signature
    return head :unauthorized unless verify_signature

    # Parse notification
    data = JSON.parse(request.body.read)

    if data['feed']['entry']
      entry = data['feed']['entry']
      channel_id = entry['yt:channelId']
      user_id = extract_user_id(entry)
      action = entry['title'] == 'Subscription added' ? :subscribe : :unsubscribe

      # Update subscription status
      update_subscription_status(user_id, channel_id, action)
    end

    head :ok
  end

  private

  def verify_signature
    # Implement signature verification
  end

  def extract_user_id(entry)
    # Extract user ID from entry
  end

  def update_subscription_status(user_id, channel_id, action)
    user = User.find_by(id: user_id)
    return unless user

    leagues = League.where(youtube_channel_id: channel_id, subscriber_only: true)

    leagues.each do |league|
      membership = Membership.find_by(user: user, league: league)

      if action == :subscribe && !membership
        # User subscribed, add to league if they requested it
        join_request = LeagueJoinRequest.find_by(user: user, league: league)
        if join_request
          Membership.create(user: user, league: league, subscription_status: 'active', subscription_verified_at: Time.current)
          join_request.destroy
        end
      elsif action == :unsubscribe && membership
        # User unsubscribed, handle according to league policy
        if league.unsubscribe_policy == 'remove_immediately'
          membership.destroy
        else
          membership.update(subscription_status: 'grace_period', grace_period_ends_at: Time.current + 7.days)
        end
      end
    end
  end
end
```

This eliminates the need for periodic verification, drastically reducing API usage.

### 5. Implement Quota Alerts

Set up alerts when approaching quota limits:

```ruby
# app/services/youtube_quota_service.rb
def self.check_quota_threshold
  current_usage = get_current_usage
  percentage_used = (current_usage.to_f / DAILY_QUOTA_LIMIT * 100).round(2)

  # Define thresholds
  thresholds = [50, 75, 90, 95]

  # Check if we've crossed any thresholds
  thresholds.each do |threshold|
    threshold_key = "youtube_api:threshold:#{threshold}:#{Date.today}"

    if percentage_used >= threshold && !RedisConnection.redis.exists?(threshold_key)
      # Mark this threshold as notified
      RedisConnection.redis.setex(threshold_key, 24.hours.to_i, 'notified')

      # Send notification
      AdminMailer.quota_threshold_reached(threshold, current_usage, DAILY_QUOTA_LIMIT).deliver_later

      # Log the threshold crossing
      Rails.logger.warn "YouTube API quota threshold crossed: #{threshold}% (#{current_usage}/#{DAILY_QUOTA_LIMIT})"
    end
  end
end
```

Call this method after tracking API usage:

```ruby
def self.track_api_call(method_name, units_used)
  # Existing implementation...

  # Check quota thresholds
  check_quota_threshold

  current_usage
end
```

### 6. Implement Quota Distribution

Distribute quota usage evenly throughout the day:

```ruby
# app/services/youtube_quota_service.rb
def self.can_make_api_call?(method_name, units_needed)
  today = Date.today.to_s
  key = "youtube_api:usage:#{today}"

  # Get current usage
  current_usage = RedisConnection.redis.get(key).to_i

  # Calculate time-based quota limit
  hours_passed = (Time.now.to_f - Time.now.beginning_of_day.to_f) / 3600
  percentage_of_day = [hours_passed / 24, 1.0].min
  time_based_limit = (DAILY_QUOTA_LIMIT * percentage_of_day).ceil

  # Add buffer to time-based limit
  time_based_limit += QUOTA_BUFFER

  # Check if we have enough quota remaining
  can_proceed = current_usage < time_based_limit

  unless can_proceed
    Rails.logger.warn "YouTube API quota usage ahead of schedule: #{current_usage}/#{time_based_limit} units. Blocking call to #{method_name} (#{units_needed} units)"
  end

  can_proceed
end
```

This ensures that quota is used evenly throughout the day, preventing early exhaustion.

### 7. Implement Quota Prioritization

Prioritize critical API calls over less important ones:

```ruby
# app/services/youtube_quota_service.rb
def self.can_make_api_call?(method_name, units_needed, priority = :normal)
  today = Date.today.to_s
  key = "youtube_api:usage:#{today}"

  # Get current usage
  current_usage = RedisConnection.redis.get(key).to_i

  # Calculate remaining quota
  remaining = DAILY_QUOTA_LIMIT - current_usage

  # Define priority thresholds
  thresholds = {
    high: 0.1,     # Allow high priority calls until 90% used
    normal: 0.2,   # Allow normal priority calls until 80% used
    low: 0.3       # Allow low priority calls until 70% used
  }

  # Check if we have enough quota remaining based on priority
  threshold = thresholds[priority] || thresholds[:normal]
  buffer = DAILY_QUOTA_LIMIT * threshold

  can_proceed = remaining > (units_needed + buffer)

  unless can_proceed
    Rails.logger.warn "YouTube API quota nearly exhausted for #{priority} priority: #{current_usage}/#{DAILY_QUOTA_LIMIT} units. Blocking call to #{method_name} (#{units_needed} units)"
  end

  can_proceed
end
```

Usage example:

```ruby
# High priority call (subscription verification)
YoutubeQuotaService.can_make_api_call?('subscriptions.list', 100, :high)

# Normal priority call (channel info)
YoutubeQuotaService.can_make_api_call?('channels.list', 1, :normal)

# Low priority call (search)
YoutubeQuotaService.can_make_api_call?('search.list', 100, :low)
```

This ensures that critical API calls can still be made even when approaching quota limits.

## Implementation Priority

If you want to implement these future improvements, we recommend the following priority order:

1. **User-Initiated Verification** - Highest impact for lowest effort
2. **Quota Alerts** - Critical for preventing unexpected costs
3. **Adaptive Cache Duration** - Good balance of impact and implementation ease
4. **Batch API Requests** - Significant optimization for multiple requests
5. **Quota Distribution** - Fine-tuning optimization
6. **Quota Prioritization** - Advanced optimization for critical operations
7. **Subscription Webhooks** - Most complex but potentially most efficient

## Conclusion

The current implementation significantly reduces YouTube API usage through quota monitoring, circuit breaking, and tiered verification. The future improvements outlined in this document can further optimize API usage if needed.

By implementing these strategies, you can ensure that your application stays within the free tier limits for as long as possible, and scales efficiently as your user base grows.
