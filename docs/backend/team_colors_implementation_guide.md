# Team Colors Implementation Guide

This document provides guidelines for implementing team colors in the Brag Rights application.

## Overview

Team colors are an important visual element in the application, used to identify teams in matches, standings, and other UI components. The application needs to store and display team colors consistently across different parts of the system.

## Color Storage Format

### New Object Format (Preferred)

Team club colors should be stored as an object with the following structure:

```json
{
  "home": {
    "primary": "#FF0000",
    "secondary": "#FFFFFF",
    "tertiary": "#000000"  // Optional
  },
  "away": {
    "primary": "#0000FF",
    "secondary": "#FFFFFF",
    "tertiary": "#000000"  // Optional
  }
}
```

Key points:
- All colors should be stored as hex values
- The `tertiary` color is optional and should only be included if it differs from both primary and secondary
- Both `home` and `away` objects are required

### Legacy String Format (Deprecated)

The legacy format stores colors as a string with 1-3 colors separated by '/' characters:

```
"Dark Blue / Yellow"
```

This format is deprecated and should be converted to the new object format when possible.

## Color Name Mapping

The application includes a mapping of color names to hex values. The following special color names have been added to the `Team` model's `COLOR_NAMES_TO_HEX` constant:

| Color Name | Hex Value |
|------------|-----------|
| claret     | #7F1734   |
| burgundy   | #800020   |
| amber      | #FFBF00   |
| navyblue   | #000080   |

## Handling Icelandic Team Colors

For Icelandic teams, use the following mapping:
- 'Peysa' (jersey) for primary color
- 'Buxur' (shorts) for secondary color
- 'Sokkar' (socks) for tertiary color (skip if it matches primary or secondary)

See the [Icelandic Team Colors Guide](icelandic_team_colors.md) for specific team color information.

## Color Conflict Resolution

When displaying team colors for a match, if the away team's primary color is similar to the home team's primary color, use the away team's away colors instead.

Example implementation:

```ruby
def resolve_color_conflict(home_team, away_team)
  # Check if primary colors are similar
  if similar_colors?(home_team.club_colors.home.primary, away_team.club_colors.home.primary)
    # Use away team's away colors
    return {
      home_team_colors: home_team.club_colors.home,
      away_team_colors: away_team.club_colors.away
    }
  else
    # Use both teams' home colors
    return {
      home_team_colors: home_team.club_colors.home,
      away_team_colors: away_team.club_colors.home
    }
  end
end

def similar_colors?(color1, color2)
  # Implementation to check if colors are similar
  # This could use color distance algorithms like CIEDE2000
end
```

## API Serialization

Serializers should consistently use `club_colors` rather than `clubColors` for team color attributes to maintain API consistency.

Example serializer:

```ruby
class TeamSerializer < ActiveModel::Serializer
  attributes :id, :name, :short_name, :tla, :crest_url, :club_colors

  def club_colors
    # Return the object format
    object.club_colors_object
  end
end
```

## Updating Colors from External Services

When updating team data from external services like FootballDataService, the `club_colors` attribute may need manual updates to convert from the string format to the object format.

Example implementation:

```ruby
def update_team_colors(team, external_data)
  if external_data['clubColors'].present?
    # Convert from string format to object format
    colors_array = external_data['clubColors'].split(' / ')
    
    team.club_colors = {
      home: {
        primary: convert_color_name_to_hex(colors_array[0]),
        secondary: convert_color_name_to_hex(colors_array[1]),
        tertiary: colors_array[2].present? ? convert_color_name_to_hex(colors_array[2]) : nil
      },
      away: {
        # Default away colors if not available
        primary: "#FFFFFF",
        secondary: "#000000"
      }
    }
  end
end

def convert_color_name_to_hex(color_name)
  # Look up color name in the COLOR_NAMES_TO_HEX mapping
  Team::COLOR_NAMES_TO_HEX[color_name.downcase] || color_name
end
```

## Frontend Considerations

The frontend should:
1. Always expect team colors in the object format
2. Have fallback colors in case team colors are missing
3. Handle color conflicts for better visual distinction between teams
4. Use appropriate contrast for text displayed on top of team colors

## Testing

When testing team color functionality:
1. Test both the new object format and legacy string format
2. Verify color conflict resolution works correctly
3. Test special color name mapping
4. Ensure serializers output the correct format

## Migration Plan

To migrate from the legacy string format to the new object format:
1. Update the Team model to support both formats
2. Add a migration task to convert existing string format to object format
3. Update serializers to always return the object format
4. Update frontend to expect the object format
5. Eventually remove support for the legacy string format
