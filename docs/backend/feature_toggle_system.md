# Backend Feature Toggle System

This document outlines the implementation of a server-side feature toggle system to replace the current localStorage-based approach.

## Overview

The backend feature toggle system provides a centralized way to manage feature flags across the application. Unlike the current localStorage implementation, this approach allows for:

- Centralized management of feature toggles
- Consistent feature availability across devices
- User or group-specific feature targeting
- Secure toggle management (users can't modify toggles)
- Persistence across browser sessions

## Architecture

The system consists of:

- **FeatureToggle Model**: Database representation of feature toggles
- **API Endpoints**: For retrieving and managing toggles
- **Admin Interface**: For toggle administration
- **Frontend Integration**: Updated components to use backend toggles

## Database Schema

Feature toggles are stored in a `feature_toggles` table with the following structure:

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Primary key |
| `name` | string | Unique feature name |
| `enabled` | boolean | Whether the feature is enabled |
| `description` | text | Description of the feature |
| `scope` | string | Scope of the toggle (global, user_group, user) |
| `scope_id` | integer | ID of the user or group if scoped |
| `created_at` | datetime | Creation timestamp |
| `updated_at` | datetime | Last update timestamp |

## Implementation

### 1. Database Migration

```ruby
class CreateFeatureToggles < ActiveRecord::Migration[7.0]
  def change
    create_table :feature_toggles do |t|
      t.string :name, null: false
      t.boolean :enabled, default: false, null: false
      t.text :description
      t.string :scope, default: 'global', null: false
      t.integer :scope_id
      
      t.timestamps
    end
    
    add_index :feature_toggles, :name
    add_index :feature_toggles, [:scope, :scope_id]
  end
end
```

### 2. Model

```ruby
# app/models/feature_toggle.rb
class FeatureToggle < ApplicationRecord
  SCOPES = %w[global user_group user].freeze
  
  validates :name, presence: true, uniqueness: { scope: [:scope, :scope_id] }
  validates :scope, inclusion: { in: SCOPES }
  validates :scope_id, presence: true, if: -> { scope != 'global' }
  
  scope :global, -> { where(scope: 'global') }
  scope :for_user, ->(user_id) { where(scope: 'user', scope_id: user_id) }
  scope :enabled, -> { where(enabled: true) }
  
  # Check if a feature is enabled for a specific user
  def self.enabled?(name, user_id = nil)
    # Check user-specific toggle first
    return true if user_id.present? && exists?(name: name, scope: 'user', scope_id: user_id, enabled: true)
    
    # Then check global toggle
    exists?(name: name, scope: 'global', enabled: true)
  end
  
  # Get all toggles applicable to a user
  def self.for_user_with_globals(user_id)
    global_toggles = global.to_a
    user_toggles = for_user(user_id).to_a
    
    # Merge toggles, with user-specific overriding global
    all_toggles = global_toggles.index_by(&:name)
    user_toggles.each { |toggle| all_toggles[toggle.name] = toggle }
    
    all_toggles.values
  end
end
```

### 3. Controllers

#### User-facing Controller

```ruby
# app/controllers/api/v1/feature_toggles_controller.rb
module Api
  module V1
    class FeatureTogglesController < ApplicationController
      before_action :authenticate_devise_api_token!
      
      def user
        toggles = FeatureToggle.for_user_with_globals(current_user.id)
        
        render json: {
          data: ActiveModelSerializers::SerializableResource.new(
            toggles, 
            each_serializer: FeatureToggleSerializer
          ),
          message: ['Feature toggles retrieved successfully'],
          status: 200,
          type: 'success'
        }
      end
    end
  end
end
```

#### Admin Controller

```ruby
# app/controllers/api/v1/admin/feature_toggles_controller.rb
module Api
  module V1
    module Admin
      class FeatureTogglesController < ApplicationController
        before_action :authenticate_devise_api_token!
        before_action :authenticate_admin!
        before_action :set_feature_toggle, only: [:show, :update, :destroy]
        
        def index
          toggles = FeatureToggle.all
          render json: {
            data: ActiveModelSerializers::SerializableResource.new(
              toggles, 
              each_serializer: FeatureToggleAdminSerializer
            ),
            message: ['Feature toggles found'],
            status: 200,
            type: 'success'
          }
        end
        
        def show
          render json: {
            data: ActiveModelSerializers::SerializableResource.new(
              @feature_toggle, 
              serializer: FeatureToggleAdminSerializer
            ),
            message: ['Feature toggle found'],
            status: 200,
            type: 'success'
          }
        end
        
        def create
          toggle = FeatureToggle.new(feature_toggle_params)
          
          if toggle.save
            render json: {
              data: ActiveModelSerializers::SerializableResource.new(
                toggle, 
                serializer: FeatureToggleAdminSerializer
              ),
              message: ['Feature toggle created successfully'],
              status: 201,
              type: 'success'
            }, status: :created
          else
            render json: {
              errors: toggle.errors.full_messages,
              status: 422
            }, status: :unprocessable_entity
          end
        end
        
        def update
          if @feature_toggle.update(feature_toggle_params)
            render json: {
              data: ActiveModelSerializers::SerializableResource.new(
                @feature_toggle, 
                serializer: FeatureToggleAdminSerializer
              ),
              message: ['Feature toggle updated successfully'],
              status: 200,
              type: 'success'
            }
          else
            render json: {
              errors: @feature_toggle.errors.full_messages,
              status: 422
            }, status: :unprocessable_entity
          end
        end
        
        def destroy
          @feature_toggle.destroy
          render json: {
            message: ['Feature toggle deleted successfully'],
            status: 200,
            type: 'success'
          }
        end
        
        private
        
        def set_feature_toggle
          @feature_toggle = FeatureToggle.find(params[:id])
        end
        
        def feature_toggle_params
          params.require(:feature_toggle).permit(:name, :enabled, :description, :scope, :scope_id)
        end
      end
    end
  end
end
```

### 4. Serializers

```ruby
# app/serializers/feature_toggle_serializer.rb
class FeatureToggleSerializer < ActiveModel::Serializer
  attributes :name, :enabled
end

# app/serializers/feature_toggle_admin_serializer.rb
class FeatureToggleAdminSerializer < ActiveModel::Serializer
  attributes :id, :name, :enabled, :description, :scope, :scope_id, :created_at, :updated_at
end
```

### 5. Routes

```ruby
# config/routes.rb (partial)
Rails.application.routes.draw do
  namespace :api do
    namespace :v1 do
      resources :feature_toggles, only: [] do
        collection do
          get 'user'
        end
      end
      
      namespace :admin do
        resources :feature_toggles
      end
    end
  end
end
```

### 6. Service Object (Optional)

For more complex toggle logic, you might want to create a service object:

```ruby
# app/services/feature_toggle_service.rb
class FeatureToggleService
  def initialize(user = nil)
    @user = user
  end
  
  def enabled?(feature_name)
    return false unless feature_name.present?
    
    user_id = @user&.id
    FeatureToggle.enabled?(feature_name, user_id)
  end
  
  def all_toggles
    user_id = @user&.id
    toggles = FeatureToggle.for_user_with_globals(user_id)
    toggles.index_by(&:name).transform_values(&:enabled)
  end
  
  def enable_for_user(feature_name, user_id)
    toggle = FeatureToggle.find_or_initialize_by(
      name: feature_name,
      scope: 'user',
      scope_id: user_id
    )
    toggle.enabled = true
    toggle.save
  end
  
  def disable_for_user(feature_name, user_id)
    toggle = FeatureToggle.find_or_initialize_by(
      name: feature_name,
      scope: 'user',
      scope_id: user_id
    )
    toggle.enabled = false
    toggle.save
  end
end
```

## Frontend Integration

### 1. API Client

```typescript
// src/api/featureToggles.ts
import { api } from '@/src/utils/api';

export interface FeatureToggle {
  name: string;
  enabled: boolean;
}

export const fetchUserToggles = async (): Promise<Record<string, boolean>> => {
  try {
    const response = await api.get('/api/v1/feature_toggles/user');
    const toggles = response.data.data;
    
    // Convert to a simple name -> enabled map
    return toggles.reduce((acc: Record<string, boolean>, toggle: FeatureToggle) => {
      acc[toggle.name] = toggle.enabled;
      return acc;
    }, {});
  } catch (error) {
    console.error('Failed to fetch feature toggles:', error);
    return {};
  }
};
```

### 2. Feature Toggle Hook

```typescript
// src/hooks/use-feature-toggle.ts
import { useEffect, useState, useCallback } from 'react';
import { fetchUserToggles } from '@/src/api/featureToggles';

export function useFeatureToggle(featureName?: string) {
  const [toggles, setToggles] = useState<Record<string, boolean>>({});
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const loadToggles = async () => {
      setLoading(true);
      try {
        const toggleData = await fetchUserToggles();
        setToggles(toggleData);
      } finally {
        setLoading(false);
      }
    };
    
    loadToggles();
  }, []);
  
  const isEnabled = useCallback(
    (name: string) => toggles[name] === true,
    [toggles]
  );
  
  // For backward compatibility during migration
  const allFeatures = toggles;
  
  if (featureName) {
    return {
      enabled: isEnabled(featureName),
      loading,
    };
  }
  
  return {
    isEnabled,
    allFeatures,
    loading,
  };
}
```

### 3. Feature Toggle Provider

```typescript
// src/utils/providers/FeatureToggleProvider.tsx
import React, { createContext, useContext, useEffect, useState } from 'react';
import { fetchUserToggles } from '@/src/api/featureToggles';

interface FeatureToggleContextType {
  isEnabled: (featureName: string) => boolean;
  getAllFeatures: () => Record<string, boolean>;
  loading: boolean;
}

const FeatureToggleContext = createContext<FeatureToggleContextType>({
  isEnabled: () => false,
  getAllFeatures: () => ({}),
  loading: true,
});

export const useFeatureToggleContext = () => useContext(FeatureToggleContext);

export const FeatureToggleProvider: React.FC<{ children: React.ReactNode }> = ({ 
  children 
}) => {
  const [toggles, setToggles] = useState<Record<string, boolean>>({});
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const loadToggles = async () => {
      try {
        const toggleData = await fetchUserToggles();
        setToggles(toggleData);
      } catch (error) {
        console.error('Failed to load feature toggles:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadToggles();
  }, []);
  
  const isEnabled = (featureName: string) => toggles[featureName] === true;
  
  const getAllFeatures = () => toggles;
  
  return (
    <FeatureToggleContext.Provider value={{ isEnabled, getAllFeatures, loading }}>
      {children}
    </FeatureToggleContext.Provider>
  );
};
```

### 4. Feature Toggle Component

```typescript
// src/components/feature-toggle/feature-toggle.tsx
import React from 'react';
import { useFeatureToggleContext } from '@/src/utils/providers/FeatureToggleProvider';

interface FeatureToggleProps {
  featureName: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  inverted?: boolean;
}

const FeatureToggle: React.FC<FeatureToggleProps> = ({
  featureName,
  children,
  fallback = null,
  inverted = false,
}) => {
  const { isEnabled, loading } = useFeatureToggleContext();
  
  if (loading) {
    return null; // Or a loading indicator if appropriate
  }
  
  const enabled = isEnabled(featureName);
  const shouldRender = inverted ? !enabled : enabled;
  
  return shouldRender ? <>{children}</> : <>{fallback}</>;
};

export default FeatureToggle;
```

## Admin Interface

### 1. Feature Toggle Admin Component

```typescript
// src/components/admin/feature-toggle-admin.tsx
import React, { useEffect, useState } from 'react';
import { api } from '@/src/utils/api';

interface FeatureToggle {
  id: number;
  name: string;
  enabled: boolean;
  description: string;
  scope: string;
  scope_id: number | null;
  created_at: string;
  updated_at: string;
}

interface FeatureToggleAdminProps {
  title?: string;
  description?: string;
}

const FeatureToggleAdmin: React.FC<FeatureToggleAdminProps> = ({
  title = 'Feature Toggles',
  description = 'Manage feature toggles for the application',
}) => {
  const [toggles, setToggles] = useState<FeatureToggle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Form state for creating new toggles
  const [newToggle, setNewToggle] = useState({
    name: '',
    enabled: false,
    description: '',
    scope: 'global',
    scope_id: null as number | null,
  });
  
  const fetchToggles = async () => {
    setLoading(true);
    try {
      const response = await api.get('/api/v1/admin/feature_toggles');
      setToggles(response.data.data);
      setError(null);
    } catch (err) {
      setError('Failed to load feature toggles');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    fetchToggles();
  }, []);
  
  const handleToggleChange = async (id: number, enabled: boolean) => {
    try {
      await api.put(`/api/v1/admin/feature_toggles/${id}`, {
        feature_toggle: { enabled },
      });
      setToggles(
        toggles.map((toggle) =>
          toggle.id === id ? { ...toggle, enabled } : toggle
        )
      );
    } catch (err) {
      console.error('Failed to update toggle:', err);
    }
  };
  
  const handleCreateToggle = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await api.post('/api/v1/admin/feature_toggles', {
        feature_toggle: newToggle,
      });
      setToggles([...toggles, response.data.data]);
      setNewToggle({
        name: '',
        enabled: false,
        description: '',
        scope: 'global',
        scope_id: null,
      });
    } catch (err) {
      console.error('Failed to create toggle:', err);
    }
  };
  
  const handleDeleteToggle = async (id: number) => {
    if (!confirm('Are you sure you want to delete this feature toggle?')) {
      return;
    }
    
    try {
      await api.delete(`/api/v1/admin/feature_toggles/${id}`);
      setToggles(toggles.filter((toggle) => toggle.id !== id));
    } catch (err) {
      console.error('Failed to delete toggle:', err);
    }
  };
  
  if (loading && toggles.length === 0) {
    return <div>Loading feature toggles...</div>;
  }
  
  if (error) {
    return <div className="error">{error}</div>;
  }
  
  return (
    <div className="feature-toggle-admin">
      <h2>{title}</h2>
      <p>{description}</p>
      
      <h3>Create New Feature Toggle</h3>
      <form onSubmit={handleCreateToggle}>
        <div>
          <label htmlFor="name">Name:</label>
          <input
            type="text"
            id="name"
            value={newToggle.name}
            onChange={(e) => setNewToggle({ ...newToggle, name: e.target.value })}
            required
          />
        </div>
        
        <div>
          <label htmlFor="enabled">Enabled:</label>
          <input
            type="checkbox"
            id="enabled"
            checked={newToggle.enabled}
            onChange={(e) => setNewToggle({ ...newToggle, enabled: e.target.checked })}
          />
        </div>
        
        <div>
          <label htmlFor="description">Description:</label>
          <textarea
            id="description"
            value={newToggle.description}
            onChange={(e) => setNewToggle({ ...newToggle, description: e.target.value })}
          />
        </div>
        
        <div>
          <label htmlFor="scope">Scope:</label>
          <select
            id="scope"
            value={newToggle.scope}
            onChange={(e) => setNewToggle({ ...newToggle, scope: e.target.value })}
          >
            <option value="global">Global</option>
            <option value="user">User</option>
            <option value="user_group">User Group</option>
          </select>
        </div>
        
        {newToggle.scope !== 'global' && (
          <div>
            <label htmlFor="scope_id">Scope ID:</label>
            <input
              type="number"
              id="scope_id"
              value={newToggle.scope_id || ''}
              onChange={(e) =>
                setNewToggle({
                  ...newToggle,
                  scope_id: e.target.value ? parseInt(e.target.value, 10) : null,
                })
              }
              required
            />
          </div>
        )}
        
        <button type="submit">Create Toggle</button>
      </form>
      
      <h3>Existing Feature Toggles</h3>
      <table>
        <thead>
          <tr>
            <th>Name</th>
            <th>Enabled</th>
            <th>Description</th>
            <th>Scope</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {toggles.map((toggle) => (
            <tr key={toggle.id}>
              <td>{toggle.name}</td>
              <td>
                <input
                  type="checkbox"
                  checked={toggle.enabled}
                  onChange={(e) => handleToggleChange(toggle.id, e.target.checked)}
                />
              </td>
              <td>{toggle.description}</td>
              <td>
                {toggle.scope}
                {toggle.scope_id && ` (ID: ${toggle.scope_id})`}
              </td>
              <td>
                <button onClick={() => handleDeleteToggle(toggle.id)}>Delete</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default FeatureToggleAdmin;
```

## Migration Strategy

To ensure a smooth transition from localStorage to the backend feature toggle system:

### 1. Dual-Source Implementation

During the transition period, implement a dual-source approach that checks both localStorage and the backend:

```typescript
// src/hooks/use-feature-toggle.ts (transition version)
import { useEffect, useState, useCallback } from 'react';
import { fetchUserToggles } from '@/src/api/featureToggles';

// Legacy localStorage functions
const getLocalStorageToggle = (name: string): boolean => {
  try {
    return localStorage.getItem(`FT_${name}`) === 'true';
  } catch (e) {
    return false;
  }
};

const getAllLocalStorageToggles = (): Record<string, boolean> => {
  try {
    const toggles: Record<string, boolean> = {};
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('FT_')) {
        const name = key.substring(3);
        toggles[name] = localStorage.getItem(key) === 'true';
      }
    }
    return toggles;
  } catch (e) {
    return {};
  }
};

export function useFeatureToggle(featureName?: string) {
  const [backendToggles, setBackendToggles] = useState<Record<string, boolean>>({});
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const loadToggles = async () => {
      setLoading(true);
      try {
        const toggleData = await fetchUserToggles();
        setBackendToggles(toggleData);
      } finally {
        setLoading(false);
      }
    };
    
    loadToggles();
  }, []);
  
  // Combine backend and localStorage toggles, with backend taking precedence
  const combinedToggles = {
    ...getAllLocalStorageToggles(),
    ...backendToggles
  };
  
  const isEnabled = useCallback(
    (name: string) => {
      // First check backend toggles
      if (backendToggles[name] !== undefined) {
        return backendToggles[name];
      }
      
      // Fall back to localStorage
      return getLocalStorageToggle(name);
    },
    [backendToggles]
  );
  
  if (featureName) {
    return {
      enabled: isEnabled(featureName),
      loading,
    };
  }
  
  return {
    isEnabled,
    allFeatures: combinedToggles,
    loading,
  };
}
```

### 2. Migration Steps

1. **Deploy Backend System**: Implement and deploy the backend feature toggle system
2. **Update Frontend**: Update the frontend to use the dual-source implementation
3. **Migrate Existing Toggles**: Create backend toggles for all existing localStorage toggles
4. **Monitor Usage**: Monitor for any issues during the transition
5. **Remove localStorage**: Once confident in the backend system, remove the localStorage fallback

## Caching Strategy

To improve performance, implement caching for feature toggles:

```ruby
# app/models/feature_toggle.rb (with caching)
class FeatureToggle < ApplicationRecord
  # ... existing code ...
  
  def self.enabled?(name, user_id = nil)
    cache_key = user_id ? "feature_toggle:#{name}:user:#{user_id}" : "feature_toggle:#{name}:global"
    
    Rails.cache.fetch(cache_key, expires_in: 5.minutes) do
      # Check user-specific toggle first
      return true if user_id.present? && exists?(name: name, scope: 'user', scope_id: user_id, enabled: true)
      
      # Then check global toggle
      exists?(name: name, scope: 'global', enabled: true)
    end
  end
  
  def self.for_user_with_globals(user_id)
    cache_key = "feature_toggles:user:#{user_id}"
    
    Rails.cache.fetch(cache_key, expires_in: 5.minutes) do
      global_toggles = global.to_a
      user_toggles = for_user(user_id).to_a
      
      # Merge toggles, with user-specific overriding global
      all_toggles = global_toggles.index_by(&:name)
      user_toggles.each { |toggle| all_toggles[toggle.name] = toggle }
      
      all_toggles.values
    end
  end
  
  # Invalidate cache when a toggle is updated
  after_save :invalidate_cache
  after_destroy :invalidate_cache
  
  private
  
  def invalidate_cache
    if scope == 'global'
      Rails.cache.delete("feature_toggle:#{name}:global")
      # Also invalidate all user caches since global changes affect everyone
      Rails.cache.delete_matched("feature_toggles:user:*")
    else
      Rails.cache.delete("feature_toggle:#{name}:#{scope}:#{scope_id}")
      Rails.cache.delete("feature_toggles:#{scope}:#{scope_id}")
    end
  end
end
```

## Benefits Over localStorage Implementation

1. **Centralized Management**: Manage toggles from a single admin interface
2. **Consistent Experience**: Users get the same feature set across devices
3. **Targeted Rollouts**: Enable features for specific users or groups
4. **Analytics Integration**: Track feature usage more effectively
5. **Security**: Feature flags can't be manipulated by users
6. **Persistence**: Toggles persist across browser sessions and devices
7. **Server-Side Control**: Features can be toggled without client-side changes
8. **Immediate Updates**: Changes take effect immediately for all users

## Use Cases

1. **Gradual Rollout**: Gradually roll out new features to users
2. **A/B Testing**: Test different versions of a feature
3. **Canary Releases**: Release features to a small subset of users
4. **Feature Flags**: Enable/disable features based on user roles or preferences
5. **Kill Switches**: Quickly disable problematic features without deploying

## Best Practices

1. **Use descriptive feature names**: Choose clear, descriptive names for your feature toggles
2. **Clean up unused toggles**: Remove feature toggles when they're no longer needed
3. **Document your toggles**: Keep a list of active feature toggles and their purpose
4. **Use for temporary features**: Feature toggles are best for temporary features or A/B testing
5. **Consider performance**: Use caching to reduce database queries
6. **Limit scope-specific toggles**: Use global toggles when possible to reduce complexity
7. **Audit regularly**: Regularly review and clean up unused toggles
