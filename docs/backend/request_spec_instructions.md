## Request Specs Instructions

When asked to create request specs for a Rails API:

1. First identify all API endpoints that need testing, including:

   - HTTP verb (GET, POST, PUT/PATCH, DELETE)
   - Route path
   - Required parameters
   - Expected responses

2. For each endpoint, create tests that cover:

   - Happy path (valid requests)
   - Sad path (invalid requests)
   - Edge cases (empty collections, boundary conditions)

3. Structure each test to verify:

   - HTTP status code
   - Response body structure
   - Database changes when applicable
   - Headers when relevant (Content-Type, Location)

4. Include authentication/authorization tests if applicable.

5. Use factory patterns for test data generation.

6. Format response specs following this pattern:
   ```ruby
   RSpec.describe "Resource API", type: :request do
     describe "GET /api/resource" do
       # Setup test data
       # Perform request
       # Verify response status
       # Verify response body
     end
   end
   ```

## API Schema Documentation Testing

When asked to create API documentation tests with rswag:

1. Set up the swagger_helper.rb if not already present.

2. For each endpoint, define:

   - Path and HTTP method
   - Tags for grouping
   - Parameter definitions (path, query, body)
   - Request content types
   - Response content types
   - Response schemas for each status code

3. Use examples to demonstrate real-world usage.

4. Structure schema definitions following this pattern:

   ```ruby
   path '/api/resources' do
     get 'Lists resources' do
       tags 'Resources'
       produces 'application/json'

       parameter name: :query, in: :query, type: :string, required: false

       response '200', 'resources found' do
         schema type: :array, items: { '$ref' => '#/components/schemas/resource' }
         run_test!
       end

       response '401', 'unauthorized' do
         run_test!
       end
     end
   end
   ```

5. Define reusable schema components for common structures.

6. Ensure all possible response codes are documented and tested.

7. Include authorization flows if applicable.
