# Competition Standings Frontend Implementation Guide

This guide explains how to implement the competition standings feature in the frontend application.

> **Related APIs**:
>
> - See the [League Standings API](./league_standings_api.md) for league-specific standings
> - See the [Team Standings API](./team_standings_api.md) for team-specific standings

## Overview

The competition standings feature allows users to view the overall standings for all users who have made predictions in a competition. This provides a global leaderboard across all leagues in a competition.

## API Endpoint

```
GET /api/v1/competitions/:id/standings
```

### Parameters

- `:id` - The ID of the competition (required)
- `season_id` - Optional query parameter for retrieving historical standings

## Response Format

```typescript
interface Standing {
  username: string;
  points: number;
  correct: number;
  incorrect: number;
  perfect: number;
  position: number;
  top_message?: string;
}

interface CompetitionStandingsResponse {
  competition: {
    id: number;
    name: string;
    season?: {
      id: number;
      start_date: string;
      end_date: string;
    };
    standings: Standing[];
  };
  message: string[];
  status: number;
  type: string;
}
```

## Implementation Steps

### 1. Add API Service Function

Add the following function to your API service:

```typescript
export const getCompetitionStandings = async (
  competitionId: number,
  seasonId?: number
) => {
  const params = seasonId ? { season_id: seasonId } : {};
  const response = await api.get<CompetitionStandingsResponse>(
    `/api/v1/competitions/${competitionId}/standings`,
    { params }
  );
  return response.data;
};
```

### 2. Create a Competition Standings Component

Create a component to display the competition standings:

```tsx
import React from "react";
import { Standing } from "../types";

interface CompetitionStandingsProps {
  standings: Standing[];
  isLoading: boolean;
}

const CompetitionStandings: React.FC<CompetitionStandingsProps> = ({
  standings,
  isLoading,
}) => {
  if (isLoading) {
    return <div>Loading standings...</div>;
  }

  if (!standings || standings.length === 0) {
    return <div>No standings available.</div>;
  }

  return (
    <div className="competition-standings">
      <table>
        <thead>
          <tr>
            <th>Position</th>
            <th>User</th>
            <th>Points</th>
            <th>Perfect</th>
            <th>Correct</th>
            <th>Incorrect</th>
          </tr>
        </thead>
        <tbody>
          {standings.map((standing) => (
            <tr
              key={standing.username}
              className={
                standing.position <= 3 ? `top-${standing.position}` : ""
              }
            >
              <td>{standing.position}</td>
              <td>
                {standing.username}
                {standing.top_message && (
                  <div className="top-message">{standing.top_message}</div>
                )}
              </td>
              <td>{standing.points}</td>
              <td>{standing.perfect}</td>
              <td>{standing.correct}</td>
              <td>{standing.incorrect}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default CompetitionStandings;
```

### 3. Create a Competition Standings Page

Create a page component that fetches and displays the competition standings:

```tsx
import React, { useState, useEffect } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { getCompetitionStandings } from "../services/api";
import CompetitionStandings from "../components/CompetitionStandings";
import { CompetitionStandingsResponse } from "../types";

const CompetitionStandingsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [searchParams, setSearchParams] = useSearchParams();
  const seasonId = searchParams.get("season_id");

  const [data, setData] = useState<CompetitionStandingsResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [availableSeasons, setAvailableSeasons] = useState<
    Array<{ id: number; label: string }>
  >([]);

  useEffect(() => {
    const fetchStandings = async () => {
      try {
        setIsLoading(true);
        const response = await getCompetitionStandings(
          parseInt(id as string, 10),
          seasonId ? parseInt(seasonId, 10) : undefined
        );
        setData(response);

        // If we have historical seasons data, populate the dropdown
        if (response.competition.historical_seasons) {
          setAvailableSeasons(
            response.competition.historical_seasons.map((season) => ({
              id: season.id,
              label: `${new Date(season.start_date).getFullYear()}-${new Date(
                season.end_date
              ).getFullYear()}`,
            }))
          );
        }
      } catch (err) {
        setError("Failed to load standings");
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchStandings();
    }
  }, [id, seasonId]);

  const handleSeasonChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    if (value === "current") {
      searchParams.delete("season_id");
    } else {
      searchParams.set("season_id", value);
    }
    setSearchParams(searchParams);
  };

  if (error) {
    return <div className="error-message">{error}</div>;
  }

  return (
    <div className="competition-standings-page">
      <h1>{data?.competition.name} Standings</h1>

      {data?.competition.season && (
        <div className="season-info">
          Season:{" "}
          {new Date(data.competition.season.start_date).toLocaleDateString()} -
          {new Date(data.competition.season.end_date).toLocaleDateString()}
        </div>
      )}

      {availableSeasons.length > 0 && (
        <div className="season-selector">
          <label htmlFor="season-select">Select Season:</label>
          <select
            id="season-select"
            value={seasonId || "current"}
            onChange={handleSeasonChange}
          >
            <option value="current">Current Season</option>
            {availableSeasons.map((season) => (
              <option key={season.id} value={season.id.toString()}>
                {season.label}
              </option>
            ))}
          </select>
        </div>
      )}

      <CompetitionStandings
        standings={data?.competition.standings || []}
        isLoading={isLoading}
      />
    </div>
  );
};

export default CompetitionStandingsPage;
```

### 4. Add Styling for Top 3 Users

Add CSS to highlight the top 3 users and display their special messages:

```css
/* competition-standings.css */
.competition-standings .top-1 {
  background-color: rgba(255, 215, 0, 0.1); /* Gold */
  font-weight: bold;
}

.competition-standings .top-2 {
  background-color: rgba(192, 192, 192, 0.1); /* Silver */
}

.competition-standings .top-3 {
  background-color: rgba(205, 127, 50, 0.1); /* Bronze */
}

.top-message {
  font-size: 0.8rem;
  color: #666;
  margin-top: 4px;
  font-style: italic;
}
```

### 5. Add Route to Your Application

Add the competition standings route to your router configuration:

```tsx
// In your router configuration
<Route
  path="/competitions/:id/standings"
  element={<CompetitionStandingsPage />}
/>
```

### 6. Add Link to Competition Details Page

Add a link to the standings from your competition details page:

```tsx
<Link
  to={`/competitions/${competition.id}/standings`}
  className="btn btn-primary"
>
  View Competition Standings
</Link>
```

## Comparison with Other Standings APIs

### Differences from League Standings

The competition standings feature is similar to the league standings feature, but there are a few key differences:

1. **Scope**: Competition standings show all users who have made predictions in a competition, regardless of which leagues they are in.
2. **API Endpoint**: The endpoint is `/api/v1/competitions/:id/standings` instead of `/api/v1/leagues/:id/standings`.
3. **Response Format**: The response has a `competition` object instead of a `league` object.
4. **Top Messages**: The top messages refer to "competition" instead of "league" (e.g., "leading the competition" vs. "leading the league").

### Differences from Team Standings

The competition standings feature also differs from the team standings feature:

1. **Scope**: Competition standings include all users in a competition, while team standings only include users who have selected a specific team as their favorite.
2. **API Endpoint**: The endpoint is `/api/v1/competitions/:id/standings` instead of `/api/v1/teams/:id/standings`.
3. **Filtering**: Team standings can be filtered by both competition and season, while competition standings can only be filtered by season.
4. **Top Messages**: The top messages in team standings refer to "fans" (e.g., "leading among fans" vs. "leading the competition").

## Testing

Make sure to test the following scenarios:

1. Viewing current season standings
2. Switching between current and historical seasons
3. Proper display of top 3 user messages
4. Empty state handling when no standings are available
5. Error handling when the API request fails

## Conclusion

This implementation provides a complete solution for displaying competition-wide standings in your frontend application. The feature supports both current and historical standings, with special highlighting for the top 3 users.
