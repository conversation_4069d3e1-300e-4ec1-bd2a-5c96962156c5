# League Season Transition Guide

This document explains how leagues persist across seasons in the Brag Rights application, including how historical data is stored and how to interact with the API for season transitions.

## Overview

The system now supports:

1. Preserving leagues across seasons (leagues are not deleted when a season ends)
2. Storing historical user performance data for each season
3. Viewing historical league standings from previous seasons
4. Transitioning leagues to a new season when a competition's season changes

## Key Concepts

### League Persistence

- Leagues are now designed to persist across multiple seasons
- When a new season starts for a competition, leagues are updated to use the new season
- Historical data about user performance in previous seasons is preserved
- Users can continue to use the same leagues in the new season

### Historical Data

- User performance data (points, correct predictions, etc.) is stored for each league-season combination
- This allows users to view historical standings from previous seasons
- The frontend can display a league's performance history across multiple seasons

### Season Completion

- Seasons are automatically marked as completed when:
  1. The season's end date has passed AND
  2. All matches for the season have been completed (finished, postponed, or cancelled)
- A daily background job checks for completed seasons and marks them accordingly
- When a season is marked as completed, users can no longer make predictions for it
- The system uses a `completed` flag on the Season model rather than just checking the end date
- This ensures that postponed matches that are played after the official end date are still considered

### Season Transitions

- When a new season is created for a competition, an admin must trigger the transition
- During transition, all leagues for that competition are updated to use the new season
- Historical data is preserved before the transition
- Leagues from the old season are archived (but can still be viewed for historical purposes)

## API Endpoints

### 1. Transition Leagues to New Season

**Endpoint:** `POST /api/v1/seasons/:id/transition_leagues`

**Authorization:** Admin only

**Description:** Updates all leagues for a competition to use the new season and archives leagues from the old season.

**Parameters:**

- `:id` - The ID of the new season

**Response:**

```json
{
  "message": [
    "3 leagues transitioned to new season successfully. Old season leagues have been archived."
  ],
  "status": 200,
  "type": "success"
}
```

**Error Responses:**

- If the season is not the current season for its competition:

```json
{
  "errors": ["This is not the current season for this competition"],
  "status": 422
}
```

- If leagues are already using the current season:

```json
{
  "message": ["Leagues are already using the current season"],
  "status": 200,
  "type": "success"
}
```

### 2. View League Standings (Current Season)

**Endpoint:** `GET /api/v1/leagues/:league_id/table`

**Description:** Returns the current standings for a league.

**Response:**

```json
{
  "league": {
    "id": 1,
    "name": "Premier League Fans",
    "competition": {
      "id": 2,
      "name": "Premier League",
      "emblem_public_id": "premier_league_emblem",
      "country": "England"
    },
    "standings": [
      {
        "username": "john_doe",
        "points": 45,
        "correct": 15,
        "incorrect": 5,
        "perfect": 10,
        "position": 1,
        "top_message": "🏆 john_doe is leading the league! Congratulations!"
      },
      {
        "username": "jane_smith",
        "points": 42,
        "correct": 14,
        "incorrect": 6,
        "perfect": 9,
        "position": 2,
        "top_message": "🥈 jane_smith is in second place. Keep pushing!"
      },
      {
        "username": "bob_jones",
        "points": 38,
        "correct": 12,
        "incorrect": 8,
        "perfect": 8,
        "position": 3,
        "top_message": "🥉 bob_jones is in third place. Great job!"
      },
      {
        "username": "alice_brown",
        "points": 35,
        "correct": 11,
        "incorrect": 9,
        "perfect": 7,
        "position": 4
      }
      // More users...
    ]
  },
  "message": ["League standings calculated"],
  "status": 200,
  "type": "success"
}
```

### 3. View Historical League Standings

**Endpoint:** `GET /api/v1/leagues/:league_id/table?season_id=:season_id`

**Description:** Returns the historical standings for a league in a specific season.

**Parameters:**

- `:league_id` - The ID of the league
- `:season_id` - The ID of the historical season

**Response:**

```json
{
  "league": {
    "id": 1,
    "name": "Premier League Fans",
    "season": {
      "id": 1,
      "start_date": "2022-08-05",
      "end_date": "2023-05-28"
    },
    "standings": [
      {
        "username": "john_doe",
        "points": 45,
        "correct": 15,
        "incorrect": 5,
        "perfect": 10,
        "position": 1,
        "top_message": "🏆 john_doe is leading the league! Congratulations!"
      },
      {
        "username": "jane_smith",
        "points": 42,
        "correct": 14,
        "incorrect": 6,
        "perfect": 9,
        "position": 2,
        "top_message": "🥈 jane_smith is in second place. Keep pushing!"
      },
      {
        "username": "bob_jones",
        "points": 38,
        "correct": 12,
        "incorrect": 8,
        "perfect": 8,
        "position": 3,
        "top_message": "🥉 bob_jones is in third place. Great job!"
      }
      // More users...
    ]
  },
  "message": ["Historical league standings calculated"],
  "status": 200,
  "type": "success"
}
```

### 4. Get League Details

**Endpoint:** `GET /api/v1/leagues/:id`

**Description:** Returns details about a league, including its current season and historical seasons.

**Response:**

```json
{
  "league": {
    "id": 1,
    "name": "Premier League Fans",
    "competition": {
      "id": 2,
      "name": "Premier League",
      "emblem_public_id": "premier_league_emblem",
      "country": "England",
      "current_season": {
        "id": 3,
        "current_matchday": 12
      }
    },
    "season": {
      "id": 3,
      "start_date": "2023-08-11",
      "end_date": "2024-05-19",
      "current_matchday": 12
    },
    "historical_seasons": [
      {
        "id": 2,
        "start_date": "2022-08-05",
        "end_date": "2023-05-28",
        "standings_url": "/api/v1/leagues/1/table?season_id=2"
      },
      {
        "id": 1,
        "start_date": "2021-08-13",
        "end_date": "2022-05-22",
        "standings_url": "/api/v1/leagues/1/table?season_id=1"
      }
    ],
    "open": true,
    "owner": {
      "id": 1,
      "username": "admin"
    },
    "starting_matchday": 1,
    "archived": false,
    "archived_at": null,
    "users": [
      {
        "id": 1,
        "username": "admin"
      }
      // More users...
    ]
  },
  "message": ["League found"],
  "status": 200,
  "type": "success"
}
```

### 5. List Leagues

**Endpoint:** `GET /api/v1/leagues`

**Description:** Returns a list of leagues. By default, only active leagues for the current user are returned.

**Query Parameters:**

- `competition_id` (optional) - Filter leagues by competition
- `season_id` (optional) - Filter leagues by season
- `archived` (optional) - Set to "true" to show archived leagues

**Notes:**

- When `season_id` is provided, both active and archived leagues for that season are returned
- This allows viewing historical leagues even if they're archived

**Response:**

```json
{
  "leagues": [
    {
      "id": 1,
      "name": "Premier League Fans",
      "competition": {
        "id": 2,
        "name": "Premier League",
        "emblem_public_id": "premier_league_emblem"
      },
      "open": true,
      "owner": {
        "id": 1,
        "username": "admin"
      },
      "user_count": 10,
      "archived": false
    }
    // More leagues...
  ],
  "message": ["Leagues found"],
  "status": 200,
  "type": "success"
}
```

## Handling Season Transitions in the Frontend

### Detecting a Completed Season

When fetching predictable matches for a competition, the API will return a special response when the season is over:

```json
{
  "matches": [],
  "predictions": [],
  "message": ["Season is over. No matches available for prediction."],
  "season_over": true,
  "competition_id": 2,
  "season_id": 5,
  "status": 200,
  "type": "success"
}
```

The frontend should:

1. Check for the `season_over` flag
2. Display an appropriate message to users
3. Potentially show a link to view historical standings

Note that a season is considered "over" when:

- It has been marked as completed (both end date passed AND all matches completed), OR
- The end date has passed but some matches are still pending (in this case, predictions are still disabled)

### Displaying Historical Seasons

For leagues that have data from multiple seasons:

1. Show the current season standings by default
2. Provide a dropdown or tabs to select historical seasons
3. When a historical season is selected, call the API with the `season_id` parameter
4. Display the historical standings with clear indication of which season is being viewed

### Top 3 User Messages

The league standings API now includes special messages for the top 3 users in each league:

1. **First Place**: Users in first place receive a trophy emoji and a congratulatory message
2. **Second Place**: Users in second place receive a silver medal emoji and an encouraging message
3. **Third Place**: Users in third place receive a bronze medal emoji and a positive message

These messages are automatically included in the API response for both current and historical standings in the `top_message` field. Users beyond the top 3 positions will have a `null` value for this field.

Example messages:

- 1st place: "🏆 username is leading the league! Congratulations!"
- 2nd place: "🥈 username is in second place. Keep pushing!"
- 3rd place: "🥉 username is in third place. Great job!"

The frontend should display these messages prominently when showing league standings to enhance user engagement and gamification.

### Admin Season Transition

For admin users:

1. Provide a UI to transition leagues when a new season is created
2. Call the transition_leagues endpoint with the new season ID
3. Display a success message when the transition is complete
4. Update the UI to show leagues with the new season

## Important Notes

1. **League Archiving vs Season Transitions**

   - Leagues are only archived when a new season is created for their competition
   - Archived leagues can still be viewed for historical purposes
   - When a new season is created, leagues are automatically unarchived

2. **Viewing Results for Completed Seasons**

   - Users can still view results for completed seasons until a new season is created
   - Historical data is preserved even after a new season is created

3. **Predictable Matches for Completed Seasons**

   - When a season is over, the predictable_matches endpoint will return a clear message
   - The frontend should handle this gracefully and inform users

4. **Top 3 User Messages**

   - Special messages are provided for the top 3 users in league standings
   - These messages enhance user engagement and gamification
   - The frontend should display these messages prominently in the league standings UI

5. **Season Completion Check**
   - A daily background job (`CheckSeasonCompletionJob`) runs to check if seasons are completed
   - This job checks both the end date and whether all matches are completed
   - When a season is marked as completed, leagues for that season are automatically archived
   - The job also checks for upcoming seasons that are ending soon to prepare for transitions
