# MailerSend Email Integration

## Overview

This document describes how to use MailerSend for transactional emails in the production environment of the Brag Rights application.

## Configuration

MailerSend is currently configured as the default delivery method in the production environment. The configuration is set in:

- `config/environments/production.rb` - Sets MailerSend as the delivery method
- `config/initializers/mailersend.rb` - Defines the custom delivery method

## Environment Variables

The following environment variables must be set in production:

```
MAILERSEND_API_KEY=your_api_key_here
```

You can obtain your API key from the MailerSend dashboard.

## Usage in Mailers

MailerSend works transparently with ActionMailer. No changes are needed to your mailer classes:

```ruby
class NotificationMailer < ApplicationMailer
  def welcome_email(user)
    @user = user
    mail(
      to: user.email,
      subject: "Welcome to Brag Rights!"
    )
  end
end
```

## Templates

To use MailerSend templates, add a template_id to the mail headers:

```ruby
def template_email(user)
  @user = user
  mail(
    to: user.email,
    subject: "Using a Template",
    template_id: "your_template_id"
  ) do |format|
    format.html { render plain: '' } # Content will be ignored when using a template
  end
end
```

## Personalization

To use personalization variables with MailerSend:

```ruby
def personalized_email(user)
  @user = user
  variables = [
    {
      email: user.email,
      data: {
        name: user.name,
        score: user.score
      }
    }
  ].to_json
  
  headers['variables'] = variables
  
  mail(
    to: user.email,
    subject: "Hello {{name}}"
  )
end
```

## Attachments

Attachments work the same way as with standard ActionMailer:

```ruby
def email_with_attachment(user, pdf_data)
  @user = user
  attachments['report.pdf'] = pdf_data
  
  mail(
    to: user.email,
    subject: "Your Report"
  )
end
```

## Testing

MailerSend integration has comprehensive test coverage. You can find the tests in:

- `spec/mailers/mailersend_delivery_method_spec.rb`

## Switching Back to SMTP

To switch back to the previous SMTP configuration if needed, uncomment the SMTP settings in `config/environments/production.rb` and change the delivery method from `:mailersend` to `:smtp`.

## Troubleshooting

Common issues:

1. **Missing API Key**: Ensure MAILERSEND_API_KEY is set in your production environment
2. **Email Not Delivered**: Check Rails logs for detailed error messages
3. **Template Not Found**: Verify your template ID is correct in the MailerSend dashboard

For more information, refer to the [MailerSend API Documentation](https://developers.mailersend.com/).