# Backend Documentation

This directory contains documentation specifically for backend developers working on the Brag Rights API.

## Contents

- **api_football_service_requirements.md** - Requirements for the ApiFootball service integration
- **competition_standings_guide.md** - Guide for competition standings implementation
- **email_verification_setup.md** - Setup guide for email verification
- **final_predictions.md** - Implementation guide for final predictions
- **icelandic_team_colors.md** - Guide for handling Icelandic team colors
- **league_season_transition_guide.md** - Guide for transitioning leagues between seasons
- **mailersend_integration.md** - Integration guide for MailerSend
- **request_spec_instructions.md** - Instructions for writing request specs

Note: API endpoint specifications have been moved to the `api_endpoints/` directory.

## Using These Guides

These guides are intended to help backend developers understand the implementation details of the Brag Rights API. They include:

- Service requirements
- Implementation details
- Testing guidelines

When implementing new features or making changes to existing ones, please refer to these guides to ensure consistency with the existing application architecture.
