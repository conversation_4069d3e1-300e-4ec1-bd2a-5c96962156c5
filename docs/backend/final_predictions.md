# Final Predictions Documentation

## Overview

The **Final Predictions** feature allows users to make predictions about the outcomes of football matches and leagues. This includes predicting match scores, league winners, and other related outcomes. The system calculates scores based on the accuracy of these predictions and ranks users accordingly.

---

## Key Features

1. **Match Predictions**:

   - Users can predict the scores of individual matches.
   - Predictions are locked once the match starts.

2. **League Predictions**:

   - Users can predict the final standings of leagues.
   - Predictions are locked once the league reaches a specified stage.

3. **Scoring System**:

   - Points are awarded based on the accuracy of predictions.
   - The scoring mechanism is flexible and can be adjusted.

4. **Leaderboards**:
   - Users are ranked based on their total scores.
   - Separate leaderboards for public and private leagues.

---

## API Endpoints

### Match Predictions

- **POST** `/api/v1/matches/:match_id/predictions`

  - Create a new prediction for a specific match.
  - **Request Body**:
    ```json
    {
      "prediction": {
        "home_score": 2,
        "away_score": 1
      }
    }
    ```
  - **Response**:
    - **201 Created**: Returns the created prediction.
    - **422 Unprocessable Entity**: Returns validation errors.

- **GET** `/api/v1/matches/:match_id/predictions`
  - Retrieve all predictions for a specific match.

### League Predictions

- **POST** `/api/v1/leagues/:league_id/predictions`

  - Create a new prediction for league standings.
  - **Request Body**:
    ```json
    {
      "prediction": {
        "standings": [
          { "team_id": 1, "position": 1 },
          { "team_id": 2, "position": 2 }
        ]
      }
    }
    ```
  - **Response**:
    - **201 Created**: Returns the created prediction.
    - **422 Unprocessable Entity**: Returns validation errors.

- **GET** `/api/v1/leagues/:league_id/predictions`
  - Retrieve all predictions for a specific league.

---

## Scoring System

1. **Match Predictions**:

   - Exact score: Full points.
   - Correct winner but incorrect score: Partial points.
   - Incorrect prediction: No points.

2. **League Predictions**:
   - Correct position for a team: Full points.
   - Close position (e.g., off by 1): Partial points.
   - Incorrect position: No points.

---

## Error Handling

- **401 Unauthorized**: Returned if the user is not authenticated.
- **403 Forbidden**: Returned if the user is not authorized to access the resource.
- **404 Not Found**: Returned if the match or league does not exist.
- **422 Unprocessable Entity**: Returned for validation errors.

---

## Authentication and Authorization

- All endpoints require authentication via JWT tokens.
- Users can only view or modify their own predictions unless they have admin privileges.

---

## Testing Guidelines

- Write request specs for all endpoints.
- Test both happy paths and edge cases.
- Verify proper status codes and JSON response structure.
- Use factories to create test data for matches, leagues, and predictions.

---

## Notes

- Ensure predictions are immutable once locked.
- Use background jobs for recalculating scores and updating leaderboards.
- Implement pagination for large datasets in prediction retrieval endpoints.
