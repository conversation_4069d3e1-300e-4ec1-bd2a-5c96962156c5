# ApiFootballService Requirements

This document outlines the requirements for using the ApiFootballService in the Brag Rights backend application.

## Overview

The ApiFootballService is responsible for interacting with the API-Football external service to fetch competition, team, and match data. It has been refactored to remove hardcoded constants and now requires explicit parameters for all operations.

## Required Parameters

### For All Methods

All methods in ApiFootballService that interact with the API-Football service now require explicit parameters:

1. **league_id**: The external service ID for the league/competition
   - This should be obtained from the `external_service_id` field of the Competition model
   - Example: `113` for the Icelandic Superliga

2. **season_year**: The year of the season
   - This should be provided by the frontend when creating a new season
   - Example: `2026` for the 2026 season

### Creating a New Season

When creating a new season using the `create_new_season_for_competition` method:

```ruby
ApiFootballService.new.create_new_season_for_competition(competition, season_id)
```

- `competition`: The Competition model instance
  - Must have a valid `external_service_id` set
- `season_id`: The year of the season to create (e.g., `2026`)

### Importing Competition Data

When importing competition data using the `import_competition` method:

```ruby
ApiFootballService.new.import_competition(nil, league_id, season_year)
```

- `league_id`: The external service ID for the league/competition
- `season_year`: The year of the season

### Updating Matches

When updating matches using the `update_matches` method:

```ruby
ApiFootballService.new.update_matches(competition, league_id, season_year)
```

- `competition`: The Competition model instance
- `league_id`: (Optional) The external service ID for the league/competition
  - If not provided, will use `competition.external_service_id`
- `season_year`: (Optional) The year of the season
  - If not provided, will use `competition.current_season.external_service_id`

### Importing Teams

When importing teams using the `import_teams_for_competition` method:

```ruby
ApiFootballService.new.import_teams_for_competition(competition, league_id, season_year)
```

- `competition`: The Competition model instance
- `league_id`: (Optional) The external service ID for the league/competition
  - If not provided, will use `competition.external_service_id`
- `season_year`: (Optional) The year of the season
  - If not provided, will use `competition.current_season.external_service_id`

## Error Handling

The ApiFootballService will raise an `ApiError` in the following cases:

1. Missing required parameters:
   - If `league_id` is not provided and cannot be determined from the competition
   - If `season_year` is not provided and cannot be determined from the competition's current season

2. API request failures:
   - If the API returns a non-200 status code
   - If the API returns invalid or incomplete data

## Frontend Requirements

When creating a new season for an ApiFootball competition, the frontend must:

1. Provide the `season_external_service_id` parameter in the request
2. Ensure the competition has a valid `external_service_id` set

Example request:

```javascript
// For ApiFootball competitions
const response = await api.post(
  `/api/v1/admin/competitions/${competitionId}/create_new_season`,
  { season_external_service_id: '2026' }
);
```

## Testing

When testing the ApiFootballService, make sure to:

1. Provide valid `league_id` and `season_year` parameters
2. Mock the API responses appropriately
3. Test error cases for missing parameters
4. Test error cases for API failures

## Migration Notes

The hardcoded constants `LEAGUE_ID` and `SEASON` have been removed from the ApiFootballService. All methods now require explicit parameters or will attempt to use values from the provided competition model.

This change ensures that the service is more flexible and can work with different leagues and seasons as needed.
