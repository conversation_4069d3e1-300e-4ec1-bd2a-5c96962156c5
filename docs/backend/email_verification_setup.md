# Email Verification Implementation for BragRights Football API

This guide will walk you through implementing email verification in your existing Rails 7 API-only application using Devise and Devise-API gems, with Namecheap email handling.

## 1. Update the User Model with Confirmable

First, we need to add the `confirmable` module to your User model:

```ruby
# app/models/user.rb
class User < ApplicationRecord
  # Add the confirmable module to existing Devise modules
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable,
         :confirmable  # Add this line
         
  # Existing associations and validations...
end
```

## 2. Create a Migration for Confirmable Columns

Since your users table already exists, you need to add the confirmable columns:

```ruby
# db/migrate/YYYYMMDDHHMMSS_add_confirmable_to_users.rb
class AddConfirmableToUsers < ActiveRecord::Migration[7.0]
  def up
    add_column :users, :confirmation_token, :string
    add_column :users, :confirmed_at, :datetime
    add_column :users, :confirmation_sent_at, :datetime
    add_column :users, :unconfirmed_email, :string
    
    add_index :users, :confirmation_token, unique: true
    
    # Mark existing users as confirmed
    User.update_all(confirmed_at: DateTime.now)
  end

  def down
    remove_columns :users, :confirmation_token, :confirmed_at, :confirmation_sent_at, :unconfirmed_email
  end
end
```

Run the migration:

```bash
rails db:migrate
```

## 3. Configure Email Settings

Update your environment configurations to use Namecheap for email:

```ruby
# config/environments/development.rb
Rails.application.configure do
  # Other configurations...
  
  config.action_mailer.default_url_options = { host: 'localhost', port: 3000 }
  config.action_mailer.delivery_method = :smtp
  config.action_mailer.perform_deliveries = true
  config.action_mailer.raise_delivery_errors = true
  
  config.action_mailer.smtp_settings = {
    address: 'mail.privateemail.com',  # Namecheap Private Email server
    port: 587,
    domain: 'bragrights.football',
    user_name: '<EMAIL>',
    password: ENV['NAMECHEAP_PASSWORD'],
    authentication: 'plain',
    enable_starttls_auto: true
  }
end
```

```ruby
# config/environments/production.rb
Rails.application.configure do
  # Other configurations...
  
  config.action_mailer.default_url_options = { host: 'api.bragrights.football' }
  config.action_mailer.delivery_method = :smtp
  config.action_mailer.perform_deliveries = true
  
  config.action_mailer.smtp_settings = {
    address: 'mail.privateemail.com',  # Namecheap Private Email server
    port: 587,
    domain: 'bragrights.football',
    user_name: '<EMAIL>',
    password: ENV['NAMECHEAP_PASSWORD'],
    authentication: 'plain',
    enable_starttls_auto: true
  }
end
```

## 4. Update the TokensController

Update your `TokensController` to handle confirmations:

```ruby
# app/controllers/tokens_controller.rb
class TokensController < Devise::Api::TokensController
  rescue_from ActiveRecord::RecordInvalid, with: :handle_validation_error
  before_action :authenticate_user!, only: [:show]

  def sign_in
    super()
  end

  def create
    # Direct user creation instead of using build_resource
    @user = User.new(sign_up_params)

    @user.save!

    # Send confirmation email
    @user.send_confirmation_instructions

    # Generate authentication token if needed (but user will be unconfirmed)
    @token = Devise::Api.generate_token(@user) if Devise.mappings[:user].api.sign_up_generates_token

    render json: {
      status: :success,
      data: @user,
      token: @token,
      message: 'Please check your email to confirm your account'
    }, status: :created
  rescue ActiveRecord::RecordInvalid => e
    handle_validation_error(e)
  end

  def sign_up
    create  # Use the same implementation as create
  end

  def show
    render json: {
      status: :success,
      data: {
        user: current_user,
        token: request.headers['Authorization']&.split(' ')&.last,
        confirmed: current_user.confirmed?
      }
    }
  end

  # Add confirmation endpoint
  def confirm
    token = params[:confirmation_token]
    user = User.confirm_by_token(token)
    
    if user.errors.empty?
      # Generate a new token since the user is now confirmed
      @token = Devise::Api.generate_token(user)
      
      render json: {
        status: :success,
        message: 'Your account has been successfully confirmed',
        data: {
          user: user,
          token: @token
        }
      }
    else
      render json: {
        status: :error,
        message: 'Confirmation token is invalid or has expired',
        errors: user.errors.full_messages
      }, status: :unprocessable_entity
    end
  end
  
  # Add resend confirmation endpoint
  def resend_confirmation
    email = params[:email]
    user = User.find_by(email: email)
    
    if user
      if user.confirmed?
        render json: {
          status: :info,
          message: 'This account has already been confirmed'
        }
      else
        user.resend_confirmation_instructions
        render json: {
          status: :success,
          message: 'Confirmation instructions have been sent to your email'
        }
      end
    else
      render json: {
        status: :error,
        message: 'Email not found'
      }, status: :not_found
    end
  end

  private

  def handle_validation_error(exception)
    # Existing error handling...
  end

  def sign_up_params
    # Existing parameter handling...
  end
end
```

## 5. Update Routes

Update your routes to handle confirmations:

```ruby
# config/routes.rb
Rails.application.routes.draw do
  # Existing routes...
  
  devise_for :users, controllers: { tokens: 'tokens' }

  # Add confirmation-specific routes
  devise_scope :user do
    get 'tokens/show', to: 'tokens#show'
    get 'confirmation', to: 'tokens#confirm'
    post 'resend_confirmation', to: 'tokens#resend_confirmation'
  end
  
  # Other existing routes...
end
```

## 6. Update Devise Configuration

Modify the Devise initializer to configure confirmable settings:

```ruby
# config/initializers/devise.rb
Devise.setup do |config|
  # Existing configuration...
  
  # Configure email sender
  config.mailer_sender = '<EMAIL>'
  
  # Configure confirmation settings
  config.confirm_within = 2.days  # Token expires after 2 days
  config.reconfirmable = true     # Track email changes with reconfirmation
  
  # Allow unconfirmed access for some period (optional)
  config.allow_unconfirmed_access_for = 0.days  # No access until confirmed
  
  # Other configuration...
end
```

## 7. Update Devise-API Configuration

Ensure your Devise-API configuration is properly set up:

```ruby
# config/initializers/devise_api.rb
DeviseApi.setup do |config|
  # Existing configuration...
  
  # Configure JWT to include confirmed status in payload
  config.jwt_payload do |user|
    {
      sub: user.id,
      confirmed: user.confirmed?,
      username: user.username
      # Other custom claims...
    }
  end
  
  # Other configuration...
end
```

## 8. Customize Email Templates

Create custom email templates for confirmation:

```erb
<!-- app/views/devise/mailer/confirmation_instructions.html.erb -->
<p>Welcome <%= @email %>!</p>

<p>You can confirm your BragRights Football account through the link below:</p>

<p><%= link_to 'Confirm my account', "#{ENV['FRONTEND_URL']}/confirm?token=#{@token}" %></p>

<p>If you have any questions, please contact <NAME_EMAIL>.</p>

<p>Thank you,<br>The BragRights Football Team</p>
```

## 9. Create a Custom Mailer (Optional)

For more customization, create a custom mailer:

```ruby
# app/mailers/custom_devise_mailer.rb
class CustomDeviseMailer < Devise::Mailer
  helper :application
  include Devise::Controllers::UrlHelpers
  default template_path: 'devise/mailer'
  
  def confirmation_instructions(record, token, opts={})
    # Add custom logic here
    @token = token
    @frontend_url = "#{ENV['FRONTEND_URL']}/confirm?token=#{token}"
    
    # Set BragRights Football specific headers or content
    headers['X-BragRights-Football'] = 'Authentic Email'
    
    mail(to: record.email, subject: 'Confirm Your BragRights Football Account') do |format|
      format.html { render "devise/mailer/confirmation_instructions" }
    end
  end
end
```

Then update Devise to use this mailer:

```ruby
# config/initializers/devise.rb
Devise.setup do |config|
  # Other configuration...
  config.mailer = 'CustomDeviseMailer'
end
```

## 10. Add Authentication Check for Confirmed Users

Create a concern to check if users are confirmed:

```ruby
# app/controllers/concerns/require_confirmation.rb
module RequireConfirmation
  extend ActiveSupport::Concern

  included do
    before_action :check_confirmed_email
  end

  private

  def check_confirmed_email
    unless current_user&.confirmed?
      render json: {
        status: :error,
        message: 'You need to confirm your email address before continuing'
      }, status: :unauthorized
      return
    end
  end
end
```

Apply this concern to controllers that require confirmed users:

```ruby
# app/controllers/api/v1/leagues_controller.rb
module Api
  module V1
    class LeaguesController < ApplicationController
      include RequireConfirmation  # Add this line
      
      # Existing controller code...
    end
  end
end
```

## 11. Frontend Integration

For your frontend implementation, you'll need to create:

1. A confirmation page that handles the token from the email link
2. A resend confirmation option in case the email is not received
3. UI indicators showing unconfirmed status to the user

Example confirmation page (React):

```jsx
// Confirmation.jsx
import { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import axios from 'axios';

const Confirmation = () => {
  const [status, setStatus] = useState('confirming');
  const [message, setMessage] = useState('');
  const location = useLocation();
  const navigate = useNavigate();
  
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const token = params.get('token');
    
    if (!token) {
      setStatus('error');
      setMessage('No confirmation token found');
      return;
    }
    
    const confirmAccount = async () => {
      try {
        const response = await axios.get(`${API_URL}/confirmation?confirmation_token=${token}`);
        
        // Store the new token
        localStorage.setItem('authToken', response.data.data.token);
        
        setStatus('success');
        setMessage(response.data.message);
        
        // Redirect after a short delay
        setTimeout(() => {
          navigate('/dashboard');
        }, 3000);
      } catch (error) {
        setStatus('error');
        setMessage(error.response?.data?.message || 'Confirmation failed');
      }
    };
    
    confirmAccount();
  }, [location, navigate]);
  
  return (
    <div className="confirmation-page">
      {status === 'confirming' && <p>Confirming your account...</p>}
      {status === 'success' && (
        <div className="success">
          <h2>Account Confirmed!</h2>
          <p>{message}</p>
          <p>Redirecting to dashboard...</p>
        </div>
      )}
      {status === 'error' && (
        <div className="error">
          <h2>Confirmation Failed</h2>
          <p>{message}</p>
          <button onClick={() => navigate('/resend-confirmation')}>
            Resend Confirmation Email
          </button>
        </div>
      )}
    </div>
  );
};

export default Confirmation;
```

## 12. Testing the Verification Flow

Test your implementation with these steps:

1. Register a new user via your API endpoint
2. Check the email sent to the user's email address
3. Click the confirmation link in the email
4. Verify the user is now confirmed in the database
5. Verify the user can access protected endpoints

## 13. Handling Password Reset with Confirmation

If you also want to implement password reset functionality:

```ruby
# app/controllers/tokens_controller.rb
# Add these methods

def forgot_password
  user = User.find_by(email: params[:email])
  
  if user
    user.send_reset_password_instructions
    render json: {
      status: :success,
      message: 'Password reset instructions have been sent to your email'
    }
  else
    render json: {
      status: :error,
      message: 'Email not found'
    }, status: :not_found
  end
end

def reset_password
  token = params[:reset_password_token]
  password = params[:password]
  password_confirmation = params[:password_confirmation]
  
  user = User.reset_password_by_token(
    reset_password_token: token,
    password: password,
    password_confirmation: password_confirmation
  )
  
  if user.errors.empty?
    # Generate token after password reset
    @token = Devise::Api.generate_token(user)
    
    render json: {
      status: :success,
      message: 'Your password has been changed successfully',
      data: {
        user: user,
        token: @token
      }
    }
  else
    render json: {
      status: :error,
      message: 'Password reset failed',
      errors: user.errors.full_messages
    }, status: :unprocessable_entity
  end
end
```

Then update your routes:

```ruby
# config/routes.rb
devise_scope :user do
  # Existing routes...
  post 'forgot_password', to: 'tokens#forgot_password'
  put 'reset_password', to: 'tokens#reset_password'
end
```

## 14. Security Considerations

1. **Set appropriate token expiration times**:
   ```ruby
   # config/initializers/devise.rb
   config.confirm_within = 2.days
   config.reset_password_within = 6.hours
   ```

2. **Add rate limiting to prevent abuse**:
   ```ruby
   # Gemfile
   gem 'rack-attack'
   ```
   
   ```ruby
   # config/initializers/rack_attack.rb
   class Rack::Attack
     # Throttle confirmation attempts for a given email parameter
     throttle('confirmation_by_ip', limit: 5, period: 3600) do |req|
       req.ip if req.path == '/confirmation' && req.get?
     end
     
     # Throttle password reset requests
     throttle('forgot_password_by_ip', limit: 3, period: 3600) do |req|
       req.ip if req.path == '/forgot_password' && req.post?
     end
   end
   ```

3. **Secure your environment variables**:
   - Use a secure password for your Namecheap email
   - Store sensitive credentials in Rails credentials or a secure environment variable service