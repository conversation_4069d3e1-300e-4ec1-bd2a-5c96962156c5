# Competition Colors Implementation Guide

This document provides guidelines for implementing competition colors in the Brag Rights application.

## Overview

Competition colors are visual elements used to represent football competitions (leagues, tournaments) throughout the application. These colors are used for UI elements such as headers, backgrounds, and borders to create a consistent visual identity for each competition.

## Color Storage Format

Competition colors should be stored as an object with the following structure:

```json
{
  "primary": "#FF0000",
  "secondary": "#FFFFFF",
  "tertiary": "#000000"  // Optional
}
```

Key points:
- All colors should be stored as hex values
- The `primary` color is the main brand color of the competition
- The `secondary` color is used for contrast and complementary elements
- The `tertiary` color is optional and should only be included if needed for additional UI elements

## Database Schema

The Competition model should include a `colors` field to store the color information:

```ruby
# Example migration
class AddColorsToCompetitions < ActiveRecord::Migration[7.0]
  def change
    add_column :competitions, :colors, :jsonb, default: {}
  end
end
```

## Default Colors

If a competition doesn't have specific colors defined, the system should use default colors based on the competition type:

| Competition Type | Primary Color | Secondary Color | Tertiary Color |
|------------------|---------------|----------------|----------------|
| League           | #0A1647       | #FFFFFF        | #E31B23        |
| Cup              | #1A5E24       | #FFFFFF        | #FFD700        |
| International    | #003399       | #FFFFFF        | #FFCC00        |

## API Serialization

Competition colors should be included in the competition serializers:

```ruby
class CompetitionSerializer < ActiveModel::Serializer
  attributes :id, :name, :code, :type, :emblem_url, :colors

  def colors
    object.colors.presence || default_colors_for(object.type)
  end

  private

  def default_colors_for(type)
    case type
    when 'LEAGUE'
      { primary: '#0A1647', secondary: '#FFFFFF', tertiary: '#E31B23' }
    when 'CUP'
      { primary: '#1A5E24', secondary: '#FFFFFF', tertiary: '#FFD700' }
    else
      { primary: '#003399', secondary: '#FFFFFF', tertiary: '#FFCC00' }
    end
  end
end
```

## Frontend Updates

The frontend should be able to:
1. Retrieve competition colors from the API
2. Apply these colors to UI components
3. Ensure proper contrast for text and other elements
4. Fall back to default colors if specific colors are not provided

Example usage in frontend:

```typescript
// Example React component using competition colors
const CompetitionHeader: React.FC<{ competition: Competition }> = ({ competition }) => {
  const { colors } = competition;
  
  return (
    <header 
      style={{ 
        backgroundColor: colors.primary,
        color: colors.secondary,
        borderBottom: `3px solid ${colors.tertiary || colors.secondary}`
      }}
    >
      <h1>{competition.name}</h1>
      <img src={competition.emblem_url} alt={`${competition.name} logo`} />
    </header>
  );
};
```

## Admin Interface

The admin interface should allow updating competition colors:

1. Provide color pickers for primary, secondary, and tertiary colors
2. Show a preview of how the colors will look in the UI
3. Allow resetting to default colors
4. Validate that colors provide sufficient contrast for accessibility

## Color Accessibility

When implementing competition colors, ensure:

1. Text has sufficient contrast against background colors (WCAG AA compliance)
2. Don't rely on color alone to convey information
3. Test with color blindness simulators
4. Provide alternative styling for users with high contrast mode enabled

## Testing

When testing competition color functionality:

1. Verify colors are correctly stored in the database
2. Test serialization of colors in API responses
3. Ensure default colors are applied when specific colors are missing
4. Test color contrast for accessibility compliance

## Implementation Steps

1. Add the `colors` field to the Competition model
2. Update the CompetitionSerializer to include colors
3. Add color management to the admin interface
4. Update frontend components to use competition colors
5. Add default colors for different competition types
6. Test for accessibility and proper display
