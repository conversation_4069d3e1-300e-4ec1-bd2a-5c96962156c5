# API Endpoints Documentation

This directory contains documentation for the API endpoints in the Brag Rights application. These guides describe how the frontend interacts with the backend API without including UI/UX implementation details.

## Contents

- **admin_competition_management_api.md** - API specifications for competition management (admin only)
- **authentication_api.md** - API specifications for user registration, login, and authentication
- **competition_teams_api.md** - API specifications for retrieving teams in a competition
- **competitions_api.md** - API specifications for retrieving competition information
- **favorite_team_api.md** - API specifications for managing favorite teams
- **join_requests_api.md** - API specifications for managing league join requests
- **league_standings_api.md** - API specifications for retrieving league standings
- **leagues_api.md** - API specifications for creating and managing leagues
- **match_predictions_api.md** - API specifications for submitting match predictions
- **matches_api.md** - API specifications for retrieving match information
- **new_season_requirements_api.md** - API specifications for creating new seasons (admin only)
- **predictable_matches_api.md** - API specifications for retrieving matches available for prediction
- **round_predictions_api.md** - API specifications for managing round predictions
- **seasons_api.md** - API specifications for retrieving season information
- **team_standings_api.md** - API specifications for retrieving team fan standings
- **user_api.md** - API specifications for retrieving and updating user information
- **user_statistics_api.md** - API specifications for retrieving detailed user statistics

## Purpose

These guides serve as a reference for both frontend and backend developers to understand:

- Available API endpoints
- Required parameters
- Response formats
- Authentication requirements
- Error handling

Each guide focuses solely on the API contract between frontend and backend, without prescribing specific UI/UX implementations. Frontend developers are free to implement the UI as they see fit, as long as they adhere to the API specifications.

## Format

Each API documentation file follows a consistent format:

1. **Overview** - Brief description of the API's purpose
2. **Endpoints** - List of available endpoints with their HTTP methods and paths
3. **Parameters** - Required and optional parameters for each endpoint
4. **Response Format** - Structure of the response data
5. **Examples** - Example requests and responses
6. **Error Handling** - Possible error responses and their meanings

## Usage

When implementing new features that require API interaction:

1. Refer to these guides to understand the available endpoints
2. Ensure your requests include all required parameters
3. Handle all possible response scenarios, including errors
4. If you need a new endpoint or changes to an existing one, coordinate with the backend team
