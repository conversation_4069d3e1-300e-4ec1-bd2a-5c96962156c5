# Competitions API

## Overview

The Competitions API provides endpoints for retrieving information about football competitions available in the system. These endpoints allow users to view all competitions, get details about a specific competition, and access teams participating in a competition.

## Endpoints

### List All Competitions

Retrieve a list of all available competitions.

```
GET /api/v1/competitions
```

#### Authentication

No authentication required.

#### Success Response (200 OK)

```json
{
  "data": [
    {
      "id": 1,
      "code": "PL",
      "name": "Premier League",
      "emblem_public_id": "competitions/emblems/pl",
      "gender": "male",
      "competition_type": "LEAGUE",
      "colors": {
        "primary": "#3D195B",
        "secondary": "#FFFFFF",
        "tertiary": "#E90052"
      },
      "area": {
        "id": 1,
        "name": "England",
        "code": "ENG",
        "flag_public_id": "areas/flags/england"
      },
      "current_season": {
        "id": 1,
        "current_matchday": 10,
        "overall_matchday": 10,
        "start_date": "2023-08-11",
        "end_date": "2024-05-19"
      }
    },
    {
      "id": 2,
      "code": "CL",
      "name": "UEFA Champions League",
      "emblem_public_id": "competitions/emblems/cl",
      "gender": "male",
      "competition_type": "CUP",
      "colors": {
        "primary": "#000000",
        "secondary": "#FFFFFF",
        "tertiary": "#0E1E5B"
      },
      "area": {
        "id": 2,
        "name": "Europe",
        "code": "EUR",
        "flag_public_id": "areas/flags/europe"
      },
      "current_season": {
        "id": 2,
        "current_matchday": 6,
        "overall_matchday": 6,
        "start_date": "2023-09-19",
        "end_date": "2024-06-01"
      }
    }
  ],
  "message": ["Competitions found"],
  "status": 200,
  "type": "success"
}
```

### Get Competition Details

Retrieve detailed information about a specific competition.

```
GET /api/v1/competitions/:id
```

#### Parameters

- `:id` - The ID or code of the competition (e.g., `1` or `PL`)

#### Authentication

No authentication required.

#### Success Response (200 OK)

```json
{
  "data": {
    "id": 1,
    "code": "PL",
    "name": "Premier League",
    "emblem_public_id": "competitions/emblems/pl",
    "gender": "male",
    "competition_type": "LEAGUE",
    "colors": {
      "primary": "#3D195B",
      "secondary": "#FFFFFF",
      "tertiary": "#E90052"
    },
    "area": {
      "id": 1,
      "name": "England",
      "code": "ENG",
      "flag_public_id": "areas/flags/england"
    },
    "current_season": {
      "id": 1,
      "current_matchday": 10,
      "overall_matchday": 10,
      "start_date": "2023-08-11",
      "end_date": "2024-05-19",
      "match_days": 38,
      "stage": "REGULAR_SEASON",
      "teams_count": 20,
      "created_at": "2023-08-01T00:00:00.000Z",
      "updated_at": "2023-10-15T00:00:00.000Z"
    },
    "current_winner": {
      "id": 1,
      "name": "Manchester City",
      "short_name": "Man City",
      "crest_public_id": "teams/crests/man_city"
    }
  },
  "message": ["Competition found"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

**Competition Not Found (404 Not Found)**

```json
{
  "errors": ["Competition not found"],
  "status": 404
}
```

### Get Competition Teams

Retrieve a list of teams participating in a competition's current season.

```
GET /api/v1/competitions/:id/teams
```

#### Parameters

- `:id` - The ID or code of the competition (e.g., `1` or `PL`)

#### Authentication

No authentication required.

#### Success Response (200 OK)

```json
{
  "data": [
    {
      "id": 1,
      "name": "Arsenal",
      "short_name": "Arsenal",
      "tla": "ARS",
      "crest_public_id": "teams/crests/arsenal",
      "club_colors": {
        "home": {
          "primary": "#EF0107",
          "secondary": "#FFFFFF"
        },
        "away": {
          "primary": "#000000",
          "secondary": "#EF0107"
        }
      },
      "venue": "Emirates Stadium",
      "form": "WWDWL"
    },
    {
      "id": 2,
      "name": "Aston Villa",
      "short_name": "Aston Villa",
      "tla": "AVL",
      "crest_public_id": "teams/crests/aston_villa",
      "club_colors": {
        "home": {
          "primary": "#95BFE5",
          "secondary": "#670E36"
        },
        "away": {
          "primary": "#000000",
          "secondary": "#95BFE5"
        }
      },
      "venue": "Villa Park",
      "form": "LWWDW"
    }
  ],
  "message": ["Teams found"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

**Competition Not Found (404 Not Found)**

```json
{
  "errors": ["Competition not found"],
  "status": 404
}
```

## Implementation Notes

1. **Competition Codes**: Each competition has a unique code (e.g., "PL" for Premier League, "CL" for Champions League) that can be used as an alternative to the ID in API requests.
2. **Competition Types**: Competitions can be of type "LEAGUE" (regular league format) or "CUP" (knockout tournament format).
3. **Competition Colors**: Each competition has a set of colors (primary, secondary, tertiary) that can be used for UI styling.
4. **Current Season**: The current_season field contains information about the active season for the competition.
5. **Current Winner**: For leagues, the current_winner field indicates the team that won the previous season.
6. **Team Form**: The form field in team data represents the team's recent performance as a string of characters (W = Win, D = Draw, L = Loss), with the most recent result first.

## Frontend Integration

The frontend should:

1. Display a list of available competitions with their emblems and basic information
2. Provide detailed views for each competition, including current season information
3. Show the teams participating in each competition with their crests and colors
4. Use competition colors for UI styling elements
5. Display the current matchday and other relevant season information
