# Authentication API

## Overview

The Authentication API provides endpoints for user registration, login, logout, and email verification. It uses JW<PERSON> (JSON Web Tokens) for authentication, with access and refresh tokens stored in HTTP-only cookies.

## Endpoints

### User Registration

Register a new user account.

```
POST /users/tokens/sign_up
```

#### Request Body

```json
{
  "username": "user123",
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Success Response (200 OK)

```json
{
  "status": "success",
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "user123",
    "created_at": "2023-01-01T12:00:00.000Z",
    "updated_at": "2023-01-01T12:00:00.000Z"
  }
}
```

#### Error Responses

**Username Already Taken (422 Unprocessable Entity)**

```json
{
  "status": "error",
  "message": "Username already taken",
  "errors": {
    "username": ["has already been taken"]
  }
}
```

**Email Already Taken (422 Unprocessable Entity)**

```json
{
  "status": "error",
  "message": "Email already taken",
  "errors": {
    "email": ["has already been taken"]
  }
}
```

**Validation Errors (422 Unprocessable Entity)**

```json
{
  "status": "error",
  "message": "Validation failed",
  "errors": {
    "password": ["is too short (minimum is 6 characters)"]
  }
}
```

### User Login

Authenticate a user and receive access and refresh tokens.

```
POST /users/tokens/sign_in
```

#### Request Body

```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Success Response (200 OK)

```json
{
  "status": "success",
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "user123",
    "created_at": "2023-01-01T12:00:00.000Z",
    "updated_at": "2023-01-01T12:00:00.000Z"
  }
}
```

The response will also include HTTP-only cookies containing the access and refresh tokens.

#### Error Responses

**Invalid Credentials (401 Unauthorized)**

```json
{
  "status": "error",
  "message": "Invalid email or password",
  "errors": {
    "base": ["Invalid email or password"]
  }
}
```

**Account Not Confirmed (401 Unauthorized)**

```json
{
  "status": "error",
  "message": "You need to confirm your email address before continuing",
  "errors": {
    "base": ["You need to confirm your email address before continuing"]
  }
}
```

### Token Refresh

Refresh an expired access token using the refresh token.

```
POST /users/tokens/refresh
```

#### Request

No request body is needed. The refresh token is sent in the HTTP-only cookie.

#### Success Response (200 OK)

```json
{
  "status": "success",
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "user123",
    "created_at": "2023-01-01T12:00:00.000Z",
    "updated_at": "2023-01-01T12:00:00.000Z"
  }
}
```

The response will include a new access token in an HTTP-only cookie.

#### Error Responses

**Invalid Refresh Token (401 Unauthorized)**

```json
{
  "status": "error",
  "message": "Invalid refresh token",
  "errors": {
    "base": ["Invalid refresh token"]
  }
}
```

**Expired Refresh Token (401 Unauthorized)**

```json
{
  "status": "error",
  "message": "Refresh token has expired",
  "errors": {
    "base": ["Refresh token has expired"]
  }
}
```

### User Logout

Revoke the user's access and refresh tokens.

```
POST /users/tokens/revoke
```

#### Request

No request body is needed. The tokens are sent in HTTP-only cookies.

#### Success Response (200 OK)

```json
{
  "status": "success",
  "message": "Logged out successfully"
}
```

The response will also clear the HTTP-only cookies containing the tokens.

### Email Verification

Verify a user's email address using the confirmation token.

```
GET /confirmation
```

#### Query Parameters

- `confirmation_token`: The token sent to the user's email address

#### Success Response (200 OK)

```json
{
  "status": "success",
  "message": "Email confirmed successfully"
}
```

#### Error Responses

**Invalid Token (422 Unprocessable Entity)**

```json
{
  "status": "error",
  "message": "Invalid confirmation token",
  "errors": {
    "base": ["Invalid confirmation token"]
  }
}
```

**Expired Token (422 Unprocessable Entity)**

```json
{
  "status": "error",
  "message": "Confirmation token has expired",
  "errors": {
    "base": ["Confirmation token has expired"]
  }
}
```

### Resend Confirmation Email

Resend the confirmation email to the user.

```
POST /resend_confirmation
```

#### Request Body

```json
{
  "email": "<EMAIL>"
}
```

#### Success Response (200 OK)

```json
{
  "status": "success",
  "message": "Confirmation email sent"
}
```

#### Error Responses

**User Not Found (404 Not Found)**

```json
{
  "status": "error",
  "message": "User not found",
  "errors": {
    "base": ["User not found"]
  }
}
```

**Email Already Confirmed (422 Unprocessable Entity)**

```json
{
  "status": "error",
  "message": "Email already confirmed",
  "errors": {
    "base": ["Email already confirmed"]
  }
}
```

## Implementation Notes

1. **Token Storage**: Access and refresh tokens are stored in HTTP-only cookies to prevent XSS attacks.
2. **Token Expiration**: Access tokens expire after 1 hour, refresh tokens after 7 days.
3. **CSRF Protection**: The API uses CSRF tokens for protection against CSRF attacks.
4. **Rate Limiting**: Authentication endpoints are rate-limited to prevent brute force attacks.
5. **Email Verification**: Users must verify their email address before they can log in.

## Frontend Integration

The frontend should:

1. Handle form submission for registration and login
2. Store user data in client-side state after successful authentication
3. Implement automatic token refresh when the access token expires
4. Provide UI for email verification
5. Handle error messages appropriately
