# Matches API

## Overview

The Matches API provides endpoints for retrieving information about football matches. These endpoints allow users to view matches for specific competitions, seasons, and matchdays.

## Endpoints

### List Matches

Retrieve a list of matches, filterable by competition, season, and matchday.

```
GET /api/v1/matches
```

#### Query Parameters

- `competition_id` (optional) - Filter matches by competition ID
- `season_id` (optional) - Filter matches by season ID
- `matchday` (optional) - Filter matches by matchday number
- `status` (optional) - Filter matches by status (e.g., "SCHEDULED", "FINISHED")
- `from_date` (optional) - Filter matches from this date (format: YYYY-MM-DD)
- `to_date` (optional) - Filter matches to this date (format: YYYY-MM-DD)
- `page` (optional) - Page number for pagination (default: 1)
- `per_page` (optional) - Number of matches per page (default: 20)

#### Authentication

No authentication required.

#### Success Response (200 OK)

```json
{
  "data": [
    {
      "id": 1,
      "competition_id": 1,
      "season_id": 1,
      "utc_date": "2023-10-15T14:00:00.000Z",
      "status": "FINISHED",
      "matchday": 8,
      "stage": "REGULAR_SEASON",
      "group": null,
      "last_updated": "2023-10-15T16:00:00.000Z",
      "home_team": {
        "id": 1,
        "name": "Arsenal",
        "short_name": "Arsenal",
        "tla": "ARS",
        "crest_public_id": "teams/crests/arsenal"
      },
      "away_team": {
        "id": 2,
        "name": "Aston Villa",
        "short_name": "Aston Villa",
        "tla": "AVL",
        "crest_public_id": "teams/crests/aston_villa"
      },
      "score": {
        "winner": "HOME_TEAM",
        "duration": "REGULAR",
        "full_time": {
          "home": 2,
          "away": 1
        },
        "half_time": {
          "home": 1,
          "away": 0
        }
      },
      "referees": [
        {
          "id": 1,
          "name": "Michael Oliver",
          "type": "REFEREE"
        }
      ]
    },
    {
      "id": 2,
      "competition_id": 1,
      "season_id": 1,
      "utc_date": "2023-10-15T16:30:00.000Z",
      "status": "FINISHED",
      "matchday": 8,
      "stage": "REGULAR_SEASON",
      "group": null,
      "last_updated": "2023-10-15T18:30:00.000Z",
      "home_team": {
        "id": 3,
        "name": "Chelsea",
        "short_name": "Chelsea",
        "tla": "CHE",
        "crest_public_id": "teams/crests/chelsea"
      },
      "away_team": {
        "id": 4,
        "name": "Liverpool",
        "short_name": "Liverpool",
        "tla": "LIV",
        "crest_public_id": "teams/crests/liverpool"
      },
      "score": {
        "winner": "AWAY_TEAM",
        "duration": "REGULAR",
        "full_time": {
          "home": 1,
          "away": 2
        },
        "half_time": {
          "home": 1,
          "away": 1
        }
      },
      "referees": [
        {
          "id": 2,
          "name": "Anthony Taylor",
          "type": "REFEREE"
        }
      ]
    }
  ],
  "meta": {
    "current_page": 1,
    "total_pages": 5,
    "total_count": 92
  },
  "message": ["Matches found"],
  "status": 200,
  "type": "success"
}
```

## Implementation Notes

1. **Match Status**: Matches can have various statuses, including "SCHEDULED", "LIVE", "IN_PLAY", "PAUSED", "FINISHED", "POSTPONED", "SUSPENDED", "CANCELED".
2. **Match Stages**: For cup competitions, matches can be in different stages (e.g., "GROUP_STAGE", "ROUND_OF_16", "QUARTER_FINALS", "SEMI_FINALS", "FINAL").
3. **Match Groups**: For cup competitions with group stages, matches can be assigned to specific groups (e.g., "GROUP_A", "GROUP_B").
4. **Match Duration**: The duration field in the score object indicates whether the match went to extra time or penalties ("REGULAR", "EXTRA_TIME", "PENALTIES").
5. **Match Winners**: The winner field in the score object can be "HOME_TEAM", "AWAY_TEAM", or "DRAW".
6. **Pagination**: The list matches endpoint supports pagination to handle large numbers of matches.
7. **Date Filtering**: The list matches endpoint supports filtering by date range to help users find matches within a specific time period.

## Frontend Integration

The frontend should:

1. Display matches in a user-friendly format, grouped by date and time
2. Show team crests and names for each match
3. Indicate match status (scheduled, live, finished, postponed, etc.)
4. Display scores for finished matches
5. Implement filtering by competition, season, and matchday
6. Handle pagination for large result sets
7. Display postponed matches appropriately within the regular match list
