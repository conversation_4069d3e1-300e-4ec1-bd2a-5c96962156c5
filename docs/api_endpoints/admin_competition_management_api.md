# Admin Competition Management API Guide

## Overview

This API provides administrative endpoints for managing competitions, seasons, teams, and matches in the BragRights system. These endpoints are only accessible to users with admin privileges and are essential for maintaining the system's data integrity.

## Authentication

All endpoints require:

- A valid authentication token (Bearer token)
- Admin user privileges (`admin: true` in users table)

## Base URL

All endpoints are prefixed with `/api/v1/admin/`

## Endpoints

### List All Competitions

```http
GET /api/v1/admin/competitions
```

Returns a list of all competitions with detailed administrative information including usage statistics.

#### Response

```json
{
  "data": [
    {
      "id": 1,
      "code": "PL",
      "name": "Premier League",
      "emblem_public_id": "competitions/emblems/pl",
      "gender": "male",
      "competition_type": "LEAGUE",
      "created_at": "2024-01-01T00:00:00.000Z",
      "updated_at": "2024-01-01T00:00:00.000Z",
      "area": {
        "id": 1,
        "name": "England",
        "code": "ENG",
        "flag_public_id": "areas/flags/england"
      },
      "current_winner": {
        "id": 1,
        "name": "Manchester City",
        "short_name": "Man City",
        "crest_public_id": "teams/crests/man_city"
      },
      "current_season": {
        "id": 1,
        "current_matchday": 10,
        "overall_matchday": 10,
        "start_date": "2023-08-11",
        "end_date": "2024-05-19",
        "match_days": 38,
        "stage": "REGULAR_SEASON",
        "teams_count": 20,
        "created_at": "2024-01-01T00:00:00.000Z",
        "updated_at": "2024-01-01T00:00:00.000Z"
      },
      "stats": {
        "leagues_count": 15,
        "user_count": 150,
        "total_matches": 380,
        "finished_matches": 90,
        "upcoming_matches": 290,
        "teams_with_predictions": 18
      },
      "teams_count": 20
    }
  ],
  "message": ["Competitions found"],
  "status": 200,
  "type": "success"
}
```

### Get Competition Details

```http
GET /api/v1/admin/competitions/:id
```

Returns detailed information about a specific competition. The `:id` parameter can be either:

- Numeric ID (e.g., "1")
- Competition code (e.g., "PL", "CL", "SV")

#### Response

Same structure as the list endpoint, but for a single competition.

### Initialize or Update Competition

```http
POST /api/v1/admin/competitions/initialize_or_update
```

Initializes a new competition or updates an existing one by fetching data from the appropriate external API.

#### Request Body

```json
{
  "code": "PL" // Competition code: "PL", "CL", or "SV"
}
```

#### Response

Returns the updated competition data in the CompetitionSerializer format.

### Update Teams

```http
POST /api/v1/admin/competitions/:id/update_teams
```

Fetches and updates the teams for the specified competition from the appropriate external API:

- football-data.org for PL and CL
- api-football for SV

#### Response

Returns the updated competition data including the new teams information.

### Update Matches

```http
POST /api/v1/admin/competitions/:id/update_matches
```

Fetches and updates the matches for the current season of the specified competition. For cup competitions, this will also trigger a stage transition check.

#### Response

Returns the updated competition data including the new matches statistics.

### Get Current Season Teams

```http
GET /api/v1/admin/competitions/:id/current_season_teams
```

Returns a simplified list of teams in the current season. This endpoint is particularly useful when setting up a new competition and needing to set the current winner.

#### Response

```json
{
  "data": [
    {
      "id": 1,
      "name": "Arsenal",
      "short_name": "Arsenal",
      "crest_public_id": "teams/crests/arsenal"
    }
  ],
  "message": ["Teams found"],
  "status": 200,
  "type": "success"
}
```

### Set Competition Winner

```http
POST /api/v1/admin/competitions/:id/set_winner
```

Sets the current winner for a competition. The winner must be a team that belongs to the competition's current season.

#### Request Body

```json
{
  "team_id": 1
}
```

#### Response

```json
{
  "data": {
    "id": 1,
    "code": "PL",
    "name": "Premier League",
    // ... other competition fields ...
    "current_winner": {
      "id": 1,
      "name": "Manchester City",
      "short_name": "Man City",
      "crest_public_id": "teams/crests/man_city"
    }
  },
  "message": ["Competition winner updated successfully"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

##### Team Not Found (404)

```json
{
  "errors": ["Team not found"],
  "status": 404
}
```

##### Invalid Team (400)

```json
{
  "errors": ["Team is not part of the current season"],
  "status": 400
}
```

## Error Responses

All endpoints may return the following error responses:

### Unauthorized (401)

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

### Forbidden (403)

```json
{
  "errors": ["Only admin can manage competitions"],
  "status": 403
}
```

### Not Found (404)

```json
{
  "errors": ["Competition not found"],
  "status": 404
}
```

### Unprocessable Entity (422)

```json
{
  "errors": ["Error message describing the issue"],
  "status": 422
}
```

### Internal Server Error (500)

```json
{
  "errors": ["Internal server error"],
  "status": 500
}
```

## Usage Examples

### Initialize Premier League Competition

```bash
curl -X POST \
  'https://api.bragrights.com/api/v1/admin/competitions/initialize_or_update' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{"code": "PL"}'
```

### Update Competition Teams

```bash
curl -X POST \
  'https://api.bragrights.com/api/v1/admin/competitions/1/update_teams' \
  -H 'Authorization: Bearer YOUR_TOKEN'
```

### Get Teams for Setting Winner

```bash
curl -X GET \
  'https://api.bragrights.com/api/v1/admin/competitions/PL/current_season_teams' \
  -H 'Authorization: Bearer YOUR_TOKEN'
```

## Setup Process

When setting up a new competition:

1. Initialize the competition using the `initialize_or_update` endpoint
2. Verify teams were imported correctly using the `current_season_teams` endpoint
3. Set the current winner if applicable
4. Update matches using the `update_matches` endpoint

## Rate Limits

Be mindful of external API rate limits when using these endpoints:

- football-data.org: 10 calls/minute
- api-football: Varies by subscription

## Caching

The system implements caching for certain responses:

- Competition data is cached for 12 hours
- Teams and matches have varying cache periods based on status
