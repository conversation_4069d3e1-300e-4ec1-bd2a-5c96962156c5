# PredictableMatches API Integration Guide

## Overview

We've implemented a new approach to match predictions that addresses scheduling issues. Instead of using `current_matchday` to determine which matches are available for prediction, we now use a time-based window. This ensures users can predict on all upcoming matches within a time frame (default: 7 days), even if matches from different matchdays get rescheduled.

## Why This Change?

- **Problem**: Using `current_matchday` caused issues when matches were rescheduled. Users would miss prediction opportunities if matches from future matchdays were moved earlier.
  
- **Solution**: The new `PredictableMatchesController` fetches matches based on date, not matchday, ensuring all upcoming matches within a specific time window are available for prediction.

## API Endpoints

### Get Predictable Matches

**Endpoint:** `GET /api/v1/predictable_matches`

**Parameters:**
- `competitionCode` - The competition code (e.g., "PL", "CL", "SV")

**Example Request:**
```http
GET /api/v1/predictable_matches?competitionCode=PL
```

**Response:**
```json
{
  "matches": [
    {
      "id": 123,
      "status": "TIMED",
      "stage": "REGULAR_SEASON",
      "matchday": 15,
      "utc_date": "2023-12-10T16:30:00Z",
      "homeTeam": {
        "id": 61,
        "name": "Chelsea FC",
        "shortName": "Chelsea",
        "tla": "CHE",
        "crestUrl": "https://example.com/crests/chelsea.png"
      },
      "awayTeam": {
        "id": 62,
        "name": "Everton FC",
        "shortName": "Everton",
        "tla": "EVE",
        "crestUrl": "https://example.com/crests/everton.png"
      },
      "score": {
        "fullTimeHome": null,
        "fullTimeAway": null,
        "halfTimeHome": null,
        "halfTimeAway": null,
        "winner": null
      }
    },
    // More matches sorted by date...
  ],
  "message": ["Predictable matches found"],
  "status": 200,
  "type": "success"
}
```

### Refresh Predictable Matches (Admin Only)

**Endpoint:** `GET /api/v1/predictable_matches/refresh`

**Parameters:**
- `competitionCode` - The competition code to refresh

**Example Request:**
```http
GET /api/v1/predictable_matches/refresh?competitionCode=PL
```

**Response (Success - Admin):**
```json
{
  "message": ["Match refresh scheduled successfully"],
  "status": 200,
  "type": "success"
}
```

**Response (Unauthorized - Non-Admin):**
```json
{
  "message": ["Not authorized to refresh matches manually"],
  "status": 403,
  "type": "error"
}
```

## Match Selection Logic

Even though matches are returned as a flat array sorted by date, the backend uses sophisticated logic to determine which matches to include:

### For League Competitions:

1. If all upcoming matches in the next 7 days are from a single matchday, it returns all matches for that matchday.

2. If upcoming matches span multiple matchdays:
   - It identifies the "current matchday" as the one with the most matches
   - It includes ALL matches for the current matchday, even those beyond 7 days
   - It includes matches from earlier matchdays (likely rescheduled or postponed matches)
   - It includes matches from later matchdays if the first match of that later matchday is within 3 days of the last match of the current matchday

### For Cup Competitions:

1. It considers both stage and matchday when selecting matches.
2. If matches span multiple stages, it prioritizes the current stage.
3. Within a stage, it applies the same logic as league competitions.

## Data Model

### Match Object

| Field | Type | Description |
|-------|------|-------------|
| id | Integer | Unique identifier for the match |
| status | String | Match status (e.g., "TIMED", "SCHEDULED", "IN_PLAY", "FINISHED") |
| stage | String | Competition stage (e.g., "REGULAR_SEASON", "LEAGUE_STAGE") |
| matchday | Integer | Competition matchday number |
| utc_date | String | Match start time in ISO 8601 format |
| homeTeam | Team | Home team object |
| awayTeam | Team | Away team object |
| score | Score | Current score object |

### Team Object

| Field | Type | Description |
|-------|------|-------------|
| id | Integer | Unique identifier for the team |
| name | String | Full team name |
| shortName | String | Abbreviated team name |
| tla | String | Three-letter team acronym |
| crestUrl | String | URL to team crest image |

### Score Object

| Field | Type | Description |
|-------|------|-------------|
| fullTimeHome | Integer | Home team full-time goals |
| fullTimeAway | Integer | Away team full-time goals |
| halfTimeHome | Integer | Home team half-time goals |
| halfTimeAway | Integer | Away team half-time goals |
| winner | String | Winner ("HOME_TEAM", "AWAY_TEAM", "DRAW", or null) |

## Implementation Notes

1. The "matches" endpoint is designed for historical data viewing
2. The "predictable_matches" endpoint is specifically for retrieving matches that users can predict
3. Background jobs automatically update match data at 6:00 AM daily and throughout match days
4. Requests to these endpoints do not trigger API calls to external services - data is kept current by background jobs

## Frontend Integration Examples

### Rendering Matches

```tsx
// Example React component for rendering matches
function MatchList({ matches }) {
  return (
    <div className="match-list">
      {matches.map(match => (
        <MatchCard 
          key={match.id} 
          match={match} 
          onPredict={handlePrediction} 
        />
      ))}
    </div>
  );
}
```

### Grouping by Date (if needed)

```tsx
// Group matches by date for display
function MatchesByDate({ matches }) {
  // Group matches by date
  const matchesByDate = matches.reduce((groups, match) => {
    const date = new Date(match.utc_date).toLocaleDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(match);
    return groups;
  }, {});
  
  return (
    <div>
      {Object.entries(matchesByDate).map(([date, dateMatches]) => (
        <div key={date}>
          <h3>{date}</h3>
          {dateMatches.map(match => (
            <MatchCard 
              key={match.id} 
              match={match} 
              onPredict={handlePrediction} 
            />
          ))}
        </div>
      ))}
    </div>
  );
}
```

### Grouping by Matchday (if needed)

```tsx
// Group matches by matchday for display
function MatchesByMatchday({ matches }) {
  // Group matches by matchday
  const matchesByMatchday = matches.reduce((groups, match) => {
    if (!groups[match.matchday]) {
      groups[match.matchday] = [];
    }
    groups[match.matchday].push(match);
    return groups;
  }, {});
  
  return (
    <div>
      {Object.entries(matchesByMatchday).map(([matchday, matchdayMatches]) => (
        <div key={matchday}>
          <h3>Matchday {matchday}</h3>
          {matchdayMatches.map(match => (
            <MatchCard 
              key={match.id} 
              match={match} 
              onPredict={handlePrediction} 
            />
          ))}
        </div>
      ))}
    </div>
  );
}
```

### Fetching Predictable Matches

```typescript
// Service function for fetching predictable matches
const fetchPredictableMatches = async (competitionCode) => {
  try {
    const response = await api.get(`/api/v1/predictable_matches?competitionCode=${competitionCode}`);
    return response.data.matches;
  } catch (error) {
    console.error('Failed to fetch predictable matches:', error);
    return [];
  }
};
```

## Error Handling

Standard error codes are used:
- 200: Success
- 403: Unauthorized (for admin-only operations)
- 404: No matches found
- 500: Server error

All responses include a "type" field with either "success" or "error".

