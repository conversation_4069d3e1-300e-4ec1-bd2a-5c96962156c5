# User Statistics API

## Overview

The User Statistics API provides endpoints for retrieving detailed statistics about a user's prediction performance. This API allows for filtering statistics by type, competition, season, and time period.

## Endpoints

### Get User Statistics

Retrieve detailed statistics for a specific user.

```
GET /api/v1/users/:user_id/statistics
```

#### Parameters

- `:user_id` - The ID of the user to retrieve statistics for
- `type` (query parameter, optional) - Type of statistics to retrieve:
  - `basic` (default): Basic performance metrics
  - `advanced`: Advanced metrics including consistency and streaks
  - `competition`: Competition-specific performance
  - `comparative`: Comparative metrics against other users
  - `all`: All available statistics
- `competition_id` (query parameter, optional) - Filter statistics by competition
- `season_id` (query parameter, optional) - Filter statistics by season
- `time_period` (query parameter, optional) - Filter statistics by time period (e.g., 'current_season', 'all_time', 'last_month')

#### Authentication

Requires a valid access token. Users can only view their own statistics unless they are an admin.

#### Success Response (200 OK)

##### Basic Statistics Response

```json
{
  "data": {
    "total_points": 450,
    "total_predictions": 296,
    "prediction_accuracy": 68.5,
    "perfect_predictions": {
      "count": 45,
      "percentage": 15.2
    },
    "correct_predictions": {
      "count": 158,
      "percentage": 53.3
    },
    "incorrect_predictions": {
      "count": 93,
      "percentage": 31.5
    },
    "highest_round_score": 27,
    "average_round_score": 14.5,
    "most_perfect_predictions_in_a_round": 3
  },
  "message": ["Statistics retrieved successfully"],
  "status": 200,
  "type": "success"
}
```

##### Advanced Statistics Response

```json
{
  "data": {
    "total_points": 450,
    "total_predictions": 296,
    "prediction_accuracy": 68.5,
    "perfect_predictions": {
      "count": 45,
      "percentage": 15.2
    },
    "correct_predictions": {
      "count": 158,
      "percentage": 53.3
    },
    "incorrect_predictions": {
      "count": 93,
      "percentage": 31.5
    },
    "highest_round_score": 27,
    "average_round_score": 14.5,
    "most_perfect_predictions_in_a_round": 3,
    "consistency_rating": 4.2,
    "streaks": {
      "current": 2,
      "longest": 5
    }
  },
  "message": ["Statistics retrieved successfully"],
  "status": 200,
  "type": "success"
}
```

##### Competition Statistics Response

```json
{
  "data": {
    "competition_performance": [
      {
        "competition_id": 1,
        "competition_name": "Premier League",
        "season_id": 1,
        "points": 320,
        "total_predictions": 200,
        "accuracy": 72.5,
        "perfect_predictions": {
          "count": 35,
          "percentage": 17.5
        },
        "correct_predictions": {
          "count": 110,
          "percentage": 55.0
        },
        "incorrect_predictions": {
          "count": 55,
          "percentage": 27.5
        },
        "favorite_team_performance": {
          "team_id": 5,
          "team_name": "Arsenal",
          "total_predictions": 38,
          "accuracy": 76.3,
          "perfect_predictions": {
            "count": 8,
            "percentage": 21.1
          },
          "correct_predictions": {
            "count": 21,
            "percentage": 55.2
          },
          "incorrect_predictions": {
            "count": 9,
            "percentage": 23.7
          }
        }
      },
      {
        "competition_id": 2,
        "competition_name": "Champions League",
        "season_id": 2,
        "points": 130,
        "total_predictions": 96,
        "accuracy": 59.4,
        "perfect_predictions": {
          "count": 10,
          "percentage": 10.4
        },
        "correct_predictions": {
          "count": 48,
          "percentage": 49.0
        },
        "incorrect_predictions": {
          "count": 38,
          "percentage": 40.6
        },
        "favorite_team_performance": null
      }
    ]
  },
  "message": ["Statistics retrieved successfully"],
  "status": 200,
  "type": "success"
}
```

##### Comparative Statistics Response

```json
{
  "data": {
    "total_points": 450,
    "global_rank": 12,
    "total_users": 156,
    "percentile_ranking": 92.3,
    "points_behind_leader": 78
  },
  "message": ["Statistics retrieved successfully"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

**Forbidden (403 Forbidden)**

```json
{
  "errors": ["You are not authorized to access this resource."],
  "status": 403
}
```

**Not Found (404 Not Found)**

```json
{
  "errors": ["User not found"],
  "status": 404
}
```

## Implementation Notes

1. **Performance Considerations**: Some statistics calculations may be resource-intensive. Consider implementing caching for frequently accessed statistics.

2. **Filtering**: The API supports filtering by competition, season, and time period to allow for more targeted analysis.

3. **Expandability**: The service is designed to be easily expanded with new statistic types in the future.

4. **Authorization**: Users can only view their own statistics unless they have admin privileges.

## Frontend Integration

The frontend should:

1. Provide a dedicated statistics page or section on the user profile
2. Implement tabs or filters for different types of statistics
3. Use appropriate visualizations (charts, graphs) to display statistics
4. Allow users to filter statistics by competition, season, or time period
5. Consider implementing progressive loading for complex statistics

## Future Enhancements

1. **Caching**: Implement Redis caching for frequently accessed statistics
2. **Scheduled Calculations**: Calculate complex statistics asynchronously during off-peak hours
3. **Personalization**: Allow users to customize which statistics they see
4. **Achievements**: Create badges or achievements based on statistical milestones
5. **Comparative Analysis**: Expand comparative statistics to include league-specific comparisons
6. **Historical Tracking**: Add time-series data to track improvement over time
