# Leagues API

## Overview

The Leagues API provides endpoints for creating, retrieving, and managing user leagues within competitions. These endpoints allow users to create their own leagues, join existing leagues, and manage league memberships.

## Endpoints

### List Leagues

Retrieve a list of leagues, optionally filtered by competition.

```
GET /api/v1/leagues
```

#### Query Parameters

- `competition_id` (optional) - Filter leagues by competition ID
- `page` (optional) - Page number for pagination (default: 1)
- `per_page` (optional) - Number of leagues per page (default: 20)

#### Authentication

Requires a valid access token.

#### Success Response (200 OK)

```json
{
  "data": [
    {
      "id": 1,
      "name": "Friends League",
      "open": true,
      "starting_matchday": 1,
      "competition_id": 1,
      "competition_name": "Premier League",
      "competition_code": "PL",
      "owner_id": 1,
      "owner_username": "user123",
      "members_count": 8,
      "created_at": "2023-08-01T12:00:00.000Z",
      "updated_at": "2023-08-01T12:00:00.000Z",
      "user_is_member": true,
      "user_is_owner": true
    },
    {
      "id": 2,
      "name": "Office League",
      "open": false,
      "starting_matchday": 5,
      "competition_id": 1,
      "competition_name": "Premier League",
      "competition_code": "PL",
      "owner_id": 2,
      "owner_username": "other_user",
      "members_count": 12,
      "created_at": "2023-08-05T12:00:00.000Z",
      "updated_at": "2023-08-05T12:00:00.000Z",
      "user_is_member": false,
      "user_is_owner": false,
      "user_has_pending_request": true
    }
  ],
  "meta": {
    "current_page": 1,
    "total_pages": 3,
    "total_count": 45
  },
  "message": ["Leagues found"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

### Get League Details

Retrieve detailed information about a specific league.

```
GET /api/v1/leagues/:id
```

#### Parameters

- `:id` - The ID of the league to retrieve

#### Authentication

Requires a valid access token.

#### Success Response (200 OK)

```json
{
  "data": {
    "id": 1,
    "name": "Friends League",
    "open": true,
    "starting_matchday": 1,
    "competition_id": 1,
    "competition_name": "Premier League",
    "competition_code": "PL",
    "owner_id": 1,
    "owner_username": "user123",
    "members_count": 8,
    "created_at": "2023-08-01T12:00:00.000Z",
    "updated_at": "2023-08-01T12:00:00.000Z",
    "user_is_member": true,
    "user_is_owner": true,
    "members": [
      {
        "id": 1,
        "username": "user123",
        "joined_at": "2023-08-01T12:00:00.000Z"
      },
      {
        "id": 3,
        "username": "friend1",
        "joined_at": "2023-08-02T12:00:00.000Z"
      }
    ],
    "season_id": 1,
    "season_start_date": "2023-08-11",
    "season_end_date": "2024-05-19"
  },
  "message": ["League found"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

**League Not Found (404 Not Found)**

```json
{
  "errors": ["League not found"],
  "status": 404
}
```

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

### Create League

Create a new league within a competition.

```
POST /api/v1/leagues
```

#### Request Body

```json
{
  "league": {
    "name": "New League",
    "competition_id": 1,
    "open": true,
    "starting_matchday": 1
  }
}
```

#### Authentication

Requires a valid access token.

#### Success Response (201 Created)

```json
{
  "data": {
    "id": 3,
    "name": "New League",
    "open": true,
    "starting_matchday": 1,
    "competition_id": 1,
    "competition_name": "Premier League",
    "competition_code": "PL",
    "owner_id": 1,
    "owner_username": "user123",
    "members_count": 1,
    "created_at": "2023-10-15T12:00:00.000Z",
    "updated_at": "2023-10-15T12:00:00.000Z",
    "user_is_member": true,
    "user_is_owner": true
  },
  "message": ["League created successfully"],
  "status": 201,
  "type": "success"
}
```

#### Error Responses

**Validation Errors (422 Unprocessable Entity)**

```json
{
  "errors": {
    "name": ["can't be blank"],
    "competition_id": ["can't be blank"],
    "starting_matchday": ["must be greater than or equal to 1"]
  },
  "status": 422
}
```

**Competition Not Found (404 Not Found)**

```json
{
  "errors": ["Competition not found"],
  "status": 404
}
```

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

### Join League Directly

Join an open league directly without requiring approval.

```
POST /api/v1/leagues/:id/memberships
```

#### Parameters

- `:id` - The ID of the league to join

#### Authentication

Requires a valid access token.

#### Success Response (201 Created)

```json
{
  "data": {
    "league_id": 1,
    "user_id": 1,
    "joined_at": "2023-10-15T12:00:00.000Z"
  },
  "message": ["Successfully joined the league"],
  "status": 201,
  "type": "success"
}
```

#### Error Responses

**League Not Found (404 Not Found)**

```json
{
  "errors": ["League not found"],
  "status": 404
}
```

**League Not Open (403 Forbidden)**

```json
{
  "errors": ["This league requires approval to join"],
  "status": 403
}
```

**Already a Member (422 Unprocessable Entity)**

```json
{
  "errors": ["You are already a member of this league"],
  "status": 422
}
```

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

### Leave League

Leave a league you are a member of.

```
DELETE /api/v1/leagues/:id/memberships/:user_id
```

#### Parameters

- `:id` - The ID of the league to leave
- `:user_id` - The ID of the user leaving the league (must be the current user's ID or the league owner removing a member)

#### Authentication

Requires a valid access token.

#### Success Response (200 OK)

```json
{
  "message": ["Successfully left the league"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

**League Not Found (404 Not Found)**

```json
{
  "errors": ["League not found"],
  "status": 404
}
```

**Not a Member (404 Not Found)**

```json
{
  "errors": ["Membership not found"],
  "status": 404
}
```

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

**Forbidden (403 Forbidden)**

```json
{
  "errors": ["You can only remove yourself or members of leagues you own"],
  "status": 403
}
```

**Owner Cannot Leave (422 Unprocessable Entity)**

```json
{
  "errors": ["League owner cannot leave the league"],
  "status": 422
}
```

## Implementation Notes

1. **League Ownership**: Each league has an owner who has special privileges, such as approving join requests and removing members.
2. **Open vs. Closed Leagues**: Open leagues can be joined directly, while closed leagues require approval from the owner.
3. **Starting Matchday**: The starting_matchday field determines from which matchday the league starts counting points.
4. **League Season**: Each league is associated with a specific season of a competition.
5. **Pagination**: The list leagues endpoint supports pagination to handle large numbers of leagues.
6. **User Context**: The API responses include user-specific fields like user_is_member and user_is_owner to help the frontend determine what actions are available.

## Frontend Integration

The frontend should:

1. Provide a form for creating new leagues
2. Display a list of available leagues, with filtering by competition
3. Show detailed information about each league, including members
4. Implement join/leave functionality with appropriate UI elements
5. Handle pagination for the leagues list
6. Show different UI elements based on the user's relationship to the league (owner, member, non-member)
