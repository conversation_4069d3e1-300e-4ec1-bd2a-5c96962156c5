# Seasons API

## Overview

The Seasons API provides endpoints for retrieving information about competition seasons, including matchday options and season details. These endpoints help users navigate through different seasons and matchdays of competitions.

## Endpoints

### Get Season Details

Retrieve detailed information about a specific season.

```
GET /api/v1/seasons/:id
```

#### Parameters

- `:id` - The ID of the season to retrieve

#### Authentication

No authentication required.

#### Success Response (200 OK)

```json
{
  "data": {
    "id": 1,
    "start_date": "2023-08-11",
    "end_date": "2024-05-19",
    "current_matchday": 10,
    "overall_matchday": 10,
    "match_days": 38,
    "stage": "REGULAR_SEASON",
    "teams_count": 20,
    "created_at": "2023-08-01T00:00:00.000Z",
    "updated_at": "2023-10-15T00:00:00.000Z",
    "competition_id": 1,
    "competition_name": "Premier League",
    "competition_code": "PL",
    "external_service_id": "2023",
    "source": "football_data"
  },
  "message": ["Season found"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

**Season Not Found (404 Not Found)**

```json
{
  "errors": ["Season not found"],
  "status": 404
}
```

### Get Matchday Options

Retrieve a list of available matchdays for a season.

```
GET /api/v1/seasons/:id/matchday_options
```

#### Parameters

- `:id` - The ID of the season

#### Authentication

No authentication required.

#### Success Response (200 OK)

```json
{
  "data": {
    "matchdays": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    "current_matchday": 10,
    "overall_matchday": 10,
    "total_matchdays": 38
  },
  "message": ["Matchday options found"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

**Season Not Found (404 Not Found)**

```json
{
  "errors": ["Season not found"],
  "status": 404
}
```

### List Seasons for Competition

Retrieve a list of all seasons for a specific competition.

```
GET /api/v1/competitions/:id/seasons
```

#### Parameters

- `:id` - The ID or code of the competition (e.g., `1` or `PL`)

#### Authentication

No authentication required.

#### Success Response (200 OK)

```json
{
  "data": [
    {
      "id": 1,
      "start_date": "2023-08-11",
      "end_date": "2024-05-19",
      "current_matchday": 10,
      "overall_matchday": 10,
      "match_days": 38,
      "stage": "REGULAR_SEASON",
      "teams_count": 20,
      "created_at": "2023-08-01T00:00:00.000Z",
      "updated_at": "2023-10-15T00:00:00.000Z",
      "external_service_id": "2023",
      "source": "football_data",
      "is_current": true
    },
    {
      "id": 2,
      "start_date": "2022-08-05",
      "end_date": "2023-05-28",
      "current_matchday": 38,
      "overall_matchday": 38,
      "match_days": 38,
      "stage": "REGULAR_SEASON",
      "teams_count": 20,
      "created_at": "2022-08-01T00:00:00.000Z",
      "updated_at": "2023-05-28T00:00:00.000Z",
      "external_service_id": "2022",
      "source": "football_data",
      "is_current": false
    }
  ],
  "message": ["Seasons found"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

**Competition Not Found (404 Not Found)**

```json
{
  "errors": ["Competition not found"],
  "status": 404
}
```

## Implementation Notes

1. **Current Matchday**: The current_matchday field represents the matchday that is currently being played or the next matchday to be played.
2. **Overall Matchday**: For cup competitions with multiple stages, the overall_matchday field provides a continuous numbering across all stages.
3. **Season Stages**: For cup competitions, the stage field indicates the current stage of the competition (e.g., "GROUP_STAGE", "ROUND_OF_16").
4. **Current Season**: The is_current field indicates whether the season is the current active season for the competition.
5. **External Service ID**: The external_service_id field contains the ID used by the external data provider for this season.
6. **Source**: The source field indicates which external service provides the data for this season (e.g., "football_data", "api_football").

## Frontend Integration

The frontend should:

1. Display a list of available seasons for each competition
2. Provide a way to switch between different seasons
3. Show the current matchday and total number of matchdays
4. Implement a matchday selector using the matchday options
5. Clearly indicate which season is the current active season
6. Handle different season stages appropriately (especially for cup competitions)
