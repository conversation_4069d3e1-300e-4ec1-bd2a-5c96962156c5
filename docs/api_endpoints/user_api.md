# User API

## Overview

The User API provides endpoints for retrieving and updating user information. These endpoints allow users to view their own profile, view other users' profiles, and update their personal information.

## Endpoints

### Get Current User

Retrieve the currently authenticated user's information.

```
GET /api/v1/users/me
```

#### Authentication

Requires a valid access token.

#### Success Response (200 OK)

```json
{
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "user123",
    "created_at": "2023-01-01T12:00:00.000Z",
    "updated_at": "2023-01-01T12:00:00.000Z",
    "highest_round_score": 27,
    "average_round_score": 14.5,
    "most_perfect_predictions_in_a_round": 3,
    "admin": false,
    "leagues": [
      {
        "id": 1,
        "name": "Friends League",
        "competition_id": 2,
        "competition_name": "Premier League"
      }
    ]
  },
  "message": ["User found"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

### Get Specific User

Retrieve information about a specific user by ID.

```
GET /api/v1/users/:id
```

#### Parameters

- `:id` - The ID of the user to retrieve

#### Authentication

Requires a valid access token.

#### Success Response (200 OK)

```json
{
  "data": {
    "id": 2,
    "username": "other_user",
    "created_at": "2023-01-02T12:00:00.000Z",
    "updated_at": "2023-01-02T12:00:00.000Z",
    "highest_round_score": 24,
    "average_round_score": 12.8,
    "most_perfect_predictions_in_a_round": 2,
    "leagues": [
      {
        "id": 1,
        "name": "Friends League",
        "competition_id": 2,
        "competition_name": "Premier League"
      }
    ]
  },
  "message": ["User found"],
  "status": 200,
  "type": "success"
}
```

Note: For privacy reasons, the email address is only included in the response when retrieving the current user's information.

#### Error Responses

**User Not Found (404 Not Found)**

```json
{
  "errors": ["User not found"],
  "status": 404
}
```

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

### Update User

Update the currently authenticated user's information.

```
PATCH /api/v1/users/:id
```

#### Parameters

- `:id` - The ID of the user to update (must be the current user's ID)

#### Request Body

```json
{
  "user": {
    "username": "new_username",
    "email": "<EMAIL>",
    "password": "new_password",
    "password_confirmation": "new_password",
    "current_password": "current_password"
  }
}
```

All fields are optional. If updating the password, both `password` and `password_confirmation` must be provided, along with the `current_password` for verification.

#### Authentication

Requires a valid access token.

#### Success Response (200 OK)

```json
{
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "new_username",
    "created_at": "2023-01-01T12:00:00.000Z",
    "updated_at": "2023-01-03T12:00:00.000Z",
    "highest_round_score": 27,
    "average_round_score": 14.5,
    "most_perfect_predictions_in_a_round": 3,
    "admin": false,
    "leagues": [
      {
        "id": 1,
        "name": "Friends League",
        "competition_id": 2,
        "competition_name": "Premier League"
      }
    ]
  },
  "message": ["User updated successfully"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

**Validation Errors (422 Unprocessable Entity)**

```json
{
  "errors": {
    "username": ["has already been taken"],
    "password_confirmation": ["doesn't match Password"],
    "current_password": ["is invalid"]
  },
  "status": 422
}
```

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

**Forbidden (403 Forbidden)**

```json
{
  "errors": ["You are not authorized to update this user"],
  "status": 403
}
```

## Implementation Notes

1. **Email Updates**: When a user updates their email address, they may need to verify the new email address before the change takes effect.
2. **Password Updates**: When updating a password, the current password must be provided for security verification.
3. **Username Uniqueness**: Usernames must be unique across the system.
4. **Privacy**: User email addresses are only visible to the user themselves.
5. **Basic Statistics**: Basic user statistics (highest_round_score, average_round_score, most_perfect_predictions_in_a_round) are included in the user response.
6. **Detailed Statistics**: For more comprehensive statistics, use the dedicated User Statistics API endpoint (`GET /api/v1/users/:id/statistics`). See [User Statistics API](./user_statistics_api.md) for details.

## Frontend Integration

The frontend should:

1. Provide a profile page for viewing and editing user information
2. Implement form validation for username, email, and password fields
3. Display appropriate error messages for validation failures
4. Show user statistics in a user-friendly format
5. Provide a way to view other users' profiles with limited information
