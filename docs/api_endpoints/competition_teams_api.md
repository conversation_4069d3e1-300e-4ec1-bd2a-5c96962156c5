# Competition Teams API Guide

## Overview

The Competition Teams API provides access to all teams participating in a competition's current season. This endpoint makes it simple to retrieve a complete list of teams for display in your frontend application.

## Endpoint

```
GET /api/v1/competitions/:id/teams
```

Where `:id` is the unique identifier of the competition.

## Authentication

This endpoint does not require authentication, allowing public access to team data.

## Response Format

The API returns a JSON object with the following structure:

```json
{
  "data": [
    {
      "id": 1,
      "name": "Team Name",
      "short_name": "TName",
      "tla": "TNM",
      "crest_public_id": "teams/logo123",
      "venue": "Team Stadium",
      "area": {
        "id": 5,
        "name": "Country Name"
      }
    }
    // Additional teams...
  ],
  "message": ["Teams found"],
  "status": 200,
  "type": "success"
}
```

## Error Responses

- **404 Not Found**: Returned when the competition with the specified ID doesn't exist.

## Frontend Implementation Example

Here's an example of how to use this endpoint in a React application with Axios:

```typescript
import axios from "axios";
import { useState, useEffect } from "react";

interface Team {
  id: number;
  name: string;
  short_name: string;
  tla: string;
  crest_public_id: string;
  venue: string;
  area: {
    id: number;
    name: string;
  };
}

interface TeamsResponse {
  data: Team[];
  message: string[];
  status: number;
  type: string;
}

const CompetitionTeams = ({ competitionId }: { competitionId: number }) => {
  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTeams = async () => {
      try {
        setLoading(true);
        const response = await axios.get<TeamsResponse>(
          `${API_BASE_URL}/api/v1/competitions/${competitionId}/teams`
        );
        setTeams(response.data.data);
        setError(null);
      } catch (err) {
        setError("Failed to fetch teams");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchTeams();
  }, [competitionId]);

  if (loading) return <div>Loading teams...</div>;
  if (error) return <div>{error}</div>;

  return (
    <div className="teams-container">
      <h2>Teams</h2>
      <div className="teams-grid">
        {teams.map((team) => (
          <div key={team.id} className="team-card">
            <img
              src={`https://res.cloudinary.com/yourcloudname/image/upload/${team.crest_public_id}`}
              alt={`${team.name} crest`}
            />
            <h3>{team.name}</h3>
            <p>{team.venue}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CompetitionTeams;
```

## Using with Competition Details

When displaying competition details, you can add a section for teams by fetching both the competition data and the teams:

```typescript
// In your competition details component
const CompetitionDetails = ({ id }: { id: number }) => {
  const [competition, setCompetition] = useState<Competition | null>(null);
  const [teams, setTeams] = useState<Team[]>([]);

  useEffect(() => {
    // Fetch competition details
    // ...

    // Fetch teams in the competition
    const fetchTeams = async () => {
      try {
        const response = await axios.get<TeamsResponse>(
          `${API_BASE_URL}/api/v1/competitions/${id}/teams`
        );
        setTeams(response.data.data);
      } catch (err) {
        console.error("Failed to fetch teams:", err);
      }
    };

    fetchTeams();
  }, [id]);

  // Render competition details and teams
  // ...
};
```

This endpoint allows you to easily display all teams in a competition's current season, which can be useful for team selection dropdowns, team listings, and other UI components that require team data.
