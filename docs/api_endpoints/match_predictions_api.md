# Match Predictions API

## Overview

The Match Predictions API provides endpoints for submitting and retrieving predictions for football matches. These endpoints allow users to predict match outcomes and view their previous predictions.

## Endpoints

### Submit Predictions

Submit predictions for multiple matches at once.

```
POST /api/v1/match_predictions/bulk_create_or_update
```

#### Request Body

```json
{
  "predictions": [
    {
      "match_id": 1,
      "home_score": 2,
      "away_score": 1
    },
    {
      "match_id": 2,
      "home_score": 1,
      "away_score": 3
    }
  ]
}
```

#### Authentication

Requires a valid access token.

#### Success Response (200 OK)

```json
{
  "data": [
    {
      "id": 1,
      "match_id": 1,
      "user_id": 1,
      "home_score": 2,
      "away_score": 1,
      "points": null,
      "created_at": "2023-10-14T12:00:00.000Z",
      "updated_at": "2023-10-15T12:00:00.000Z"
    },
    {
      "id": 2,
      "match_id": 2,
      "user_id": 1,
      "home_score": 1,
      "away_score": 3,
      "points": null,
      "created_at": "2023-10-14T12:00:00.000Z",
      "updated_at": "2023-10-15T12:00:00.000Z"
    }
  ],
  "message": ["Predictions saved successfully"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

**Validation Errors (422 Unprocessable Entity)**

```json
{
  "errors": {
    "predictions.0.home_score": ["must be greater than or equal to 0"],
    "predictions.1.away_score": ["must be greater than or equal to 0"]
  },
  "status": 422
}
```

**Match Not Found (404 Not Found)**

```json
{
  "errors": ["One or more matches not found"],
  "status": 404
}
```

**Match Not Predictable (422 Unprocessable Entity)**

```json
{
  "errors": ["One or more matches are not available for prediction"],
  "status": 422
}
```

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

### Get User Predictions for Match

Retrieve a user's predictions for a specific match.

```
GET /api/v1/matches/:id/predictions
```

#### Parameters

- `:id` - The ID of the match

#### Authentication

Requires a valid access token.

#### Success Response (200 OK)

```json
{
  "data": {
    "id": 1,
    "match_id": 1,
    "user_id": 1,
    "home_score": 2,
    "away_score": 1,
    "points": 3,
    "created_at": "2023-10-14T12:00:00.000Z",
    "updated_at": "2023-10-15T16:00:00.000Z"
  },
  "message": ["Prediction found"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

**Match Not Found (404 Not Found)**

```json
{
  "errors": ["Match not found"],
  "status": 404
}
```

**Prediction Not Found (404 Not Found)**

```json
{
  "errors": ["Prediction not found"],
  "status": 404
}
```

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

### Get User Predictions for Multiple Matches

Retrieve a user's predictions for multiple matches.

```
GET /api/v1/match_predictions
```

#### Query Parameters

- `match_ids` - Comma-separated list of match IDs (e.g., `1,2,3`)

#### Authentication

Requires a valid access token.

#### Success Response (200 OK)

```json
{
  "data": [
    {
      "id": 1,
      "match_id": 1,
      "user_id": 1,
      "home_score": 2,
      "away_score": 1,
      "points": 3,
      "created_at": "2023-10-14T12:00:00.000Z",
      "updated_at": "2023-10-15T16:00:00.000Z"
    },
    {
      "id": 2,
      "match_id": 2,
      "user_id": 1,
      "home_score": 1,
      "away_score": 3,
      "points": 1,
      "created_at": "2023-10-14T12:00:00.000Z",
      "updated_at": "2023-10-15T18:30:00.000Z"
    }
  ],
  "message": ["Predictions found"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

**Invalid Parameters (400 Bad Request)**

```json
{
  "errors": ["match_ids parameter is required"],
  "status": 400
}
```

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

## Implementation Notes

1. **Points System**: Predictions are scored as follows:
   - 3 points for correctly predicting the exact score
   - 1 point for correctly predicting the outcome (win, loss, draw) but not the exact score
   - 0 points for incorrectly predicting the outcome
2. **Prediction Timing**: Predictions can only be submitted or updated before the match starts.
3. **Bulk Submission**: The API supports submitting multiple predictions at once to improve efficiency.
4. **Points Calculation**: The points field is null until the match is finished and the result is known.
5. **Prediction Updates**: Users can update their predictions as many times as they want before the match starts.

## Frontend Integration

The frontend should:

1. Provide a form for submitting match predictions
2. Display the user's current predictions for upcoming matches
3. Show the points earned for past predictions
4. Implement validation to ensure scores are non-negative integers
5. Disable prediction submission for matches that have already started
6. Provide visual feedback when predictions are saved successfully
7. Display appropriate error messages when prediction submission fails
