# Documentation Organization

This folder contains documentation for the Brag Rights backend application. The documentation is organized into the following directories:

## Directory Structure

- **frontend/** - Documentation related to the frontend application, including implementation details
- **backend/** - Documentation related to the backend application, including service requirements and implementation guides
- **api_endpoints/** - Documentation for API endpoints and how the frontend interacts with them
- **youtube/** - Documentation related to YouTube integration, including future ideas and implementation details
- **obsolete/** - Older documentation that may no longer be relevant but is kept for historical reference

## Frontend Documentation

The `frontend/` directory contains guides specifically for frontend developers, including:

- Authentication implementation
- New season creation
- Frontend application overview

## Backend Documentation

The `backend/` directory contains guides specifically for backend developers, including:

- Service requirements
- Implementation guides
- Testing instructions

## API Endpoints Documentation

The `api_endpoints/` directory contains documentation for API endpoints, including:

- Endpoint specifications
- Request and response formats
- Authentication requirements
- Error handling

These guides describe how the frontend interacts with the backend API without including UI/UX implementation details.

## Obsolete Documentation

The `obsolete/` directory contains documentation that may no longer be relevant but is kept for historical reference.

## Contributing

When adding new documentation:

1. Place frontend-specific guides in the `frontend/` directory
2. Place backend-specific guides in the `backend/` directory
3. Place API endpoint documentation in the `api_endpoints/` directory
4. Use clear, descriptive filenames
5. Include a title and overview at the beginning of each document
6. Use markdown formatting for consistency
