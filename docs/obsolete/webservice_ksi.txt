i skjali pessu er lyst peim follum i opinni vefpj6nustu KSi fyrir felog og aora. Vefpj6nustann er staosett a http://www2.ksi.is/vefthjonustur/mot.asmx

Breytingar:
12.11.2019 Viob6t 3 rutinur, LeikirBreyting, LeikirBreytingUrslit, Leikir 15.11.2019 VollurNumer brett vio MotLeikur


Leikir hja felagi (Felogleikir)
Skilar til baka leikjum felags eftir leitarskilyroum Verour ao ao leita eftir felagi og hamarkstimabil er ar. Leitarskilyroi
FelagNumer
String
Numer felags. Verour ao vera utfyllt
VollurNumer
String
Numer vallar
FlokkurNumer
String
Flokkur numer
Kyn
String
I = Karla, 0 =Kvenna
DagsFra
Datetime
Dagur fra
DagsTil
Datetime
Dagur til

Skilar til baka
FelogLeikirSvar
Nafn
Tegund
Skyring
ArrayFelogLeikir
Fylki
Inniheldur fylki meo leikjum. Lysing
her ao neoa a stokum i pvi.
VillaNumer
Int
0 ef pao er enginn villa.
Villa
String
Lysing a villu/athugasemd

FelogLeikir
LeikurNumer
Tegund

FelagHeimaNafn
String

FelagUtiNafn
String

FelagHeimaNumer
int

FelagUtiNumer
Int

Leikdagur
Datetime

UrslitHeima
Int

UrslitUti
Int

Skyrslustada
Int

VollurNafn
String

VollurNumer
Int

MotNafn
String

MotNumer
Int

Flokkur
String

MotKyn
Int
Nytt svreoi 24.2.2011






Stoautafla i m6ti (MotStada) Skilar til baka stooutoflu i akveonu m6ti. Leitarskilyroi
MotNumer
String
Numer m6ts

Skilar til baka
MotStadaSvar
Nafn
Tegund
Skyring
ArrayMotStada
Fylki
Inniheldur fylki meo lioum. Lysing
her ao neoan a stokum i pvi.
VillaNumer
Int
0 ef pao er enginn villa.
Villa
String
Lysing a villu/athugasemd

MotStada
Nafn
Tegund
Skyring
FelagNumer
int
Numer felags
FelagNafn
string
Nafn Felags
LeikirAlls
int

LeikirUnnir
Int

LeikirJafnt
Int

LeikirTap
Int

MorkSkorud
Int

MorkFenginASig
Int

MorkMisMunur
Int

Stig
Int




Leikir i m6ti (Motleikir)
Skilar til baka stooutoflu i akveonu m6ti.
Leitarskilyroi
MotNumer
String
Numer m6ts

Skilar til baka
MotLeikirS var
Nafn
Tegund
Skyring
ArrayMotLeikir
Fylki
Inniheldur fylki meo leikjum. Lysing
her ao neoan a stokum i pvi.
VillaNumer
Int
0 ef pao er enginn villa.
Villa
String
Lysing a villu/athugasemd

MotLeikur

Nafn
Tegund
Skyring
LeikurNumer
int

UmferdNumer
String

Leikdagur
Datetime

FelagHeimaNafn
String

FelagHeimaNumer
Int

FelagUtiNafn
String

FelagUtiNumer
Int

UrslitHeima
String

UrslitUti
String

StadaFyrriHalfLeikHeima
String

StadaFyrriHalfLeikUti
String

StadaSeinniHalfLeikHeima
String

StadaSeinniHalfLeikUti
String

SkyrslaStada
String
u	Leik lokio
H		Leikur i gangi Ekki hafin
L	Leikskyrsla skrao
S	Staofest Leikskyrsla
VollurNafn
String

Ahorfendur
String

VollurNumer
Int




Markhrestu menn i m6ti (MotMarkhaestu)
Skilar til baka stooutoflu i akveonu m6ti.
Leitarskilyroi
MotNumer
String
Numer m6ts

Skilar til baka
MotMarkaHaestuSvar
Nafn	Tegund
Skyring
ArrayMotMarkahaestu	Fylki
Inniheldur fylki meo leikmonnum.
Lysing her ao neoan a stokum i pvi.



VillaNumer
Int
0 ef pao er enginn villa.
Villa
String
Lysing a villu/athugasemd

MotMarkahaestu
Nafn
Tegund
Skyring
LeikmadurNumer
int

LeikmadurN afn
String

FelagNafn
String

Mork
Int

Viti
Int

Leikir
Int


Flokkur (Flokkur)
Skilar til baka flokki

Skilar til baka
FlokkurSvar
Nafn
Tegund
Skyring
ArrayFlokkur
Fylki
Inniheldur fylki meo flokki. Lysing her ao neoan a stokum i ]:,vi.
VillaNumer
Int
0 ef ]:,ao er enginn villa.
Villa
String
Lysing a villu/athugasemd

Flokkur
Nafn
Tegund
Skyring
FlokkurHeiti
String

FlokkurNumer
int



Gui spjold i m6ti (MotSpjoldGul)
Skilar til baka lista meo talningu a gulum spjoldum a leikmenn
Leitarskilyroi
MotNumer
String
Numer m6ts

Skilar til baka
MotSpjoldSvar
Nafn
Tegund
Skyring
ArrayMotSpjold
Fylki
Inniheldur fylki meo leikmonnum.
Lysing her ao neoan a stokum i ]:,vi.
VillaNumer
Int
0 ef ]:,ao er enginn villa.
Villa
String
Lysing a villu/athugasemd

MotSpjold
Nafn
Tegund
Skyring
LeikmadurNumer
int

LeikmadurN afn
String

FelagNafn
String

Fjoldi
Int
Fjoldi spjalda


Rauo spjold i m6ti (MotSpjoldRaud)
Skilar til baka lista meo talningu a rauoum spjoldum a leikmenn
Leitarskilyroi
MotNumer
String
Numer m6ts

Skilar til baka
MotSpjoldSvar
Nafn
Tegund
Skyring

ArrayMotSpjold
Fylki
Inniheldur fylki meo leikmonnum.
Lysing her ao neoan a stokum i pvi.
VillaNumer
Int
0 ef pao er enginn villa.
Villa
String
Lysing a villu/athugasemd

MotSpjold
Nafn
Tegund
Skyring
LeikmadurNumer
Int

LeikmadurNafn
String

FelagNafn
String

Fjoldi
Int
Fjoldi spjalda


Atburoir i leik (LeikurAtburdir)
Skilar til baka lista atburoum i akveonum leik

Leitarskilyroi
LeikurNumer
String
Numer leiks

Skilar til baka
LeikurAtburdirSvar
Nafn	Tegund
Skyring
ArrayLeikurAtburdir	Fylki
Inniheldur fylki meo atburoum
Lysing her ao neoan a stokum i pvi.



VillaNumer
Int
0 ef pao er enginn villa.
Villa
String
Lysing a villu/athugasemd

LeikurAtburdir
Nafn
Tegund
Skyring
LeikmadurNumer
Int

LeikmadurN afn
String

FelagNumer
Int

FelagNafn
String

TreyjuNumer
Int

AtburdurMinuta
Int

AtburdurNumer
Int

AtburdurNafn
String



Leikmenn i leik (Leikurleikmenn)
Skilar til baka lista meo leikmonnum leik

Leitarskilyroi
LeikurNumer
String
Numer leiks

Skilar til baka
LeikurAtburdirSvar
Nafn
Tegund
Skyring

ArrayLeikurLeikmenn
Fylki
Inniheldur fylki meo leikmonnum Lysing her ao neoan a stokum i pvi.
VillaNumer
Int
0 ef pao er enginn villa.
Villa
String
Lysing a villu/athugasemd

LeikurAtburdir
Nafn
Tegund
Skyring
LeikmadurNumer
Int

LeikmadurNafn
String

FelagNumer
Int

FelagNafn
String

TreyjuNumer
Int

StadaNumer
Int

StadaNafn
String

Leikmadur
Int
1 = leikmaour, O=Liostj6m

D6marar i leik (Domararleikmenn)
Skilar til baka lista meo d6murum i leik

Leitarskilyroi
LeikurNumer
String
Numer leiks

Skilar til baka
LeikurDomararSvar
Nafn	Tegund
Skyring
ArrayLeikurDomarar	Fylki
Inniheldur fylki meo d6murum
Lysing her ao neoan a stokum i pvi.



VillaNumer
Int
0 ef pao er enginn villa.
Villa
String
Lysing a villu/athugasemd

LeikurDomarar
Nafn
Tegund
Skyring
DomariNumer
Int

DomariNafn
String

HluverkNumer
Int

HlutverkNafn
String



Breytingar a leik (LeikirBreyting)
Listi yfir leiki par sem buio er ao breyta voll/leiktima og breytingadags peirra innan timaramma sem er sendur inn


Leitarskilyroi I DagurTimiFra I DagurTimiTra


I DateTime
I DateTime


I Dagur timi fra
I Dagur timi til



Skilar til baka

Nafn
Tegund
Skyring
LeikirNumer
String
Leikjum sem hefur verio breytt,
LeikurNumer aoskilio meo ,
VillaNumer
Int
0 ef pao er enginn villa.
Villa
String
Lysing a villu/athugasemd


Breytingar a leik (LeikirBreytingUrslit)
Listi yfir leiki meo urslit og breytingardags peirra innan timaramma sem er sendur inn

Leitarskilyroi

I DagurTimiFra
I DateTime
I Dagur timi fra
I DagurTimiTra
I DateTime
I Dagur timi til

Skilar til baka



Nafn
Tegund
Skyring
LeikirNumer
String
Leikjum sem hefur verio breytt,
LeikurNumer aoskilio meo ,
VillaNumer
Int
0 ef pao er enginn villa.
Villa
String
Lysing a villu/athugasemd



Leikir (Leikir)
Skilar til baka stooutoflu i akveonu m6ti.
Leitarskilyroi
LeikirNumer
String
LeikirNumer aoskilio meo ,

Skilar til baka
MotLeikirSvar (sja ao ofar i skjali lysingu a }:,vi.)
Nafn	Tegund	Skyring
ArrayMotLeikir	Fylki	Inniheldur fylki meo leikjum. Lysing


her ao neoan a stokum i }:,vi.
VillaNumer
Int
0 ef pao er enginn villa.
Villa
String
Lysing a villu/athugasemd



# Python example
import datetime
import typing
from collections import defaultdict, namedtuple

from suds.client import Client

from .models import ErrorDict, Player

WSDL_URL = "http://www2.ksi.is/vefthjonustur/mot.asmx?WSDL"

STARTER_ROLES = [
    "Markmaður",
    "Leikmaður",
    "Fyrirliði",
    "Fyrirliði/markmaður",
]

sex_map = {
    0: "kvk",
    1: "kk",
}
Match = namedtuple(
    "Match",
    (
        "match_id",
        "group",
        "sex",
    ),
)


def player_sort_key(p):
    n = int(p.TreyjuNumer) if p.TreyjuNumer else 999
    if p.StadaNafn in STARTER_ROLES:
        if "markmaður" in p.StadaNafn.lower():
            return n / 100000
        return n / 100
    return n


class KsiClient:
    def __init__(self):
        self.client = Client(WSDL_URL, timeout=10)

    def get_player(self, player) -> Player:
        if player.TreyjuNumer:
            number = int(player.TreyjuNumer)
        else:
            number = 0
        return {
            "id": player.LeikmadurNumer,
            "number": number,
            "name": player.LeikmadurNafn.strip(),
            "role": player.StadaNafn,
            "show": player.StadaNafn in STARTER_ROLES,
        }

    def get_matches(self, home_team, away_team, date, back_days=15):
        result = self.client.service.FelogLeikir(
            FelagNumer=home_team,
            DagsFra=date - datetime.timedelta(back_days),
            DagsTil=date + datetime.timedelta(10),
            Kyn="",
            FlokkurNumer="",
            VollurNumer="",
        )

        if hasattr(result, "Villa"):
            return {
                "error": {
                    "key": "UPSTREAM_ERROR",
                    "text": result.Villa,
                }
            }
        gameArray = result.ArrayFelogLeikir
        if not gameArray:
            return
        games = gameArray.FelogLeikir
        matches: typing.List[Match] = []
        for game in games:
            print(game.MotKyn, ":", game.FelagHeimaNafn, "-", game.FelagUtiNafn)
            if game.FelagHeimaNumer == home_team and game.FelagUtiNumer == away_team:
                print("Found match: %s" % (game.LeikurNumer,))
                matches.append(
                    Match(
                        game.LeikurNumer,
                        game.Flokkur,
                        sex_map.get(game.MotKyn, "kk"),
                    )
                )
        return matches

    def get_players(self, match_id) -> dict[str, list[Player]] | ErrorDict:
        game = self.client.service.LeikurLeikmenn(LeikurNumer=match_id)
        result = defaultdict(list)
        captains = {}
        if not game.ArrayLeikurLeikmenn:
            return {
                "error": {
                    "key": "NO_PLAYERS",
                    "text": "No players found for game %s" % (match_id,),
                }
            }
        for player in sorted(
            game.ArrayLeikurLeikmenn.LeikurLeikmenn,
            key=player_sort_key,
        ):
            club_id = str(player.FelagNumer)
            player_dict = self.get_player(player)
            if (
                club_id in captains
                and captains[club_id]["number"] < player_dict["number"]
            ):
                result[club_id].append(captains[club_id])
                del captains[club_id]
            if player_dict["role"] == "Fyrirliði":
                captains[club_id] = player_dict
            else:
                result[club_id].append(player_dict)
        return result


ksi_client = KsiClient()