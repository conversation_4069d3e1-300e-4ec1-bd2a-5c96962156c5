# BragRights Backend Low-Cost Deployment Guide

BragRights is a sports prediction platform backend API built with Ruby on Rails, optimized for low traffic and minimal hosting costs.

## Cost-Optimized Architecture

This guide focuses on deploying BragRights on a single small DigitalOcean droplet to minimize costs while maintaining reliability:

- **Total Monthly Cost**: ~$6 (1GB RAM/1 CPU Basic Droplet)
- **Expected Capacity**: Suitable for low-traffic APIs with hundreds of daily requests
- **Static IP**: Included free (Floating IP) for external service whitelisting

## System Requirements (All running on a single droplet)

- Ubuntu 22.04 LTS
- Ruby 3.3.0
- PostgreSQL (on-droplet instance)
- Redis (on-droplet instance)
- Nginx
- Puma (configured for low memory usage)
- Sidekiq (configured for low memory usage)

## Initial Droplet Setup

1. Create a DigitalOcean droplet:

   - Select Ubuntu 22.04 LTS
   - Choose Basic Plan ($6/mo - 1GB RAM/1 CPU)
   - Select a datacenter region close to your users
   - Add your SSH key
   - Choose a hostname (e.g., bragrights-production)

2. Assign a Floating IP to your droplet:

   - Go to Networking → Floating IPs
   - Click "Assign Floating IP" and select your droplet
   - This IP will remain static (use it for external service whitelisting)

3. Set up a non-root user with sudo privileges:

```bash
adduser deploy
usermod -aG sudo deploy
mkdir -p /home/<USER>/.ssh
cp ~/.ssh/authorized_keys /home/<USER>/.ssh/
chown -R deploy:deploy /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh
chmod 600 /home/<USER>/.ssh/authorized_keys
```

4. Log in as the new user:

```bash
su - deploy
```

## Memory Optimization Setup

1. Add a swap file to help with occasional memory spikes:

```bash
sudo fallocate -l 1G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
```

2. Set memory optimization parameters:

```bash
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
echo 'vm.vfs_cache_pressure=50' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

## Install Required Software

```bash
sudo apt-get update
sudo apt-get install -y curl git build-essential libpq-dev nodejs npm redis-server postgresql postgresql-contrib nginx certbot python3-certbot-nginx
```

## Configure Firewall

```bash
sudo ufw allow OpenSSH
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

## Install Ruby using rbenv

```bash
git clone https://github.com/rbenv/rbenv.git ~/.rbenv
echo 'export PATH="$HOME/.rbenv/bin:$PATH"' >> ~/.bashrc
echo 'eval "$(rbenv init -)"' >> ~/.bashrc
source ~/.bashrc
git clone https://github.com/rbenv/ruby-build.git ~/.rbenv/plugins/ruby-build
rbenv install 3.3.0
rbenv global 3.3.0
gem install bundler --no-document
```

## Modified Setup Script

Create a modified setup script that's optimized for low resource usage:

```bash
mkdir -p ~/scripts
nano ~/scripts/low_cost_setup.sh
```

Add the following content:

```bash
#!/bin/bash

set -e

echo "Setting up BragRights on Digital Ocean (Low-Cost Configuration)..."

# Configuration
APP_PATH="/var/www/bragrights-be"
USER=$(whoami)
RAILS_ENV="production"
DB_PASSWORD="NewSimplePassword123" # Changed to a password without special characters

# Functions
log() {
  echo "$(date +'%Y-%m-%d %H:%M:%S') - $1"
}

# Check if running as normal user
if [ "$(id -u)" -eq 0 ]; then
  log "Please run this script as a non-root user with sudo privileges"
  exit 1
fi

# Setup application directory
log "Setting up application directory..."
sudo mkdir -p $APP_PATH
sudo chown $USER:$USER $APP_PATH

# Clone repository if needed
if [ ! -d "$APP_PATH/.git" ]; then
  log "Cloning repository..."
  <NAME_EMAIL>:magnus-thor/brag-rights-be.git $APP_PATH
  cd $APP_PATH
else
  log "Repository already exists, updating..."
  cd $APP_PATH
  git pull origin main
fi

# Generate a secure random password
log "Setting up database credentials..."

# Setup PostgreSQL database (optimized for low memory)
log "Setting up PostgreSQL database with memory optimizations..."
sudo -u postgres psql -c "CREATE USER bragrights WITH PASSWORD '$DB_PASSWORD';" || true
sudo -u postgres psql -c "CREATE DATABASE bragrights_production OWNER bragrights;" || true
sudo -u postgres psql -c "ALTER USER bragrights WITH SUPERUSER;" || true

# Configure PostgreSQL for low memory usage
sudo tee -a /etc/postgresql/14/main/conf.d/memory.conf > /dev/null <<EOF
# Memory Configuration - Low Memory Settings
shared_buffers = 128MB
work_mem = 4MB
maintenance_work_mem = 32MB
effective_cache_size = 256MB
EOF

sudo systemctl restart postgresql

# Setup environment variables
log "Setting up environment variables..."
if [ ! -f "$APP_PATH/.env" ]; then
  touch $APP_PATH/.env
  echo "DB_PASSWORD=$DB_PASSWORD" >> $APP_PATH/.env
  echo "DATABASE_URL=postgresql://bragrights:$DB_PASSWORD@localhost:5432/bragrights_production" >> $APP_PATH/.env
  echo "REDIS_URL=redis://localhost:6379/0" >> $APP_PATH/.env
  echo "RAILS_ENV=production" >> $APP_PATH/.env
  echo "SECRET_KEY_BASE=$(openssl rand -hex 64)" >> $APP_PATH/.env
fi

# Configure Redis for low memory usage
log "Configuring Redis for low memory..."
sudo tee /etc/redis/redis.conf > /dev/null <<EOL
maxmemory 128mb
maxmemory-policy allkeys-lru
EOL

sudo systemctl restart redis-server

# Install dependencies
log "Installing dependencies..."
cd $APP_PATH
bundle config set --local deployment 'true'
bundle config set --local without 'development test'
bundle install

# Setup the database
log "Setting up the database..."
cd $APP_PATH
RAILS_ENV=$RAILS_ENV bundle exec rails db:create || true
RAILS_ENV=$RAILS_ENV bundle exec rails db:migrate
RAILS_ENV=$RAILS_ENV bundle exec rails db:seed || true

# Create optimized Puma configuration
log "Creating optimized Puma configuration..."
tee $APP_PATH/config/puma_production.rb > /dev/null <<EOL
#!/usr/bin/env puma

directory '/var/www/bragrights-be'
environment 'production'
daemonize false
pidfile '/var/www/bragrights-be/tmp/pids/puma.pid'
state_path '/var/www/bragrights-be/tmp/pids/puma.state'
stdout_redirect '/var/www/bragrights-be/log/puma_access.log', '/var/www/bragrights-be/log/puma_error.log', true

# Threads configuration - Low Memory Setup
threads 1, 3

# Workers configuration - Only 1 worker for low memory usage
workers 1
preload_app!

# Unix socket to balance multiple processes
bind 'unix:///var/www/bragrights-be/tmp/sockets/puma.sock'

on_worker_boot do
  ActiveRecord::Base.establish_connection if defined?(ActiveRecord)
end

before_fork do
  ActiveRecord::Base.connection_pool.disconnect! if defined?(ActiveRecord)
end

plugin :tmp_restart
EOL

# Setup Nginx with minimal config
log "Setting up Nginx..."
sudo tee /etc/nginx/sites-available/bragrights > /dev/null <<EOL
# Rate limiting configuration needs to be in http context
# Adding an include file for our rate limiting config
EOL

# Create a separate file for rate limiting config that will be included in http context
log "Creating rate limiting configuration..."
sudo tee /etc/nginx/conf.d/rate_limiting.conf > /dev/null <<EOL
# Rate limiting to protect against abuse
limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
EOL

# Now create the main site config without the misplaced directive
sudo tee /etc/nginx/sites-available/bragrights > /dev/null <<EOL
upstream puma {
  server unix:///var/www/bragrights-be/tmp/sockets/puma.sock;
}

server {
  listen 80;
  server_name your-domain.com;

  root /var/www/bragrights-be/public;
  access_log /var/log/nginx/bragrights-be-access.log;
  error_log /var/log/nginx/bragrights-be-error.log;

  # Add gzip to reduce bandwidth usage
  gzip on;
  gzip_vary on;
  gzip_proxied any;
  gzip_comp_level 6;
  gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

  location / {
    try_files \$uri @puma;
  }

  location @puma {
    proxy_pass http://puma;
    proxy_set_header Host \$host;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto \$scheme;
  }

  # Apply rate limiting to API endpoints
  location /api {
    limit_req zone=api burst=20 nodelay;
    try_files \$uri @puma;
  }
}
EOL

sudo ln -sf /etc/nginx/sites-available/bragrights /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl restart nginx

# Setup Puma service
log "Setting up Puma service..."
sudo tee /etc/systemd/system/puma.service > /dev/null <<EOL
[Unit]
Description=Puma HTTP Server
After=network.target postgresql.service redis-server.service

[Service]
Type=simple
User=$USER
WorkingDirectory=$APP_PATH
Environment="RAILS_ENV=$RAILS_ENV"
EnvironmentFile=$APP_PATH/.env
ExecStart=/home/<USER>/.rbenv/shims/bundle exec puma -C $APP_PATH/config/puma_production.rb
ExecReload=/bin/kill -s USR1 \$MAINPID
TimeoutSec=300
Restart=always
RestartSec=10

# Memory optimization
MemoryAccounting=true
MemoryLimit=512M

[Install]
WantedBy=multi-user.target
EOL

# Setup Sidekiq service with limited concurrency
log "Setting up Sidekiq service with memory limits..."
sudo tee /etc/systemd/system/sidekiq.service > /dev/null <<EOL
[Unit]
Description=Sidekiq job processor
After=network.target postgresql.service redis-server.service

[Service]
Type=simple
User=$USER
WorkingDirectory=$APP_PATH
Environment="RAILS_ENV=$RAILS_ENV"
Environment="SIDEKIQ_CONCURRENCY=2"
EnvironmentFile=$APP_PATH/.env
ExecStart=/home/<USER>/.rbenv/shims/bundle exec sidekiq -e \$RAILS_ENV -c 2
ExecReload=/bin/kill -s USR1 \$MAINPID
TimeoutSec=300
Restart=always
RestartSec=10

# Memory optimization
MemoryAccounting=true
MemoryLimit=256M

[Install]
WantedBy=multi-user.target
EOL

# Create directory for sockets and pids
log "Creating directories for sockets and pids..."
mkdir -p $APP_PATH/tmp/sockets
mkdir -p $APP_PATH/tmp/pids

# Enable services
log "Enabling services..."
sudo systemctl enable puma
sudo systemctl enable sidekiq
sudo systemctl daemon-reload

# Start services
log "Starting services..."
sudo systemctl start puma
sudo systemctl start sidekiq

log "Setup completed successfully!"
log "Make sure to:"
log "1. Update the database password in .env file"
log "2. Set your domain name in the Nginx configuration"
log "3. Setup SSL with Certbot"
log "4. Configure GitHub repository secrets for CI/CD"

exit 0
```

Make the script executable:

```bash
chmod +x ~/scripts/low_cost_setup.sh
```

## Clone Your Repository and Run the Setup Script

```bash
sudo mkdir -p /var/www
sudo chown deploy:deploy /var/www
cd ~
./scripts/low_cost_setup.sh
```

## Domain and SSL Options for API-Only Backend

For an API-only backend, you have two options:

### Option 1: Using a Domain Name with SSL (Recommended)

Having a domain name with SSL provides:

- Better security for API communications
- Easier integration with third-party services that require HTTPS
- More professional appearance for API documentation or status pages
- Future-proofing as many services increasingly require HTTPS

If you choose this route:

```bash
# Set your domain in Nginx config first
sudo nano /etc/nginx/sites-available/bragrights
# Change "server_name your-domain.com;" to your actual domain

# Then obtain and install SSL certificate
sudo certbot --nginx -d your-domain.com
```

### Option 2: Using Direct IP Address (Simpler but Limited)

If your API will only be consumed by your own applications and you want to keep it simpler:

1. Use the Droplet's IP address directly in your API requests
2. Update the Nginx configuration to listen on the IP:

```bash
sudo nano /etc/nginx/sites-available/bragrights
# Change "server_name your-domain.com;" to "server_name _;"
```

3. Configure your client applications to use the direct IP:
   - Example: `https://YOUR_FLOATING_IP/api/v1/endpoint`

Note that without SSL, your API communications won't be encrypted. Some services and modern browsers may also reject non-HTTPS connections, especially for authentication endpoints.

### Minimum Security for IP-Only Setup

If you choose not to use a domain name, at least add basic authentication to your API:

```bash
# Install apache utils for htpasswd command
sudo apt install apache2-utils

# Create password file
sudo htpasswd -c /etc/nginx/.htpasswd api_user

# Update Nginx config
sudo nano /etc/nginx/sites-available/bragrights
```

Add basic auth to the API location:

```
location /api {
  auth_basic "API Access";
  auth_basic_user_file /etc/nginx/.htpasswd;
  limit_req zone=api burst=20 nodelay;
  try_files $uri @puma;
}
```

Then restart Nginx:

```bash
sudo systemctl restart nginx
```

## Configure SSL with Let's Encrypt

SSL is still important even for low-cost deployments:

```bash
sudo certbot --nginx -d your-domain.com
```

## Minimal Backup Strategy

Create a lightweight backup script that runs less frequently:

```bash
mkdir -p ~/scripts
nano ~/scripts/weekly_backup.sh
```

Add the following content:

```bash
#!/bin/bash
TIMESTAMP=$(date +"%Y%m%d")
BACKUP_DIR="/home/<USER>/backups"
BACKUP_FILE="$BACKUP_DIR/bragrights_production_$TIMESTAMP.sql"

mkdir -p $BACKUP_DIR
pg_dump -U bragrights bragrights_production > $BACKUP_FILE
gzip $BACKUP_FILE

# Keep only the 3 most recent backups to save space
ls -t $BACKUP_DIR/*.gz | tail -n +4 | xargs -r rm
```

Make it executable:

```bash
chmod +x ~/scripts/weekly_backup.sh
```

Set up a weekly cron job:

```bash
crontab -e
```

Add the following line to run backups once a week (Sunday at 2 AM):

```
0 2 * * 0 /home/<USER>/scripts/weekly_backup.sh
```

## Lightweight Monitoring

Create a simple status check script that doesn't consume many resources:

```bash
nano ~/scripts/status_check.sh
```

Add the following content:

```bash
#!/bin/bash
LOG_FILE="/home/<USER>/status_check.log"
echo "Status check at $(date)" > $LOG_FILE

# Check and restart services only if they're down
systemctl is-active --quiet puma || {
  echo "Puma: DOWN - restarting" >> $LOG_FILE
  systemctl restart puma
}

systemctl is-active --quiet sidekiq || {
  echo "Sidekiq: DOWN - restarting" >> $LOG_FILE
  systemctl restart sidekiq
}

systemctl is-active --quiet nginx || {
  echo "Nginx: DOWN - restarting" >> $LOG_FILE
  systemctl restart nginx
}

# Clean old logs (keep only last 10 lines) to save disk space
tail -10 $LOG_FILE > /tmp/status_check.log
mv /tmp/status_check.log $LOG_FILE

# Check disk space and send alert if over 80%
DISK_USAGE=$(df / | grep / | awk '{ print $5}' | sed 's/%//g')
if [ "$DISK_USAGE" -gt 80 ]; then
  echo "WARNING: Disk usage is at $DISK_USAGE%" >> $LOG_FILE
fi

# Check memory usage
free -m | awk '/Mem:/ {printf("Memory Usage: %d%%\n", $3*100/$2)}' >> $LOG_FILE
```

Make it executable:

```bash
chmod +x ~/scripts/status_check.sh
```

Set up a cron job to run every 4 hours (not too frequent to save resources):

```bash
crontab -e
```

Add the following line:

```
0 */4 * * * /home/<USER>/scripts/status_check.sh
```

## Optimized Log Rotation

Configure aggressive log rotation to save disk space:

```bash
sudo nano /etc/logrotate.d/bragrights
```

Add the following configuration:

```
/var/www/bragrights-be/log/*.log {
  daily
  missingok
  rotate 3
  compress
  delaycompress
  notifempty
  copytruncate
  size 10M
}
```

## Simplified CI/CD Pipeline

Create a minimal deployment script for GitHub Actions:

```bash
mkdir -p .github/workflows
nano .github/workflows/deploy.yml
```

Add the following content:

```yml
name: Deploy to Digital Ocean

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SERVER_IP }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd /var/www/bragrights-be
            git pull origin main
            bundle install --deployment --without development test
            RAILS_ENV=production bundle exec rails db:migrate
            sudo systemctl restart puma
            sudo systemctl restart sidekiq
```

## Regular Maintenance Tasks

To keep your low-cost deployment running smoothly:

1. Clean up old logs weekly:

```bash
find /var/www/bragrights-be/log -name "*.gz" -mtime +7 -delete
```

2. Check and clean up temporary files:

```bash
find /var/www/bragrights-be/tmp/cache -type f -mtime +7 -delete
```

3. Monitor disk space regularly:

```bash
df -h
```

4. Update your SSL certificates automatically:

```bash
sudo certbot renew
```

## Cost-Saving Recap

This deployment approach provides:

- Single $6/month droplet with all services
- Free static IP via Floating IP
- Memory optimization for Ruby, PostgreSQL and Redis
- Reduced backup frequency and retention
- Minimized logging and monitoring overhead
- Properly configured services to prevent memory issues

The total cost remains at approximately $6/month (plus domain registration if needed), while still providing a reliable production environment suitable for low-traffic applications.

## Scaling Up When Needed

When your traffic grows:

1. Upgrade your droplet to the next tier (2GB RAM - $12/month)
2. Adjust the configurations in `/etc/postgresql/14/main/conf.d/memory.conf`
3. Modify the Puma and Sidekiq service files to increase concurrency
4. You can keep the same Floating IP during the upgrade

This provides a smooth growth path without any migration complexity.
