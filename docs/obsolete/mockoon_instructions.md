# Mockoon Setup

1. Install Mockoon:
   - Download from https://mockoon.com
   - Or install via npm: `npm install -g @mockoon/cli`

2. Create a new environment:
   - Define a route such as `/v3/fixtures` returning your desired JSON.

3. Run the environment:
   - Mockoon CLI: `mockoon start --data /path/to/exported/environment.json --port 3000`
   - Or open the Mockoon app and click "Start".

4. Update your application:
   - In development/testing, set `BASE_URL=http://localhost:3000` if you want to mock `https://v3.football.api-sports.io`.

5. Test your integration:
   - Confirm your requests hit the mocked endpoint and return fixture data from Mockoon.
