# Test Coverage Gaps in BragRights Backend

This document outlines features and components in the BragRights backend that currently lack adequate test coverage. Addressing these gaps will improve the reliability and maintainability of the codebase.

## Table of Contents

1. [User Interaction Models](#user-interaction-models)
2. [YouTube Integration](#youtube-integration)
3. [Match Management](#match-management)
4. [Background Jobs](#background-jobs)
5. [Services](#services)
6. [UI Components and Styling](#ui-components-and-styling)
7. [Prioritization Strategy](#prioritization-strategy)
8. [Implementation Plan](#implementation-plan)

## User Interaction Models

### LeagueJoinRequest Model and Controller

The `LeagueJoinRequest` model handles user requests to join private leagues, but lacks test coverage:

- **Missing model tests** for:

  - Validation of uniqueness constraints
  - User league limit validation
  - Status transitions (pending → accepted/rejected)

- **Missing controller tests** for:

  - Creating join requests
  - Listing join requests for a league
  - Accepting/rejecting join requests
  - Authorization checks for league owners

- **Missing request specs** for:
  - `/api/v1/leagues/:id/join_requests` endpoints
  - `/api/v1/join_requests/user_requests` endpoint

### Membership Model

The `Membership` model represents users' membership in leagues:

- **Missing model tests** for:

  - User league limit validation
  - Association validations
  - Subscription status handling for YouTube leagues

- **Incomplete request specs** for:
  - League membership creation
  - League membership deletion
  - Handling of subscriber-only leagues

## YouTube Integration

The YouTube integration features have limited test coverage:

- **Missing tests for `YoutubeTokenService`**:

  - Token storage
  - Token refresh
  - Token revocation
  - Error handling

- **Limited tests for YouTube-related user methods**:

  - `youtube_connected?`
  - `verified_youtube_creator?`
  - `connect_youtube_account`
  - `disconnect_youtube_account`
  - `youtube_access_token`

- **Missing tests for YouTube subscription verification**:

  - Verification of user subscriptions
  - Caching of subscription status
  - Handling of API quota limits

- **Missing tests for subscriber-only league functionality**:
  - League creation with subscriber-only settings
  - Verification of subscription requirements
  - Grace period handling for unsubscribed users

## Match Management

## Background Jobs

~~Several background jobs lack proper test coverage:~~ (Tests have been added for all background jobs)

- ~~**`BatchRoundPredictionsJob`**:~~

  - ~~Batch processing of predictions~~
  - ~~Error handling~~
  - ~~Result storage in cache~~

- ~~**`DynamicMatchUpdaterJob`**:~~

  - ~~Match updates for specific competitions~~
  - ~~Error handling and retries~~
  - ~~Timeout handling~~

- ~~**`CheckPostponedMatchesJob`**:~~

  - ~~Checking status of postponed matches~~
  - ~~Scheduling next checks~~
  - ~~Updating match data~~

- ~~**`TodaysMatchesUpdateJob`**:~~

  - ~~Updating matches for the current day~~
  - ~~Handling different data sources~~

- ~~**`SubscriptionVerificationJob`**:~~
  - ~~Verification of user subscriptions~~
  - ~~Handling of different user tiers (active, semi-active, inactive)~~
  - ~~Grace period management~~

## Services

### Match Updater Service

The `MatchUpdaterService` updates match data from external sources:

- **Missing tests for different data sources**:

  - FootballDataService integration
  - ApiFootballService integration
  - KsiSoapService integration

- **Missing tests for error handling and retry logic**:
  - API failures
  - Rate limiting
  - Timeout handling

### League Season Transition Service

The `LeagueSeasonTransitionService` handles season transitions:

- **Missing tests for edge cases**:

  - Handling leagues with no members
  - Handling leagues with incomplete data
  - Handling multiple competitions transitioning simultaneously

- **Missing tests for league archiving**:
  - Archiving process
  - Historical data preservation
  - User standings calculation

## UI Components and Styling

### Team and Competition Colors

Based on project requirements, there are features related to team and competition colors:

- **Missing tests for team colors object structure**:

  - Home and away color objects
  - Primary, secondary, and tertiary color handling
  - Color name to hex mapping

- **Missing tests for competition colors**:

  - Color object structure
  - Frontend updates
  - Serialization

- **Missing tests for color display logic**:
  - Handling similar colors between home and away teams
  - Fallback color selection

## Prioritization Strategy

When addressing these test coverage gaps, consider the following prioritization:

1. **Critical user-facing features**:

   - LeagueJoinRequest and Membership models
   - YouTube integration for subscriber-only leagues

2. **Data integrity features**:

   - Match updating services
   - Season transition functionality

3. **Background processes**:

   - Jobs that handle critical data updates
   - Subscription verification

4. **UI and presentation logic**:
   - Team and competition colors
   - Display preferences

## Implementation Plan

### Phase 1: Core Models and Controllers

1. Add tests for `LeagueJoinRequest` model
2. Add tests for `LeagueJoinRequestsController`
3. Add tests for `Membership` model
4. Complete tests for league memberships endpoints

### Phase 2: YouTube Integration

1. Add tests for `YoutubeTokenService`
2. Add tests for YouTube-related user methods
3. Add tests for subscription verification
4. Add tests for subscriber-only league functionality

### Phase 3: Match Management

1. Add tests for `MatchUpdaterService`

### Phase 4: Background Jobs

~~1. Add tests for `BatchRoundPredictionsJob`~~ ✅
~~2. Add tests for `DynamicMatchUpdaterJob`~~ ✅
~~3. Add tests for `CheckPostponedMatchesJob`~~ ✅
~~4. Add tests for `TodaysMatchesUpdateJob`~~ ✅
~~5. Add tests for `SubscriptionVerificationJob`~~ ✅

### Phase 5: UI Components

1. Add tests for team colors functionality
2. Add tests for competition colors
3. Add tests for color display logic

## Conclusion

Addressing these test coverage gaps will significantly improve the reliability and maintainability of the BragRights backend. By following the prioritization strategy and implementation plan outlined above, the team can systematically enhance test coverage for critical features while ensuring the application remains stable during development.
