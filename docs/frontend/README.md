# Frontend Documentation

This directory contains documentation specifically for frontend developers working with the Brag Rights API.

## Contents

- **frontend_authentication_guide.md** - Guide for implementing user authentication in the frontend
- **frontend_new_season_api_guide.md** - API specifications for creating new seasons
- **frontend_new_season_implementation.md** - Implementation guide for the new season creation feature
- **score-rivalry-fe-description.md** - Overview of the Score Rivalry frontend application

## Using These Guides

These guides are intended to help frontend developers understand how to integrate with the Brag Rights backend API. They include:

- API endpoint specifications
- Request and response formats
- Implementation examples
- Error handling guidelines

When implementing new features, please refer to these guides to ensure consistency with the existing application architecture.
