# Team Standings Frontend Implementation Guide

This guide explains how to implement the team standings feature in the frontend application.

## Overview

The team standings feature allows users to view standings for fans of a specific team. This provides a way to see how users who have selected a particular team as their favorite are performing in predictions.

## API Endpoint

```
GET /api/v1/teams/:id/standings
```

### Parameters

- `:id` - The ID of the team (required)
- `competition_id` - Optional query parameter for filtering by competition
- `season_id` - Optional query parameter for filtering by season

## Implementation Steps

### 1. Add API Service Function

Add the following function to your API service:

```typescript
export const getTeamStandings = async (
  teamId: number,
  options?: { competitionId?: number; seasonId?: number }
) => {
  const params: Record<string, string> = {};
  
  if (options?.competitionId) {
    params.competition_id = options.competitionId.toString();
  }
  
  if (options?.seasonId) {
    params.season_id = options.seasonId.toString();
  }
  
  const response = await api.get<TeamStandingsResponse>(
    `/api/v1/teams/${teamId}/standings`,
    { params }
  );
  return response.data;
};
```

### 2. Create a Team Standings Component

Create a component to display the team standings:

```tsx
import React from 'react';
import { Standing } from '../types';

interface TeamStandingsProps {
  standings: Standing[];
  isLoading: boolean;
  teamName: string;
}

const TeamStandings: React.FC<TeamStandingsProps> = ({ 
  standings, 
  isLoading, 
  teamName 
}) => {
  if (isLoading) {
    return <div>Loading standings...</div>;
  }

  if (!standings || standings.length === 0) {
    return <div>No fans found for {teamName}.</div>;
  }

  return (
    <div className="team-standings">
      <h2>{teamName} Fan Standings</h2>
      <table>
        <thead>
          <tr>
            <th>Position</th>
            <th>User</th>
            <th>Points</th>
            <th>Perfect</th>
            <th>Correct</th>
            <th>Incorrect</th>
          </tr>
        </thead>
        <tbody>
          {standings.map((standing) => (
            <tr key={standing.username} className={standing.position <= 3 ? `top-${standing.position}` : ''}>
              <td>{standing.position}</td>
              <td>
                {standing.username}
                {standing.top_message && (
                  <div className="top-message">{standing.top_message}</div>
                )}
              </td>
              <td>{standing.points}</td>
              <td>{standing.perfect}</td>
              <td>{standing.correct}</td>
              <td>{standing.incorrect}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default TeamStandings;
```

### 3. Create a Team Standings Page

Create a page component that fetches and displays the team standings:

```tsx
import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { getTeamStandings } from '../services/api';
import TeamStandings from '../components/TeamStandings';
import { TeamStandingsResponse } from '../types';

const TeamStandingsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [searchParams, setSearchParams] = useSearchParams();
  const competitionId = searchParams.get('competition_id');
  const seasonId = searchParams.get('season_id');
  
  const [data, setData] = useState<TeamStandingsResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchStandings = async () => {
      try {
        setIsLoading(true);
        const response = await getTeamStandings(
          parseInt(id as string, 10),
          {
            competitionId: competitionId ? parseInt(competitionId, 10) : undefined,
            seasonId: seasonId ? parseInt(seasonId, 10) : undefined
          }
        );
        setData(response);
      } catch (err) {
        setError('Failed to load team standings');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchStandings();
    }
  }, [id, competitionId, seasonId]);

  const handleCompetitionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    if (value === 'all') {
      searchParams.delete('competition_id');
    } else {
      searchParams.set('competition_id', value);
    }
    setSearchParams(searchParams);
  };

  const handleSeasonChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    if (value === 'current') {
      searchParams.delete('season_id');
    } else {
      searchParams.set('season_id', value);
    }
    setSearchParams(searchParams);
  };

  if (error) {
    return <div className="error-message">{error}</div>;
  }

  return (
    <div className="team-standings-page">
      {data?.team && (
        <>
          <div className="team-header">
            <img 
              src={`/api/images/${data.team.crest_public_id}`} 
              alt={data.team.name} 
              className="team-crest" 
            />
            <h1>{data.team.name} Fan Standings</h1>
          </div>
          
          <div className="filters">
            {data.competition && (
              <div className="competition-info">
                Competition: {data.competition.name}
              </div>
            )}
            
            {data.season && (
              <div className="season-info">
                Season: {new Date(data.season.start_date).toLocaleDateString()} - 
                {new Date(data.season.end_date).toLocaleDateString()}
              </div>
            )}
          </div>
          
          <TeamStandings 
            standings={data.standings} 
            isLoading={isLoading}
            teamName={data.team.name}
          />
        </>
      )}
    </div>
  );
};

export default TeamStandingsPage;
```

### 4. Add Styling for Team Standings

Add CSS to style the team standings page:

```css
/* team-standings.css */
.team-standings-page {
  padding: 20px;
}

.team-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.team-crest {
  width: 60px;
  height: 60px;
  margin-right: 15px;
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.team-standings table {
  width: 100%;
  border-collapse: collapse;
}

.team-standings th,
.team-standings td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.team-standings th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.team-standings .top-1 {
  background-color: rgba(255, 215, 0, 0.1); /* Gold */
  font-weight: bold;
}

.team-standings .top-2 {
  background-color: rgba(192, 192, 192, 0.1); /* Silver */
}

.team-standings .top-3 {
  background-color: rgba(205, 127, 50, 0.1); /* Bronze */
}

.top-message {
  font-size: 0.8rem;
  color: #666;
  margin-top: 4px;
  font-style: italic;
}
```

### 5. Add Route to Your Application

Add the team standings route to your router configuration:

```tsx
// In your router configuration
<Route path="/teams/:id/standings" element={<TeamStandingsPage />} />
```

### 6. Add Link to Team Details Page

Add a link to the standings from your team details page:

```tsx
<Link to={`/teams/${team.id}/standings`} className="btn btn-primary">
  View Fan Standings
</Link>
```

### 7. Add Link to User Profile

If you display a user's favorite teams on their profile, add a link to view the standings for each team:

```tsx
{user.favorite_teams.map(favorite => (
  <div key={favorite.id} className="favorite-team">
    <img 
      src={`/api/images/${favorite.team.crest_public_id}`} 
      alt={favorite.team.name} 
    />
    <span>{favorite.team.name}</span>
    <Link to={`/teams/${favorite.team.id}/standings`}>
      View Fan Standings
    </Link>
  </div>
))}
```

## Testing

Make sure to test the following scenarios:

1. Viewing standings for a team with fans
2. Viewing standings for a team with no fans
3. Filtering by competition
4. Filtering by season
5. Proper display of top 3 user messages
6. Error handling when the API request fails

## Conclusion

This implementation provides a complete solution for displaying team fan standings in your frontend application. The feature supports filtering by both competition and season, with special highlighting for the top 3 users.
