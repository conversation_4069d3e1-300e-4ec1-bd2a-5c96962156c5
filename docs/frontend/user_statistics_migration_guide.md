# User Statistics Migration Guide

## Overview

We've improved the way user statistics are handled in the BragRights application by moving them from the user show endpoint to a dedicated statistics endpoint. This change provides better performance, more detailed statistics, and greater flexibility.

## Changes to User Show Endpoint

### Statistics Removed from User Show Response

The following statistics have been removed from the `/api/v1/users/:id` and `/api/v1/users/me` responses:

```json
{
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "user123",
    // ... other user data ...
    "stats": {
      "highest_round_score": 27,
      "average_round_score": 14.5,
      "most_perfect_predictions_in_a_round": 3
    }
    // ... other user data ...
  }
}
```

## New User Statistics Endpoint

These statistics (and many more) are now available through a dedicated endpoint:

```
GET /api/v1/users/:user_id/statistics
```

### Basic Usage

To get the same basic statistics that were previously included in the user show response:

```javascript
// Example using axios
const fetchUserStatistics = async (userId) => {
  try {
    const response = await axios.get(`/api/v1/users/${userId}/statistics`, {
      params: {
        type: 'basic'
      }
    });
    return response.data.data;
  } catch (error) {
    console.error('Error fetching user statistics:', error);
    throw error;
  }
};
```

### Response Format

The basic statistics response includes the original statistics plus additional metrics:

```json
{
  "data": {
    "total_points": 450,
    "total_predictions": 296,
    "prediction_accuracy": 68.5,
    "perfect_predictions": {
      "count": 45,
      "percentage": 15.2
    },
    "correct_predictions": {
      "count": 158,
      "percentage": 53.3
    },
    "incorrect_predictions": {
      "count": 93,
      "percentage": 31.5
    },
    "highest_round_score": 27,
    "average_round_score": 14.5,
    "most_perfect_predictions_in_a_round": 3
  },
  "message": ["Statistics retrieved successfully"],
  "status": 200,
  "type": "success"
}
```

## Additional Features

### Statistics Types

The new endpoint supports different types of statistics:

```javascript
// Basic statistics (default)
const basicStats = await fetchUserStatistics(userId, 'basic');

// Advanced statistics (includes consistency rating and streaks)
const advancedStats = await fetchUserStatistics(userId, 'advanced');

// Competition-specific statistics
const competitionStats = await fetchUserStatistics(userId, 'competition');

// Comparative statistics (global rank, percentile)
const comparativeStats = await fetchUserStatistics(userId, 'comparative');

// All statistics combined
const allStats = await fetchUserStatistics(userId, 'all');
```

### Filtering Options

You can filter statistics by competition, season, or time period:

```javascript
// Filter by competition
const premierLeagueStats = await fetchUserStatistics(userId, 'basic', {
  competition_id: 1
});

// Filter by season
const season2023Stats = await fetchUserStatistics(userId, 'basic', {
  season_id: 5
});

// Filter by time period
const currentSeasonStats = await fetchUserStatistics(userId, 'basic', {
  time_period: 'current_season'
});

// Combined filters
const premierLeague2023Stats = await fetchUserStatistics(userId, 'basic', {
  competition_id: 1,
  season_id: 5
});
```

### Helper Function

Here's a complete helper function for fetching user statistics with all options:

```javascript
const fetchUserStatistics = async (userId, type = 'basic', filters = {}) => {
  try {
    const params = {
      type,
      ...filters
    };
    
    const response = await axios.get(`/api/v1/users/${userId}/statistics`, { params });
    return response.data.data;
  } catch (error) {
    console.error('Error fetching user statistics:', error);
    throw error;
  }
};
```

## Performance Benefits

The new endpoint includes several performance optimizations:

1. **Caching**: Statistics are cached for 1 hour, significantly reducing database load
2. **Selective Loading**: Only request the statistics you need
3. **Progressive Loading**: Load basic statistics first, then request more detailed statistics as needed

## Migration Steps

1. Update your API calls to use the new endpoint instead of extracting statistics from the user show response
2. Take advantage of the additional statistics types and filtering options
3. Consider implementing progressive loading for a better user experience

## Example: Updating a User Profile Component

Before:
```javascript
const UserProfile = ({ userId }) => {
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    const fetchUser = async () => {
      const response = await axios.get(`/api/v1/users/${userId}`);
      setUser(response.data.data);
    };
    fetchUser();
  }, [userId]);
  
  return (
    <div>
      {user && (
        <>
          <h1>{user.username}</h1>
          <div className="stats">
            <p>Highest Round Score: {user.stats.highest_round_score}</p>
            <p>Average Round Score: {user.stats.average_round_score}</p>
            <p>Most Perfect Predictions: {user.stats.most_perfect_predictions_in_a_round}</p>
          </div>
        </>
      )}
    </div>
  );
};
```

After:
```javascript
const UserProfile = ({ userId }) => {
  const [user, setUser] = useState(null);
  const [stats, setStats] = useState(null);
  
  useEffect(() => {
    const fetchData = async () => {
      // Fetch user data and basic statistics in parallel
      const [userResponse, statsResponse] = await Promise.all([
        axios.get(`/api/v1/users/${userId}`),
        axios.get(`/api/v1/users/${userId}/statistics`, { params: { type: 'basic' } })
      ]);
      
      setUser(userResponse.data.data);
      setStats(statsResponse.data.data);
    };
    fetchData();
  }, [userId]);
  
  return (
    <div>
      {user && (
        <>
          <h1>{user.username}</h1>
          {stats && (
            <div className="stats">
              <p>Highest Round Score: {stats.highest_round_score}</p>
              <p>Average Round Score: {stats.average_round_score}</p>
              <p>Most Perfect Predictions: {stats.most_perfect_predictions_in_a_round}</p>
              <p>Total Points: {stats.total_points}</p>
              <p>Prediction Accuracy: {stats.prediction_accuracy}%</p>
              <p>Perfect Predictions: {stats.perfect_predictions.count} ({stats.perfect_predictions.percentage}%)</p>
            </div>
          )}
        </>
      )}
    </div>
  );
};
```

## Need More Information?

For complete details on the new user statistics API, refer to the [User Statistics API documentation](../api_endpoints/user_statistics_api.md).
