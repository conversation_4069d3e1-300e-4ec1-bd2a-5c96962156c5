# Score Rivalry Frontend Application Description

## Overview

Score Rivalry (Bragging Rights) is a football/soccer prediction platform that allows users to predict match outcomes, join leagues with friends, and compete based on prediction accuracy. The application focuses on various football competitions including Premier League, Champions League, Allsvenskan, and more.

## Core Functionality

### User Authentication & Management

- **Authentication Flow**: JWT-based authentication with access and refresh tokens
- **User Registration**: Email, username, and password-based signup with email verification
- **Session Management**: HTTP-only cookies for secure token storage with 1-hour frontend session timeout
- **User Profiles**: Display and management of user information, statistics, and league memberships

### Competitions & Leagues

- **Competitions**: Display of available football competitions (leagues and cups)
- **League Creation**: Users can create private or public leagues within competitions
- **League Joining**: Users can join existing leagues (directly or via application process)
- **League Management**: League owners can approve/reject join requests
- **League Tables**: Display of user rankings based on prediction points

### Match Predictions

- **Match Cards**: Interactive cards showing upcoming matches with team information
- **Prediction Interface**: Score input fields for predicting match outcomes
- **Prediction Submission**: Form-based submission of multiple match predictions
- **Prediction Status**: Visual indicators for saved/unsaved predictions
- **Results Viewing**: Display of match results and earned prediction points

### Results & Statistics

- **User Statistics**: Display of highest scores, average scores, and perfect predictions
- **League Tables**: Ranking of users within leagues based on prediction points
- **Match Results**: Display of completed match results with user predictions

## Backend API Integration

### Authentication Endpoints

- `POST /users/tokens/sign_up`: User registration
- `POST /users/tokens/sign_in`: User login
- `POST /users/tokens/refresh`: Token refresh
- `POST /users/tokens/revoke`: User logout
- `GET /confirmation`: Email verification
- `POST /resend_confirmation`: Resend verification email

### User Endpoints

- `GET /api/v1/users/me`: Get current user data
- `GET /api/v1/users/:id`: Get specific user data
- `PATCH /api/v1/users/:id`: Update user data
- `GET /api/v1/favorite_teams`: Get user's favorite teams
- `POST /api/v1/favorite_teams`: Add favorite team

### Competition Endpoints

- `GET /api/v1/competitions`: Get all competitions
- `GET /api/v1/competitions/:id`: Get specific competition data
- `GET /api/v1/competitions/:id/teams`: Get teams for a competition

### League Endpoints

- `GET /api/v1/leagues`: Get leagues (filterable by competition)
- `GET /api/v1/leagues/:id`: Get specific league data
- `POST /api/v1/leagues`: Create a new league
- `GET /api/v1/leagues/:id/join_requests`: Get league join requests
- `POST /api/v1/leagues/:id/join_requests`: Apply to join a league
- `PUT /api/v1/leagues/:id/join_requests/:request_id`: Update join request status
- `GET /api/v1/join_requests/user_requests`: Get user's league join requests
- `POST /api/v1/leagues/:id/memberships`: Join a league directly
- `DELETE /api/v1/leagues/:id/memberships/:user_id`: Leave a league

### Match & Prediction Endpoints

- `GET /api/v1/matches`: Get matches (filterable by matchday, competition, season)
- `GET /api/v1/predictable_matches`: Get matches available for prediction
- `POST /api/v1/match_predictions/bulk_create_or_update`: Submit multiple match predictions
- `GET /api/v1/round_predictions`: Get predictions for a round

### Season & Matchday Endpoints

- `GET /api/v1/seasons/:id/matchday_options`: Get matchday options for a season

### Admin Endpoints

- Various `/api/v1/admin/*` endpoints for managing competitions, teams, matches, etc.

## Data Models

### User

- Basic information: id, email, username
- Statistics: highest_round_score, average_round_score, most_perfect_predictions_in_a_round
- Relationships: leagues, favorite teams

### Competition

- Basic information: id, code, name, emblem_public_id, type, gender
- Relationships: area, current_season, current_winners
- Visual elements: team colors, emblems

### League

- Basic information: id, name, open status, starting_matchday
- Relationships: competition, owner, users/members
- Management: join requests, standings

### Match

- Basic information: id, utc_date, matchday, status, referee
- Relationships: home_team, away_team
- Score information: duration, fullTimeHome, fullTimeAway, winner

### Team

- Basic information: id, name, shortName, tla, venue
- Visual elements: crest_public_id, club_colors (primary, secondary, tertiary)
- Performance: form (recent match results)

### Prediction

- Match prediction: match_id, home_score, away_score
- Results: points (0, 1, or 3 based on accuracy)

## Frontend Implementation Details

- **Framework**: Next.js 15 with App Router
- **Data Fetching**: TanStack Query (React Query) for server state management
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: Zustand for client-side state
- **Form Handling**: Tanstack React Form
- **Image Handling**: Cloudinary for image storage and optimization
- **Internationalization**: next-intl for multi-language support (English, Icelandic, Swedish)

## Key Frontend Features

1. **Responsive Design**: Adapts to different screen sizes
2. **Real-time Updates**: Automatic refreshing of match data
3. **Form Validation**: Ensures valid prediction submissions
4. **Visual Feedback**: Status indicators for predictions and form submissions
5. **Team Color Integration**: Dynamic UI elements based on team colors
6. **Match Grouping**: Organized display of matches by date and time
7. **Accessibility**: Contrast-aware text and interactive elements

## Backend Requirements

The backend should provide:

1. **Consistent API Responses**: Follow established data structures
2. **Proper Error Handling**: Clear error messages and appropriate status codes
3. **Authentication Support**: JWT token generation and validation
4. **Data Validation**: Ensure data integrity before processing
5. **Background Processing**: Regular updates of match data and results
6. **Performance Optimization**: Efficient database queries and caching
7. **Security Measures**: Protection against common vulnerabilities

## Integration Considerations

1. **API Versioning**: Frontend expects `/api/v1/` prefix for endpoints
2. **Token Management**: Support for token refresh and revocation
3. **Data Formatting**: Consistent date formats (ISO 8601) and object structures
4. **Error Handling**: Structured error responses for frontend display
5. **Pagination**: Support for paginated responses on large data sets
6. **Filtering**: Support for query parameters to filter data
7. **Real-time Updates**: Consider WebSocket integration for live match updates
