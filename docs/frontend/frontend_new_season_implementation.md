# Frontend Implementation Guide for Creating a New Season

This guide outlines how to implement the frontend functionality for creating a new season in the Brag Rights application. The backend already has the necessary endpoints and services in place.

## Overview

The feature allows admin users to create a new season for a competition. This involves:

1. Adding a new button/action in the competition management UI
2. Creating a modal or form for confirming the action
3. Making API calls to the backend
4. Handling responses and updating the UI accordingly

## Implementation Steps

### 1. Add a New Button in the Competition Management UI

First, add a "Create New Season" button in the competition details page:

```jsx
// src/pages/Admin/CompetitionDetails.jsx

import { Button, Modal, message } from "antd";
import { useState } from "react";
import { useParams } from "react-router-dom";
import { createNewSeason } from "../../services/competitionService";

function CompetitionDetails() {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { id } = useParams();

  // Add this function to handle the button click
  const showCreateNewSeasonModal = () => {
    setIsModalVisible(true);
  };

  return (
    <div className="competition-details">
      {/* Existing competition details */}

      {/* Add the new button */}
      <Button
        type="primary"
        onClick={showCreateNewSeasonModal}
        className="create-season-button"
      >
        Create New Season
      </Button>

      {/* Modal implementation will be added in the next step */}
    </div>
  );
}
```

### 2. Create a Confirmation Modal

Add a modal to confirm the action and handle special cases for KSI competitions:

```jsx
// Continuing in CompetitionDetails.jsx

function CompetitionDetails() {
  // ... existing code

  const [seasonExternalServiceId, setSeasonExternalServiceId] = useState("");
  const [competition, setCompetition] = useState(null);

  // Add these functions to handle the modal
  const handleCancel = () => {
    setIsModalVisible(false);
    setSeasonExternalServiceId("");
  };

  const handleCreateNewSeason = async () => {
    setIsLoading(true);

    try {
      // For KSI and ApiFootball competitions, we need to provide the season_external_service_id
      const params = {};

      if (
        competition?.source === "ksi_soap" ||
        competition?.source === "api_football"
      ) {
        if (!seasonExternalServiceId) {
          throw new Error(
            `Season external service ID is required for ${competition.source} competitions`
          );
        }
        params.season_external_service_id = seasonExternalServiceId;
      }

      const response = await createNewSeason(id, params);

      message.success("New season created successfully");
      // Refresh the competition data
      // fetchCompetitionDetails(); // Implement this function to refresh data

      setIsModalVisible(false);
      setSeasonExternalServiceId("");
    } catch (error) {
      message.error(
        error.response?.data?.errors?.[0] || "Failed to create new season"
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Add the modal component
  const renderModal = () => {
    // Check if the competition requires a season external service ID
    const requiresSeasonId =
      competition?.source === "ksi_soap" ||
      competition?.source === "api_football";

    return (
      <Modal
        title="Create New Season"
        visible={isModalVisible}
        onOk={handleCreateNewSeason}
        onCancel={handleCancel}
        confirmLoading={isLoading}
      >
        <p>
          Are you sure you want to create a new season for this competition?
        </p>
        <p>This will:</p>
        <ul>
          <li>
            Create a new season based on the latest data from the external
            service
          </li>
          <li>Set it as the current season for the competition</li>
          <li>Archive leagues from the old season</li>
          <li>Transition leagues to use the new season</li>
        </ul>

        {(competition?.source === "ksi_soap" ||
          competition?.source === "api_football") && (
          <div className="season-input">
            <p>
              For {competition.source === "ksi_soap" ? "KSI" : "ApiFootball"}{" "}
              competitions, please provide the season external service ID:
            </p>
            <Input
              placeholder="Season External Service ID"
              value={seasonExternalServiceId}
              onChange={(e) => setSeasonExternalServiceId(e.target.value)}
              required
            />
            {competition.source === "api_football" && (
              <p className="help-text">
                For ApiFootball, this should be the year of the season (e.g.,
                2026).
              </p>
            )}
          </div>
        )}
      </Modal>
    );
  };

  return (
    <div className="competition-details">
      {/* Existing competition details */}

      <Button
        type="primary"
        onClick={showCreateNewSeasonModal}
        className="create-season-button"
      >
        Create New Season
      </Button>

      {renderModal()}
    </div>
  );
}
```

### 3. Add the API Service Function

Create a function in your API service to call the backend endpoint:

```jsx
// src/services/competitionService.js

import api from "./api";

// Add this function to your existing service
export const createNewSeason = (competitionId, params = {}) => {
  return api.post(
    `/api/v1/admin/competitions/${competitionId}/create_new_season`,
    params
  );
};
```

### 4. Update the Competition Details Page

Make sure your component fetches and displays the competition details:

```jsx
// Continuing in CompetitionDetails.jsx

import { useEffect } from "react";
import { getCompetitionDetails } from "../../services/competitionService";

function CompetitionDetails() {
  // ... existing code

  const fetchCompetitionDetails = async () => {
    try {
      const response = await getCompetitionDetails(id);
      setCompetition(response.data.data);
    } catch (error) {
      message.error("Failed to fetch competition details");
    }
  };

  useEffect(() => {
    fetchCompetitionDetails();
  }, [id]);

  // ... rest of the component
}
```

### 5. Add Styling

Add some CSS to style the new elements:

```css
/* src/styles/admin/CompetitionDetails.css */

.create-season-button {
  margin-top: 20px;
}

.season-input {
  margin-top: 20px;
}

.help-text {
  font-size: 12px;
  color: #888;
  margin-top: 5px;
}
```

### 6. Add Loading and Error States

Enhance the UI with loading and error states:

```jsx
// Continuing in CompetitionDetails.jsx

function CompetitionDetails() {
  // ... existing code
  const [pageLoading, setPageLoading] = useState(true);

  const fetchCompetitionDetails = async () => {
    setPageLoading(true);
    try {
      const response = await getCompetitionDetails(id);
      setCompetition(response.data.data);
    } catch (error) {
      message.error("Failed to fetch competition details");
    } finally {
      setPageLoading(false);
    }
  };

  // ... rest of the component

  if (pageLoading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
      </div>
    );
  }

  if (!competition) {
    return <div className="error-container">Competition not found</div>;
  }

  // ... render the UI
}
```

### 7. Add Access Control

Ensure only admin users can access this functionality:

```jsx
// src/components/ProtectedAdminRoute.jsx

import { Navigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";

function ProtectedAdminRoute({ children }) {
  const { user, isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  if (!user.admin) {
    return <Navigate to="/unauthorized" />;
  }

  return children;
}

// In your routes file
<Route
  path="/admin/competitions/:id"
  element={
    <ProtectedAdminRoute>
      <CompetitionDetails />
    </ProtectedAdminRoute>
  }
/>;
```

### 8. Add Refresh Functionality

After creating a new season, refresh the competition data:

```jsx
// Continuing in CompetitionDetails.jsx

const handleCreateNewSeason = async () => {
  setIsLoading(true);

  try {
    // For KSI and ApiFootball competitions, we need to provide the season_external_service_id
    const params = {};

    if (
      competition?.source === "ksi_soap" ||
      competition?.source === "api_football"
    ) {
      if (!seasonExternalServiceId) {
        throw new Error(
          `Season external service ID is required for ${competition.source} competitions`
        );
      }
      params.season_external_service_id = seasonExternalServiceId;
    }

    await createNewSeason(id, params);

    message.success("New season created successfully");
    // Refresh the competition data
    fetchCompetitionDetails();

    setIsModalVisible(false);
    setSeasonExternalServiceId("");
  } catch (error) {
    message.error(
      error.response?.data?.errors?.[0] ||
        error.message ||
        "Failed to create new season"
    );
  } finally {
    setIsLoading(false);
  }
};
```

## Testing

1. Log in as an admin user
2. Navigate to the competition details page
3. Click the "Create New Season" button
4. Confirm the action in the modal
5. For KSI and ApiFootball competitions, enter a valid season external service ID
6. Verify that the new season is created and set as the current season
7. Check that leagues are transitioned to the new season

## Error Handling

Make sure to handle these potential errors:

1. User is not an admin
2. Missing season external service ID for KSI and ApiFootball competitions
3. Missing competition external_service_id for ApiFootball competitions
4. API errors (network issues, server errors)
5. Season already exists

## Additional Considerations

1. **Confirmation Warning**: Make it clear to the user that this action will archive leagues from the old season.

2. **Progress Indicator**: For large competitions with many leagues, consider adding a progress indicator or notification.

3. **Season Selection**: For ApiFootball competitions, you need to provide the year of the season (e.g., 2026). For KSI competitions, you need to provide the external service ID for the season. Consider adding a dropdown or autocomplete to help users select the correct season ID.

4. **Audit Logging**: Consider adding audit logging to track who created new seasons and when.

5. **Undo Functionality**: If possible, provide a way to revert to the previous season if the new season was created by mistake.

6. **Season Completion Status**: The backend now tracks whether a season is completed based on both:

   - The season's end date being in the past
   - All matches for the season being completed (finished, postponed, or cancelled)

   When displaying season information, consider showing this completion status to users.

7. **Handling Completed Seasons**: When a season is marked as completed:
   - Users can no longer make predictions for matches in that season
   - The predictable_matches endpoint will return a season_over flag
   - The frontend should display appropriate messaging

## Conclusion

This implementation provides a user-friendly way for admins to create new seasons for competitions. The UI guides them through the process, handles special cases for different competition sources, and provides appropriate feedback.
