# Frontend Implementation Guide for Creating a New Season

This document outlines the API endpoints and requirements for implementing the "Create New Season" functionality in the frontend application.

## API Endpoints

### 1. Get Competition Requirements

Before creating a new season, the frontend should fetch information about what parameters are required for the specific competition.

```
GET /api/v1/admin/competitions/:id/new_season_requirements
```

#### Response

```json
{
  "competition": {
    "id": 123,
    "name": "Premier League",
    "code": "PL",
    "source": "football_data",
    "external_service_id": "2021"
  },
  "requirements": {
    "needs_season_external_service_id": false,
    "season_id_format": null
  }
}
```

OR

```json
{
  "competition": {
    "id": 124,
    "name": "Swedish Superliga",
    "code": "SV",
    "source": "api_football",
    "external_service_id": "113"
  },
  "requirements": {
    "needs_season_external_service_id": true,
    "season_id_format": "year",
    "season_id_example": "2026"
  }
}
```

OR

```json
{
  "competition": {
    "id": 125,
    "name": "Icelandic Cup",
    "code": "BDkk",
    "source": "ksi_soap",
    "external_service_id": "1"
  },
  "requirements": {
    "needs_season_external_service_id": true,
    "season_id_format": "numeric_id",
    "season_id_example": "50000"
  }
}
```

### 2. Create New Season

```
POST /api/v1/admin/competitions/:id/create_new_season
```

#### Request Body

The request body depends on the competition source:

For FootballData competitions (PL, CL):

```json
{}
```

For ApiFootball competitions (SV):

```json
{
  "season_external_service_id": "2026"
}
```

For KSI competitions (BDkk):

```json
{
  "season_external_service_id": "50000"
}
```

#### Response

```json
{
  "message": "New season created successfully for Competition Name",
  "season": {
    "id": 123,
    "start_date": "2023-08-01",
    "end_date": "2024-05-31",
    "current_matchday": 1,
    "external_service_id": "2023",
    "source": "football_data"
  }
}
```

#### Error Responses

1. Missing required parameters:

```json
{
  "errors": [
    "Season external service ID is required for ApiFootball competitions"
  ],
  "status": 400
}
```

2. Missing competition external_service_id:

```json
{
  "errors": [
    "Competition external service ID is missing. Please update the competition first."
  ],
  "status": 400
}
```

3. Unsupported competition code:

```json
{
  "errors": ["Unsupported competition code"],
  "status": 400
}
```

4. API error:

```json
{
  "errors": ["Failed to create new season: API error message"],
  "status": 422
}
```

## Implementation Flow

1. **Fetch Competition Requirements**:

   - Call the `/api/v1/admin/competitions/:id/new_season_requirements` endpoint to determine what parameters are needed for the specific competition.

2. **Display Form Based on Requirements**:

   - If `needs_season_external_service_id` is true, show a field for the season external service ID.
   - Use `season_id_format` and `season_id_example` to provide guidance to the user.

3. **Submit Create New Season Request**:

   - Call the `/api/v1/admin/competitions/:id/create_new_season` endpoint with the appropriate parameters.
   - Include `season_external_service_id` if required by the competition.

4. **Handle Response**:
   - Display success message if the season was created successfully.
   - Display appropriate error message if the request failed.
   - Refresh the competition data to show the new season.

## API Integration Example

```javascript
// Fetch competition requirements
const getNewSeasonRequirements = async (competitionId) => {
  try {
    const response = await api.get(
      `/api/v1/admin/competitions/${competitionId}/new_season_requirements`
    );
    return response.data;
  } catch (error) {
    throw new Error(
      error.response?.data?.errors?.[0] || "Failed to fetch requirements"
    );
  }
};

// Create new season
const createNewSeason = async (competitionId, params = {}) => {
  try {
    const response = await api.post(
      `/api/v1/admin/competitions/${competitionId}/create_new_season`,
      params
    );
    return response.data;
  } catch (error) {
    throw new Error(
      error.response?.data?.errors?.[0] || "Failed to create new season"
    );
  }
};

// Example usage in a component
const handleCreateNewSeason = async (competitionId) => {
  try {
    // Step 1: Get requirements
    const requirements = await getNewSeasonRequirements(competitionId);

    // Step 2: Prepare form data based on requirements
    let formData = {};

    if (requirements.requirements.needs_season_external_service_id) {
      // In a real implementation, this would come from a form input
      const seasonExternalServiceId = prompt(
        `Please enter the season external service ID (${requirements.requirements.season_id_format}). Example: ${requirements.requirements.season_id_example}`
      );

      if (!seasonExternalServiceId) {
        throw new Error("Season external service ID is required");
      }

      formData.season_external_service_id = seasonExternalServiceId;
    }

    // Step 3: Submit request
    const result = await createNewSeason(competitionId, formData);

    // Step 4: Handle response
    console.log("New season created:", result);
    alert(result.message);

    // Refresh data
    // fetchCompetitionDetails();
  } catch (error) {
    console.error("Error creating new season:", error.message);
    alert(`Error: ${error.message}`);
  }
};
```

## Required Parameters by Competition Source

| Competition Source     | Required Parameters                                           |
| ---------------------- | ------------------------------------------------------------- |
| football_data (PL, CL) | None                                                          |
| api_football (SV)      | season_external_service_id (year format, e.g., "2026")        |
| ksi_soap (BDkk)        | season_external_service_id (numeric ID format, e.g., "50000") |

## Error Handling

The frontend should handle these potential errors:

1. User is not an admin (401 Unauthorized)
2. Missing season external service ID for KSI and ApiFootball competitions (400 Bad Request)
3. Missing competition external_service_id for ApiFootball competitions (400 Bad Request)
4. API errors (network issues, server errors) (422 Unprocessable Entity)
5. Season already exists (handled by backend, returns existing season)

## Notes

- Only admin users can create new seasons
- Creating a new season is a potentially destructive operation, as it will transition all leagues to the new season
- The backend handles the logic of determining which service to use based on the competition code
- The frontend only needs to provide the required parameters based on the competition source
