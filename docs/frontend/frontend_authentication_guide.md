# Frontend Authentication Guide

## User Registration API

### Endpoint

```
POST /users/tokens
```

### Request

```json
{
  "username": "user123",
  "email": "<EMAIL>",
  "password": "password123"
}
```

### Success Response

```json
{
  "status": "success",
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "user123",
    "created_at": "2023-01-01T12:00:00.000Z",
    "updated_at": "2023-01-01T12:00:00.000Z"
  }
}
```

### Error Responses

#### When Username Is Already Taken

```json
{
  "status": "error",
  "message": "Username already taken",
  "errors": {
    "username": ["has already been taken"]
  }
}
```

#### When Email Is Already Taken

```json
{
  "status": "error",
  "message": "Email already taken",
  "errors": {
    "email": ["has already been taken"]
  }
}
```

#### Other Validation Errors

```json
{
  "status": "error",
  "message": "Validation failed",
  "errors": {
    "password": ["is too short (minimum is 6 characters)"]
  }
}
```

## Frontend Implementation Guide

### Handling Registration Errors

The backend now provides specific message strings that indicate whether a username or email is already taken. These messages can be used to display targeted error notifications to users.

Example implementation:

```javascript
async function registerUser(userData) {
  try {
    const response = await fetch("/users/tokens", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(userData),
    });

    const data = await response.json();

    if (!response.ok) {
      // Handle specific error cases
      if (data.message === "Username already taken") {
        // Show username field error
        showFieldError("username", "This username is already taken");
      } else if (data.message === "Email already taken") {
        // Show email field error
        showFieldError("email", "This email address is already registered");
      } else {
        // Handle other validation errors
        Object.entries(data.errors).forEach(([field, errors]) => {
          showFieldError(field, errors[0]);
        });
      }
      return null;
    }

    return data.data; // Return user data on success
  } catch (error) {
    console.error("Registration error:", error);
    showGeneralError("An unexpected error occurred. Please try again later.");
    return null;
  }
}
```

This approach allows you to provide immediate, field-specific feedback to users when their chosen username or email is already in use.
