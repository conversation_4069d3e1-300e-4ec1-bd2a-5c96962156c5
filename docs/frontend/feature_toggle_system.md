# Feature Toggle System

This document explains how to use the feature toggle system in the application. The feature toggle system allows you to conditionally render components based on feature flags stored in localStorage.

## Overview

The feature toggle system provides a way to enable or disable features without deploying new code. All feature toggles are stored in localStorage with the prefix `FT_`.

Key components:

- Feature Toggle Provider - Makes toggles available throughout the app
- Feature Toggle Component - Conditionally renders content based on toggle state
- Feature Toggle Hook - Provides programmatic access to toggle state
- Feature Toggle Admin - UI for managing toggles

## Usage Examples

### Basic Component Wrapping

The simplest way to use feature toggles is to wrap a component with the `FeatureToggle` component:

```tsx
import FeatureToggle from "@/src/components/feature-toggle/feature-toggle";

// Only show MyComponent when "newFeature" is enabled
<FeatureToggle featureName="newFeature">
  <MyComponent />
</FeatureToggle>;
```

### With Fallback Content

You can provide fallback content to show when a feature is disabled:

```tsx
<FeatureToggle featureName="newFeature" fallback={<LegacyComponent />}>
  <MyComponent />
</FeatureToggle>
```

### Inverted Logic

You can invert the logic to show content when a feature is disabled:

```tsx
<FeatureToggle featureName="legacyFeature" inverted={true}>
  <LegacyComponent />
</FeatureToggle>
```

## Using the Hook

For more control, you can use the `useFeatureToggle` hook:

```tsx
import { useFeatureToggle } from "@/src/hooks/use-feature-toggle";

function MyComponent() {
  // Track a specific feature
  const { enabled, toggle } = useFeatureToggle("myFeature");

  // Or use the general methods
  const {
    isEnabled,
    enableFeature,
    disableFeature,
    toggleFeature,
    allFeatures,
  } = useFeatureToggle();

  return (
    <div>
      {enabled && <NewFeature />}

      <button onClick={toggle}>Toggle My Feature</button>

      <button onClick={() => toggleFeature("anotherFeature")}>
        Toggle Another Feature
      </button>
    </div>
  );
}
```

## Using the Context

You can also access feature toggles directly from the context:

```tsx
import { useFeatureToggleContext } from "@/src/utils/providers/FeatureToggleProvider";

function MyComponent() {
  const {
    isEnabled,
    enableFeature,
    disableFeature,
    toggleFeature,
    getAllFeatures,
  } = useFeatureToggleContext();

  return (
    <div>
      {isEnabled("myFeature") && <NewFeature />}

      <button onClick={() => toggleFeature("myFeature")}>Toggle Feature</button>
    </div>
  );
}
```

## Admin Interface

The system includes an admin component for managing feature toggles:

```tsx
import FeatureToggleAdmin from "@/src/components/feature-toggle/feature-toggle-admin";

function AdminPage() {
  return (
    <div>
      <h1>Admin Dashboard</h1>
      <FeatureToggleAdmin
        title="Feature Management"
        description="Enable or disable features in the application"
      />
    </div>
  );
}
```

The admin interface allows you to:

- View all current feature toggles
- Add new feature toggles
- Enable/disable existing toggles
- Remove feature toggles

## Technical Implementation

### Storage Format

Feature toggles are stored in localStorage with the prefix `FT_`. For example, a feature named "newFeature" would be stored as `FT_newFeature` in localStorage with a value of "true" or "false".

### Context Provider

The `FeatureToggleProvider` is added to the root layout, so feature toggles are available throughout the application:

```tsx
// src/app/[locale]/layout.tsx
<body>
  <NextIntlClientProvider locale={locale}>
    <ReactQueryProvider>
      <FeatureToggleProvider>
        {children}
        <Toaster position="bottom-right" richColors />
      </FeatureToggleProvider>
    </ReactQueryProvider>
  </NextIntlClientProvider>
</body>
```

### Component Structure

The feature toggle system consists of the following files:

- `src/types/feature-toggle.ts` - TypeScript interfaces
- `src/utils/feature-toggle.ts` - Utility functions
- `src/hooks/use-feature-toggle.ts` - React hook
- `src/utils/providers/FeatureToggleProvider.tsx` - Context provider
- `src/components/feature-toggle/feature-toggle.tsx` - Component
- `src/components/feature-toggle/feature-toggle-admin.tsx` - Admin UI

## Best Practices

1. **Use descriptive feature names** - Choose clear, descriptive names for your feature toggles.
2. **Clean up unused toggles** - Remove feature toggles from localStorage when they're no longer needed.
3. **Document your toggles** - Keep a list of active feature toggles and their purpose.
4. **Use for temporary features** - Feature toggles are best for temporary features or A/B testing, not for permanent configuration.
5. **Consider performance** - Don't overuse feature toggles, as they can impact performance if used excessively.

## Use Cases

1. **Gradual Rollout** - Gradually roll out new features to users
2. **A/B Testing** - Test different versions of a feature
3. **Canary Releases** - Release features to a small subset of users
4. **Feature Flags** - Enable/disable features based on user roles or preferences
5. **Kill Switches** - Quickly disable problematic features without deploying

## Feature Toggle Examples

### YouTube Feature Toggles

The YouTube integration is split into multiple feature toggles:

1. **youtubeIntegration** - Legacy toggle that enables all YouTube features (kept for backward compatibility)
2. **youtubeConnect** - Controls the ability to connect YouTube accounts
3. **youtubeContentCreator** - Controls the content creator mode functionality

This separation allows for more granular control over different aspects of the YouTube integration. For example, you could enable account connection while keeping content creator mode disabled.

#### Initialization Behavior

- In **development mode**, YouTube-related feature toggles are enabled by default for easier testing.
- In **production mode**, feature toggles are **not initialized at all**. This prevents exposing toggle names in the client code.
- Administrators must use the admin interface to manage feature toggles in production.

## Browser Support

The feature toggle system relies on localStorage, which is supported in all modern browsers. However, it will not work in environments where localStorage is not available (e.g., some private browsing modes).

## Error Handling

The system includes error handling for cases where localStorage is not available. In such cases, all features will be considered disabled by default.
