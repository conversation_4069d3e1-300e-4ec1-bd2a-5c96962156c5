#!/bin/bash

echo "Cleaning up and standardizing scripts directory..."

# List of scripts to remove
SCRIPTS_TO_REMOVE=(
  # Puma debugging and fixing scripts
  "check_puma_socket.sh"
  "check_rails_boot.sh"
  "debug_puma_thoroughly.sh"
  "fix_nginx_socket.sh"
  "fix_socket_connection.sh"
  "fix_socket_permissions.sh"
  "manual_puma_start.sh"
  "minimal_puma_start.sh"
  "make_simplified_puma_permanent.sh"

  # One-time setup scripts
  "digital_ocean_setup.sh"
  "fix_final_issues.sh"
  "fix_health_controllers.sh"
  "setup_ssl_certificate.sh"
  "verify_https_api.sh"

  # Sidekiq configuration scripts
  "deploy_sidekiq_fixes.sh"
  "fix_sidekiq.sh"
  "restart_sidekiq.sh"
  "update_sidekiq_config.sh"

  # Old cleanup script
  "cleanup_scripts.sh"
)

# Scripts to rename (old_name => new_name)
declare -A SCRIPTS_TO_RENAME=(
  ["auto_maintenance.sh"]="maintenance_auto.sh"
  ["check_server_status.sh"]="maintenance_server_status.sh"
  ["download_ksi_mock_data.sh"]="data_download_ksi_mock.sh"
  ["fetch_ksi_data.rb"]="data_fetch_ksi.rb"
)

# Directory containing scripts
SCRIPTS_DIR="$(dirname "$0")"

# Count for removed and renamed scripts
REMOVED_COUNT=0
RENAMED_COUNT=0
BACKUP_DIR="$SCRIPTS_DIR/obsolete_backup"

# Create backup directory if it doesn't exist
if [ ! -d "$BACKUP_DIR" ]; then
  mkdir -p "$BACKUP_DIR"
  echo "Created backup directory: $BACKUP_DIR"
fi

echo "Step 1: Moving obsolete scripts to backup..."
# Move scripts to backup instead of deleting them
for script in "${SCRIPTS_TO_REMOVE[@]}"; do
  if [ -f "$SCRIPTS_DIR/$script" ]; then
    mv "$SCRIPTS_DIR/$script" "$BACKUP_DIR/"
    echo "✓ Moved $script to backup"
    REMOVED_COUNT=$((REMOVED_COUNT + 1))
  else
    echo "× $script doesn't exist"
  fi
done

echo ""
echo "Step 2: Renaming scripts to follow naming convention..."
# Rename scripts according to the new naming convention
for old_name in "${!SCRIPTS_TO_RENAME[@]}"; do
  new_name="${SCRIPTS_TO_RENAME[$old_name]}"
  if [ -f "$SCRIPTS_DIR/$old_name" ]; then
    mv "$SCRIPTS_DIR/$old_name" "$SCRIPTS_DIR/$new_name"
    echo "✓ Renamed $old_name to $new_name"
    RENAMED_COUNT=$((RENAMED_COUNT + 1))
  else
    echo "× $old_name doesn't exist"
  fi
done

echo ""
echo "Summary:"
echo "- Moved $REMOVED_COUNT obsolete scripts to $BACKUP_DIR"
echo "- Renamed $RENAMED_COUNT scripts to follow the naming convention"
echo ""
echo "If everything works fine after a few weeks, you can safely delete the backup directory with:"
echo "rm -rf $BACKUP_DIR"
echo ""
echo "Remaining scripts in $SCRIPTS_DIR:"
ls -la "$SCRIPTS_DIR" | grep -v "obsolete_backup" | grep "\.sh\|\.rb"
echo ""
echo "A README.md file has been created in the scripts directory with documentation."
echo "Please review it and make any necessary adjustments."

exit 0
