# Scripts Directory

This directory contains utility scripts for the BragRights backend application. These scripts are organized by their purpose and function.

## Maintenance Scripts

- **maintenance_auto.sh** (formerly auto_maintenance.sh) - Automated maintenance script that handles regular tasks like log rotation, disk space monitoring, and service health checks.
- **maintenance_server_status.sh** (formerly check_server_status.sh) - Checks the status of all services (Puma, Nginx, Sidekiq, Redis) and system resources.

## Data Management Scripts

- **data_download_ksi_mock.sh** (formerly download_ksi_mock_data.sh) - Downloads KSI tournament mock data from the production server.
- **data_fetch_ksi.rb** (formerly fetch_ksi_data.rb) - Ruby script for fetching and processing KSI data.

## Testing Scripts

- **test_eager_loading.rb** - Tests Rails eager loading to catch controller inheritance issues.
- **test_youtube_api.rb** - Tests YouTube API integration.

## Usage

Most scripts can be run directly from the command line:

```bash
./scripts/maintenance_server_status.sh
```

Ruby scripts should be run with Rails runner:

```bash
bundle exec rails runner scripts/test_eager_loading.rb
```

## Adding New Scripts

When adding new scripts to this directory, please follow these guidelines:

1. Use a consistent naming convention:
   - `maintenance_*.sh` - For server maintenance scripts
   - `data_*.sh` or `data_*.rb` - For data management scripts
   - `test_*.rb` or `test_*.sh` - For testing scripts
   - `setup_*.sh` - For one-time setup scripts

2. Make shell scripts executable:
   ```bash
   chmod +x scripts/your_new_script.sh
   ```

3. Include a header comment in each script explaining its purpose and usage.

4. Update this README when adding new scripts.
