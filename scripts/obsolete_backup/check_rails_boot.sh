#!/bin/bash

APP_PATH="/var/www/bragrights-be"
LOG_FILE="$APP_PATH/log/boot_error.log"

echo "Checking if Rails can boot properly..."
echo "Writing detailed logs to $LOG_FILE"

cd $APP_PATH

# Clear the log file
> $LOG_FILE

# Try to boot Rails in console mode with verbose logging
echo "Attempting to boot Rails in console mode..."
echo "==================== RAILS BOOT LOG ====================" >> $LOG_FILE 2>&1
RAILS_ENV=production bundle exec rails console -e production >> $LOG_FILE 2>&1 || true

echo "---------- ANALYZING BOOT ERRORS ----------"

# Check if we have any specific errors
grep -i "error\|exception\|fail" $LOG_FILE | tail -n 30

echo ""
echo "To see the full boot log, check: $LOG_FILE"
echo ""
echo "If there are Rails boot issues, try fixing them with:"
echo "1. Check your database configuration and credentials"
echo "2. Check all required environment variables are set in .env"
echo "3. Ensure all gem dependencies are properly installed"
echo ""
echo "You can manually attempt to start Puma with:"
echo "cd $APP_PATH && RAILS_ENV=production bundle exec puma -C config/puma_minimal.rb"

# Create a simple minimal config file for testing
cat > $APP_PATH/config/puma_minimal.rb <<EOL
#!/usr/bin/env puma
directory '$APP_PATH'
environment 'production'
threads 1, 1
workers 0
EOL

exit 0
