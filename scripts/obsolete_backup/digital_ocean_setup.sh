#!/bin/bash

set -e

echo "Setting up BragRights on Digital Ocean..."

# Configuration
APP_PATH="/var/www/bragrights-be"
USER=$(whoami)
RAILS_ENV="production"

# Functions
log() {
  echo "$(date +'%Y-%m-%d %H:%M:%S') - $1"
}

# Check if running as root or with sudo privileges
if [ "$(id -u)" -eq 0 ]; then
  log "Please run this script as a non-root user with sudo privileges"
  exit 1
fi

# Setup application directory
log "Setting up application directory..."
sudo mkdir -p $APP_PATH
sudo chown $USER:$USER $APP_PATH

# Clone repository if needed
if [ ! -d "$APP_PATH/.git" ]; then
  log "Cloning repository..."
  <NAME_EMAIL>:yourusername/bragrights-be.git $APP_PATH
  cd $APP_PATH
else
  log "Repository already exists, updating..."
  cd $APP_PATH
  git pull origin main
fi

# Setup PostgreSQL database
log "Setting up PostgreSQL database..."
sudo -u postgres psql -c "CREATE USER bragrights WITH PASSWORD 'your_password';" || true
sudo -u postgres psql -c "CREATE DATABASE bragrights_production OWNER bragrights;" || true
sudo -u postgres psql -c "ALTER USER bragrights WITH SUPERUSER;" || true

# Setup environment variables
log "Setting up environment variables..."
if [ ! -f "$APP_PATH/.env" ]; then
  cp $APP_PATH/.env.example $APP_PATH/.env
  sed -i "s/DATABASE_URL=.*/DATABASE_URL=postgres:\/\/bragrights:your_password@localhost:5432\/bragrights_production/" $APP_PATH/.env
  sed -i "s/REDIS_URL=.*/REDIS_URL=redis:\/\/localhost:6379\/0/" $APP_PATH/.env
  sed -i "s/RAILS_ENV=.*/RAILS_ENV=production/" $APP_PATH/.env
  echo "SECRET_KEY_BASE=$(openssl rand -hex 64)" >> $APP_PATH/.env
fi

# Install dependencies
log "Installing dependencies..."
cd $APP_PATH
bundle install --deployment --without development test

# Setup the database
log "Setting up the database..."
cd $APP_PATH
RAILS_ENV=$RAILS_ENV bundle exec rails db:create || true
RAILS_ENV=$RAILS_ENV bundle exec rails db:migrate
RAILS_ENV=$RAILS_ENV bundle exec rails db:seed || true

# Compile assets
log "Compiling assets..."
RAILS_ENV=$RAILS_ENV bundle exec rails assets:precompile

# Setup Nginx
log "Setting up Nginx..."
sudo tee /etc/nginx/sites-available/bragrights > /dev/null <<EOL
upstream puma {
  server unix:///var/www/bragrights-be/tmp/sockets/puma.sock;
}

server {
  listen 80;
  server_name your-domain.com;

  root /var/www/bragrights-be/public;
  access_log /var/log/nginx/bragrights-be-access.log;
  error_log /var/log/nginx/bragrights-be-error.log;

  location / {
    try_files \$uri @puma;
  }

  location @puma {
    proxy_pass http://puma;
    proxy_set_header Host \$host;
    proxy_set_header X-Real-IP \$remote_addr;
  }

  location /cable {
    proxy_pass http://puma;
    proxy_http_version 1.1;
    proxy_set_header Upgrade \$http_upgrade;
    proxy_set_header Connection "Upgrade";
  }
}
EOL

sudo ln -sf /etc/nginx/sites-available/bragrights /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl restart nginx

# Setup Puma service
log "Setting up Puma service..."
sudo tee /etc/systemd/system/puma.service > /dev/null <<EOL
[Unit]
Description=Puma HTTP Server
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$APP_PATH
Environment="RAILS_ENV=$RAILS_ENV"
EnvironmentFile=$APP_PATH/.env
ExecStart=/home/<USER>/.rbenv/shims/bundle exec puma -C $APP_PATH/config/puma.rb
ExecReload=/bin/kill -s USR1 \$MAINPID
TimeoutSec=300
Restart=always

[Install]
WantedBy=multi-user.target
EOL

# Setup Sidekiq service
log "Setting up Sidekiq service..."
sudo tee /etc/systemd/system/sidekiq.service > /dev/null <<EOL
[Unit]
Description=Sidekiq job processor
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$APP_PATH
Environment="RAILS_ENV=$RAILS_ENV"
EnvironmentFile=$APP_PATH/.env
ExecStart=/home/<USER>/.rbenv/shims/bundle exec sidekiq -e \$RAILS_ENV
ExecReload=/bin/kill -s USR1 \$MAINPID
TimeoutSec=300
Restart=always

[Install]
WantedBy=multi-user.target
EOL

# Enable services
log "Enabling services..."
sudo systemctl enable puma
sudo systemctl enable sidekiq
sudo systemctl daemon-reload

# Start services
log "Starting services..."
sudo systemctl start puma
sudo systemctl start sidekiq

log "Setup completed successfully!"
log "Make sure to:"
log "1. Update the database password in .env file"
log "2. Set your domain name in the Nginx configuration"
log "3. Setup SSL with Certbot"
log "4. Configure GitHub repository secrets for CI/CD"

exit 0
