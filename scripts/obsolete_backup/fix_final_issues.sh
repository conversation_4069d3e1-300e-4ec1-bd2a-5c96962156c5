#!/bin/bash

set -e

echo "Fixing remaining issues with socket connection and API routes..."
APP_PATH="/var/www/bragrights-be"

# 1. Fix Nginx configuration with proper socket path
echo "Fixing Nginx socket configuration..."
sudo tee /etc/nginx/sites-available/bragrights > /dev/null <<EOL
upstream puma {
  server unix:///var/www/bragrights-be/tmp/sockets/puma.sock;
}

server {
  listen 80;
  server_name api.bragrights.football;

  root /var/www/bragrights-be/public;
  access_log /var/log/nginx/bragrights-be-access.log;
  error_log /var/log/nginx/bragrights-be-error.log;

  # Add gzip to reduce bandwidth usage
  gzip on;
  gzip_vary on;
  gzip_proxied any;
  gzip_comp_level 6;
  gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

  # Apply CORS headers for frontend access
  location / {
    # Add CORS headers
    add_header 'Access-Control-Allow-Origin' 'https://bragrights.football' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'Origin, X-Requested-With, Content-Type, Accept, Authorization' always;
    
    # Handle preflight requests
    if (\$request_method = 'OPTIONS') {
      add_header 'Access-Control-Allow-Origin' 'https://bragrights.football' always;
      add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
      add_header 'Access-Control-Allow-Headers' 'Origin, X-Requested-With, Content-Type, Accept, Authorization' always;
      add_header 'Access-Control-Max-Age' 1728000;
      add_header 'Content-Type' 'text/plain charset=UTF-8';
      add_header 'Content-Length' 0;
      return 204;
    }
    
    try_files \$uri @puma;
  }

  location @puma {
    proxy_pass http://puma;
    proxy_set_header Host \$host;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto \$scheme;
  }

  # Apply rate limiting to API endpoints
  location /api {
    limit_req zone=api burst=20 nodelay;
    try_files \$uri @puma;
  }
}
EOL

# 2. Check if Puma socket exists and has correct permissions
echo "Checking and creating Puma socket directory..."
mkdir -p $APP_PATH/tmp/sockets
chmod -R 755 $APP_PATH/tmp

# 3. Restart services
echo "Restarting services..."
sudo nginx -t && sudo systemctl restart nginx
sudo systemctl restart puma
sudo systemctl restart sidekiq

echo "Waiting for services to start..."
sleep 3

# 4. Verify API routes
echo "Testing API health endpoints..."
curl -s http://localhost/api/health || echo "API health endpoint test failed"
curl -s http://localhost/api/v1/health || echo "API v1 health endpoint test failed"

echo "Checking server status..."
$APP_PATH/scripts/check_server_status.sh

echo "If the health endpoints are still not working, make sure the controllers are properly defined."
echo "You can verify the routes with: cd $APP_PATH && RAILS_ENV=production bundle exec rails routes | grep health"

exit 0
