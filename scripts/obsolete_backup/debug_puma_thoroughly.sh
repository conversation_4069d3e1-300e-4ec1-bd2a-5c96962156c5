#!/bin/bash

echo "================ Thorough Puma Debugging ================"
APP_PATH="/var/www/bragrights-be"

# 1. Check systemd logs
echo "1. Checking latest Puma systemd logs:"
sudo journalctl -u puma -n 30 --no-pager

# 2. Check environment variables
echo -e "\n2. Checking environment file existence and content:"
if [ -f "$APP_PATH/.env" ]; then
  echo "✅ .env file exists with $(wc -l < $APP_PATH/.env) lines"
  echo "File permissions: $(ls -l $APP_PATH/.env)"
else
  echo "❌ .env file is missing!"
fi

# 3. Check for common issues
echo -e "\n3. Checking for common issues:"
echo "Ruby version: $(ruby -v)"
echo "Bundle version: $(bundle -v)"
echo "Puma version: $(bundle list | grep puma || echo "Puma not found in bundle")"

# 4. Verify database connection
echo -e "\n4. Testing database connection:"
cd $APP_PATH
RAILS_ENV=production bundle exec rails runner "begin; puts 'Database connection OK' if ActiveRecord::Base.connection.execute('SELECT 1'); rescue => e; puts \"Database Error: #{e.message}\"; end" 2>/dev/null || echo "Failed to connect to database"

# 5. Check socket directory
echo -e "\n5. Checking socket directory:"
mkdir -p $APP_PATH/tmp/sockets
ls -la $APP_PATH/tmp/sockets/
echo "Directory permissions: $(ls -ld $APP_PATH/tmp/sockets/)"

# 6. Check if redis is working
echo -e "\n6. Checking Redis connection:"
redis-cli ping || echo "Redis is not responding"

# 7. Check Puma config
echo -e "\n7. Puma configuration content:"
cat $APP_PATH/config/puma_production.rb

# 8. Try starting Puma directly with more verbosity
echo -e "\n8. Attempting to start Puma manually with increased verbosity:"
cd $APP_PATH
echo "Starting Puma with: RAILS_ENV=production bundle exec puma -C config/puma_production.rb --debug"
RAILS_ENV=production bundle exec puma -C config/puma_production.rb --debug

echo -e "\nDebugging completed. If Puma still fails, you may need to check application-specific errors."
exit 0
