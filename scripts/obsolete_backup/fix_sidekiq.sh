#!/bin/bash

set -e

echo "Fixing Sidekiq service..."
APP_PATH="/var/www/bragrights-be"

# Check Sidekiq logs
echo "Checking Sidekiq logs for errors:"
sudo journalctl -u sidekiq -n 20 --no-pager

# Create simplified Sidekiq service file
echo "Creating minimal Sidekiq service file..."
sudo tee /etc/systemd/system/sidekiq.service > /dev/null <<EOL
[Unit]
Description=Sidekiq job processor
After=network.target redis-server.service

[Service]
Type=simple
User=$(whoami)
WorkingDirectory=$APP_PATH
Environment="RAILS_ENV=production"
EnvironmentFile=$APP_PATH/.env
ExecStart=$(which bundle) exec sidekiq -e production -C $APP_PATH/config/sidekiq.yml
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOL

# Create minimal Sidekiq config
echo "Creating minimal Sidekiq configuration..."
mkdir -p $APP_PATH/config
cat > $APP_PATH/config/sidekiq.yml <<EOL
---
:concurrency: 1
:queues:
  - default
EOL

# Reload and restart
sudo systemctl daemon-reload
sudo systemctl restart sidekiq

echo "Waiting for Sidekiq to start..."
sleep 3

# Check status
sudo systemctl status sidekiq --no-pager

echo "Sidekiq fix completed."
exit 0
