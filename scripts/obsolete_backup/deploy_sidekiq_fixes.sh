#!/bin/bash
# deploy_sidekiq_fixes.sh
# Script to deploy Sidekiq configuration to production server

echo "Deploying updated Sidekiq configuration to production server"

# Copy the updated job file and service configuration
echo "Copying Sidekiq job and service file..."
scp app/jobs/daily_match_update_job.rb <EMAIL>:/var/www/bragrights-be/app/jobs/
scp config/sidekiq.service <EMAIL>:/var/www/bragrights-be/config/

echo "Files have been copied to your production server."
echo ""
echo "Next steps to complete on your production server:"
echo ""
echo "Run these commands on your production server:"
echo "  cd /var/www/bragrights-be"
echo "  sudo cp config/sidekiq.service /etc/systemd/system/sidekiq.service"
echo "  sudo systemctl daemon-reload"
echo "  sudo systemctl restart sidekiq"
echo "  sudo systemctl status sidekiq"
echo ""
echo "You can SSH into your server with: ssh <EMAIL>"