#!/bin/bash

set -e

echo "Fixing Nginx socket configuration..."

# First check where <PERSON><PERSON> is actually creating its socket
SOCKET_PATH=$(ps aux | grep puma | grep -v grep | grep -o "unix://[^'\" ]*" | head -1)

if [ -z "$SOCKET_PATH" ]; then
  echo "❌ Could not find Puma socket path in process list."
  SOCKET_PATH="/var/www/bragrights-be/tmp/sockets/puma.sock"
  echo "Using default socket path: $SOCKET_PATH"
else
  SOCKET_PATH=${SOCKET_PATH#unix://}  # Remove the unix:// prefix
  echo "✅ Found Puma socket: $SOCKET_PATH"
fi

# Update Nginx configuration
echo "Updating Nginx configuration to use socket path: $SOCKET_PATH"
sudo sed -i "s|unix:///var/www/bragrights-be/tmp/sockets/puma.sock|unix://$SOCKET_PATH|g" /etc/nginx/sites-available/bragrights

# Test and reload Nginx
sudo nginx -t && sudo systemctl reload nginx

echo "Nginx configuration updated. Testing connection to API..."
curl -v http://localhost/api/health

exit 0
