#!/bin/bash

set -e

echo "Fixing health controllers for API..."

APP_PATH="/var/www/bragrights-be"

# Fix the API base health controller
cat > "$APP_PATH/app/controllers/api/health_controller.rb" <<'EOL'
module Api
  class HealthController < ApplicationController
    # Instead of skipping a specific callback, skip authentication entirely
    skip_before_action :authenticate_user!, if: -> { defined?(authenticate_user!) }
    # For token authentication, we need to be more careful
    if ActionController.instance_methods.include?(:authenticate_user_from_token!)
      skip_before_action :authenticate_user_from_token!
    end

    # Simple health check endpoint at /api/health
    def check
      render json: {
        status: 'ok',
        version: Rails.application.config.respond_to?(:version) ? Rails.application.config.version : 'unknown',
        environment: Rails.env,
        database: database_connected?,
        redis: redis_connected?
      }, status: :ok
    end

    private

    def database_connected?
      ActiveRecord::Base.connection.execute('SELECT 1')
      true
    rescue StandardError => e
      false
    end

    def redis_connected?
      Redis.new(url: ENV['REDIS_URL']).ping == 'PONG'
    rescue StandardError => e
      false
    end
  end
end
EOL

# Fix the API v1 health controller
cat > "$APP_PATH/app/controllers/api/v1/health_controller.rb" <<'EOL'
module Api
  module V1
    class HealthController < ApplicationController
      # Instead of skipping a specific callback, skip authentication entirely
      skip_before_action :authenticate_user!, if: -> { defined?(authenticate_user!) }
      # For token authentication, we need to be more careful
      if ActionController.instance_methods.include?(:authenticate_user_from_token!)
        skip_before_action :authenticate_user_from_token!
      end

      # Simple health check endpoint
      def check
        render json: {
          status: 'ok',
          version: Rails.application.config.respond_to?(:version) ? Rails.application.config.version : 'unknown',
          environment: Rails.env,
          database: database_connected?,
          redis: redis_connected?
        }, status: :ok
      end

      private

      def database_connected?
        ActiveRecord::Base.connection.execute('SELECT 1')
        true
      rescue StandardError => e
        false
      end

      def redis_connected?
        Redis.new(url: ENV['REDIS_URL']).ping == 'PONG'
      rescue StandardError => e
        false
      end
    end
  end
end
EOL

# Restart puma to apply changes
echo "Restarting Puma to apply changes..."
sudo systemctl restart puma

echo "Health controllers fixed. Check if Puma starts correctly now."
exit 0
