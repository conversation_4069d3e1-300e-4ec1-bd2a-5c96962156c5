#!/bin/bash

echo "Checking Puma socket..."
APP_PATH="/var/www/bragrights-be"
SOCKET_PATH="$APP_PATH/tmp/sockets/puma.sock"

# Check if socket exists
if [ -S "$SOCKET_PATH" ]; then
  echo "✅ Socket exists at $SOCKET_PATH"
  ls -la "$SOCKET_PATH"
else
  echo "❌ Socket does not exist at $SOCKET_PATH"
  
  # Check Puma config
  echo "Checking Puma config for socket binding:"
  grep -n "bind" "$APP_PATH/config/puma_production.rb"
  
  # Verify Puma is actually running
  echo "Checking Puma process:"
  ps aux | grep puma | grep -v grep
fi

# Check socket directory permissions
echo "Socket directory permissions:"
ls -la "$APP_PATH/tmp/sockets/"
echo "Socket parent directory permissions:"
ls -la "$APP_PATH/tmp/"

# Check socket in Puma logs
echo "Checking Puma logs for socket information:"
tail -20 "$APP_PATH/log/puma_*.log" 2>/dev/null || echo "No Puma logs found"

exit 0
