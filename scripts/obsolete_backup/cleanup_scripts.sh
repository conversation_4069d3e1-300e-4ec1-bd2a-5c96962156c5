#!/bin/bash

echo "Cleaning up redundant scripts..."

# List of scripts to remove
SCRIPTS_TO_REMOVE=(
  "fix_puma_config.sh"
  "check_puma_logs.sh"
  "fix_puma_service.sh"
  "verify_puma_config.sh"
  "fix_puma_socket.sh"
  "fix_pry_issue.sh"
  "fix_seed_dependencies.sh"
  "debug_puma.sh"
  "debug_puma_thoroughly.sh"
  "simplify_puma_service.sh"
  "minimal_puma_start.sh"
)

# Directory containing scripts
SCRIPTS_DIR="/home/<USER>/projects/brag-rights-be/scripts"

# Count for removed scripts
REMOVED_COUNT=0

# Remove scripts
for script in "${SCRIPTS_TO_REMOVE[@]}"; do
  if [ -f "$SCRIPTS_DIR/$script" ]; then
    rm "$SCRIPTS_DIR/$script"
    echo "✓ Removed $script"
    REMOVED_COUNT=$((REMOVED_COUNT + 1))
  else
    echo "× $script doesn't exist"
  fi
done

echo ""
echo "Cleanup complete! Removed $REMOVED_COUNT scripts."
echo ""
echo "Remaining essential scripts:"
ls -la $SCRIPTS_DIR | grep -v "total" | grep -v "^d" | grep -v "cleanup_scripts.sh"

exit 0
