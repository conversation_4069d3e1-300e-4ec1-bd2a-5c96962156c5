#!/usr/bin/env ruby
#
# test_youtube_api.rb - Test YouTube API integration for a specific user
#
# Description:
#   This script tests the YouTube API connection for a specific user by:
#   1. Checking if the user has YouTube connected
#   2. Verifying the user's YouTube credentials
#   3. Testing token refresh functionality
#   4. Fetching the user's YouTube channel information
#   5. Updating the user's record with YouTube channel data
#
# Usage:
#   bundle exec rails runner scripts/test_youtube_api.rb [user_id]
#
# Parameters:
#   user_id - Optional. The ID of the user to test. Defaults to 10.
#
# Requirements:
#   - Rails environment must be loaded
#   - Google API credentials must be configured
#   - The specified user must exist in the database
#

require_relative '../config/environment'
require 'google/apis/youtube_v3'

user_id = ARGV[0] || 10
user = User.find(user_id)

puts "Testing YouTube API for user #{user.id} (#{user.email})"
puts "YouTube connected? #{user.youtube_connected?}"

if user.youtube_connected?
  puts "YouTube credentials present: #{user.youtube_credentials.present?}"

  begin
    credentials = JSON.parse(user.youtube_credentials)
    puts "Access token: #{credentials['access_token'][0..10]}... (truncated)"
    puts "Refresh token present: #{credentials['refresh_token'].present?}"
    puts "Token expires at: #{Time.at(credentials['expires_at'])}"
    puts "Token type: #{credentials['token_type']}"
    puts "Scopes: #{credentials['scope']}"

    # Test getting a fresh token
    puts "\nTesting token refresh:"
    token = user.youtube_access_token
    puts "Fresh token obtained: #{token.present?}"

    # Test YouTube API
    puts "\nTesting YouTube API:"
    youtube_service = Google::Apis::YoutubeV3::YouTubeService.new
    youtube_service.authorization = token

    puts 'Fetching channel information...'
    channels = youtube_service.list_channels('snippet,statistics', mine: true)

    if channels.items.any?
      channel = channels.items.first
      puts "Found channel: #{channel.id} (#{channel.snippet.title})"
      puts "Subscriber count: #{channel.statistics.subscriber_count}"
      puts "Video count: #{channel.statistics.video_count}"

      # Try to update the user with this information
      puts "\nTrying to update user with channel information:"
      result = user.update(
        youtube_channel_id: channel.id,
        youtube_channel_name: channel.snippet.title,
        youtube_avatar_url: channel.snippet.thumbnails.default.url,
        youtube_subscriber_count: channel.statistics.subscriber_count,
        youtube_verified_at: Time.current
      )

      if result
        puts 'Successfully updated user with channel information'
      else
        puts "Failed to update user: #{user.errors.full_messages.join(', ')}"
      end
    else
      puts 'No channels found for this user'
    end
  rescue StandardError => e
    puts "Error: #{e.class.name} - #{e.message}"
    puts e.backtrace.join("\n")
  end
else
  puts 'User does not have YouTube connected'
end
