export interface RootObject {
  area:          Area;
  code:          string;
  currentSeason: Season;
  emblem:        string;
  id:            number;
  lastUpdated:   Date;
  name:          string;
  seasons:       Season[];
  type:          string;
 }
 
 export interface Area {
  code: string;
  flag: string;
  id:   number;
  name: string;
 }
 
 export interface Season {
  currentMatchday: number | null;
  endDate:         Date;
  id:              number;
  startDate:       Date;
  winner:          Winner | null;
 }
 
 export interface Winner {
  address:     string;
  clubColors:  ClubColors;
  crest:       string;
  founded:     number;
  id:          number;
  lastUpdated: Date;
  name:        string;
  shortName:   string;
  tla:         string;
  venue:       string;
  website:     string;
 }
 
 export enum ClubColors {
  BlueWhite = "Blue / White",
  RedWhite = "Red / White",
  RoyalBlueWhite = "Royal Blue / White",
  SkyBlueWhite = "Sky Blue / White",
  WhiteBlue = "White / Blue",
 }
 