export interface RootObject {
 errors:     any[];
 get:        string;
 paging:     Paging;
 parameters: Parameters;
 response:   Response[];
 results:    number;
}

export interface Paging {
 current: number;
 total:   number;
}

export interface Parameters {
 league: string;
 season: string;
}

export interface Response {
 fixture: Fixture;
 goals:   Goals;
 league:  League;
 score:   Score;
 teams:   Goals;
}

export interface Fixture {
 date:      Date;
 id:        number;
 periods:   Periods;
 referee:   null;
 status:    Status;
 timestamp: number;
 timezone:  Timezone;
 venue:     Venue;
}

export interface Periods {
 first:  null;
 second: null;
}

export interface Status {
 elapsed: null;
 extra:   null;
 long:    Long;
 short:   Short;
}

export enum Long {
 NotStarted = "Not Started",
 TimeToBeDefined = "Time to be defined",
}

export enum Short {
 NS = "NS",
 Tbd = "TBD",
}

export enum Timezone {
 UTC = "UTC",
}

export interface Venue {
 city: City;
 id:   number | null;
 name: VenueName;
}

export enum City {
 Borås = "Borås",
 Degerfors = "Degerfors",
 Göteborg = "Göteborg",
 Halmstad = "<PERSON>mstad",
 Malmö = "Malmö",
 Norrköping = "Norrköping",
 Solna = "Solna",
 Stockholm = "Stockholm",
 Sölvesborg = "Sölvesborg",
 Uppsala = "Uppsala",
 Värnamo = "Värnamo",
 Växjö = "Växjö",
}

export enum VenueName {
 BoråsArena = "Borås Arena",
 BravidaArena = "Bravida Arena",
 EledaStadion = "Eleda Stadion",
 Finnvedsvallen = "Finnvedsvallen",
 GamlaUllevi = "Gamla Ullevi",
 GrimstaIP = "Grimsta IP",
 PlatinumCarsArena = "PlatinumCars Arena",
 StoraValla = "Stora Valla",
 Strandvallen = "Strandvallen",
 StrawberryArena = "Strawberry Arena",
 StudenternasIP = "Studenternas IP",
 The3Arena = "3Arena",
 VismaArena = "Visma Arena",
 ÖrjansVall = "Örjans Vall",
}

export interface Goals {
 away: Away | null;
 home: Away | null;
}

export interface Away {
 id:     number;
 logo:   string;
 name:   AwayName;
 winner: null;
}

export enum AwayName {
 AIKStockholm = "AIK Stockholm",
 BKHacken = "BK Hacken",
 DegerforsIF = "Degerfors IF",
 DjurgardensIF = "Djurgardens IF",
 Gais = "Gais",
 Halmstad = "Halmstad",
 HammarbyFF = "Hammarby FF",
 IFBrommapojkarna = "IF Brommapojkarna",
 IFElfsborg = "IF Elfsborg",
 IFKGoteborg = "IFK Goteborg",
 IFKNorrkoping = "IFK Norrkoping",
 IFKVarnamo = "IFK Varnamo",
 MalmoFF = "Malmo FF",
 MjallbyAIF = "Mjallby AIF",
 OstersIF = "Osters IF",
 Sirius = "Sirius",
}

export interface League {
 country:   Country;
 flag:      string;
 id:        number;
 logo:      string;
 name:      LeagueName;
 round:     string;
 season:    number;
 standings: boolean;
}

export enum Country {
 Sweden = "Sweden",
}

export enum LeagueName {
 Allsvenskan = "Allsvenskan",
}

export interface Score {
 extratime: Goals;
 fulltime:  Goals;
 halftime:  Goals;
 penalty:   Goals;
}
