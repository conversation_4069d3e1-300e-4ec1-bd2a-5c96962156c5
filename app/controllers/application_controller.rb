# frozen_string_literal: true

class ApplicationController < ActionController::API
  # protect_from_forgery with: :exception
  include UserActivityTracker

  before_action :configure_permitted_parameters, if: :devise_controller?
  before_action :set_cors_headers

  private

  def authenticate_admin!
    return if current_devise_api_user&.admin?

    render json: {
      message: 'Only admin can create competitions',
      status: :unauthorized
    }, status: :unauthorized
  end

  def authorize_owner!
    return if current_devise_api_user && current_devise_api_user.id == resource_owner

    render json: { error: 'Unauthorized' }, status: :unauthorized
  end

  def resource_owner
    # Override this method in child controllers to return the owner of the resource
    # For example, if you're dealing with a League resource, you might do something like:
    # League.find(params[:id]).owner
    # This will vary depending on your application's resource ownership logic
  end

  def set_cors_headers
    headers['Access-Control-Allow-Origin'] = request.origin
    headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS, HEAD'
    headers['Access-Control-Allow-Headers'] = '*'
    headers['Access-Control-Allow-Credentials'] = 'true'

    return unless request.method == 'OPTIONS'

    render plain: '', content_type: 'text/plain'
  end

  protected

  def configure_permitted_parameters
    # devise_parameter_sanitizer.permit(:sign_up) { |u| u.permit(:username, :email, :password) }
    # devise_parameter_sanitizer.permit(:sign_up, keys: %i[email password username])
    devise_parameter_sanitizer.permit(:sign_up, keys: [:username])

    # devise_parameter_sanitizer.permit(:account_update) do |u|
    #   u.permit(
    #     :username, :email, :password, :current_password
    #   )
    # end
  end
end
