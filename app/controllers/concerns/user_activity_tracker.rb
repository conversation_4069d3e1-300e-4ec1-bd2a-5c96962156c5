# app/controllers/concerns/user_activity_tracker.rb
# This concern tracks user activity by updating the last_active_at timestamp
module UserActivityTracker
  extend ActiveSupport::Concern
  
  included do
    before_action :track_user_activity
  end
  
  private
  
  def track_user_activity
    return unless current_devise_api_user
    
    # Only update if last activity was more than 30 minutes ago
    if current_devise_api_user.last_active_at.nil? || 
       current_devise_api_user.last_active_at < 30.minutes.ago
      
      current_devise_api_user.update_column(:last_active_at, Time.current)
    end
  end
end
