module Api
  module V1
    class RoundPredictionsController < ApplicationController
      before_action :authenticate_devise_api_token!
      before_action :authorize_owner!, only: %i[update destroy]

      def index
        # Support fetching for multiple matchdays
        matchdays = params[:matchdays].present? ? parse_matchdays(params[:matchdays]) : nil

        # Build query with available parameters
        query_params = round_prediction_params2

        # Handle multiple matchdays if provided
        round_predictions = if matchdays.present?
                              current_devise_api_user.round_predictions
                                                     .where(query_params)
                                                     .where(matchday: matchdays)
                            else
                              # Fall back to existing logic for single matchday or no matchday
                              current_devise_api_user.round_predictions
                                                     .where(query_params)
                            end

        render json: {
          round_predictions:
          ActiveModelSerializers::SerializableResource.new(round_predictions,
                                                           each_serializer: RoundPredictionsSerializer),
          message: ['Round predictions found'],
          status: 200,
          type: 'success'
        }
      end

      def show
        round_prediction = current_devise_api_user.round_predictions.find_by(id: params[:id])

        if round_prediction
          render json: {
            round_predictions:
            ActiveModelSerializers::SerializableResource.new(round_prediction,
                                                             serializer: RoundPredictionSerializer),
            message: ['Round prediction found'],
            status: 200,
            type: 'success'
          }
        else
          render json: {
            message: ['Round prediction not found'],
            status: 404,
            type: 'error'
          }, status: :not_found
        end
      end

      def create # rubocop:disable Metrics/MethodLength,Metrics/PerceivedComplexity
        # Check for existing round prediction for this matchday, competition, and season
        existing_prediction = current_devise_api_user.round_predictions.find_by(
          matchday: round_prediction_params[:matchday],
          competition_id: round_prediction_params[:competition_id],
          season_id: round_prediction_params[:season_id]
        )

        if existing_prediction
          # Update the existing prediction instead of creating a new one
          match_prediction_params[:match_predictions].each do |match_prediction_param|
            match_prediction = existing_prediction.match_predictions
                                                  .find_or_initialize_by(match_id: match_prediction_param[:match_id])
            match_prediction.update(match_prediction_param)
          end

          if existing_prediction.save
            # Invalidate user statistics cache after updating predictions
            Api::V1::UserStatisticsController.invalidate_cache(current_devise_api_user.id)

            render json: {
              round_predictions:
              ActiveModelSerializers::SerializableResource.new(existing_prediction,
                                                               serializer: RoundPredictionSerializer),
              message: ['Existing round prediction updated'],
              status: 200,
              type: 'success'
            }
          else
            render json: existing_prediction.errors, status: 422
          end
        else
          # No existing prediction, create a new one as before
          round_prediction = current_devise_api_user.round_predictions.new(round_prediction_params)

          match_prediction_params[:match_predictions].each do |match_prediction_param|
            round_prediction.match_predictions.new(match_prediction_param)
          end

          if round_prediction.save
            # Invalidate user statistics cache after creating predictions
            Api::V1::UserStatisticsController.invalidate_cache(current_devise_api_user.id)

            render json: {
              round_predictions:
              ActiveModelSerializers::SerializableResource.new(round_prediction,
                                                               serializer: RoundPredictionSerializer),
              message: ['Round prediction created'],
              status: 200,
              type: 'success'
            }
          else
            render json: round_prediction.errors, status: 422
          end
        end
      end

      def edit; end

      private

      def round_prediction_params2
        params.permit(:season_id, :competition_id, :stage).to_h.compact_blank
      end

      def parse_matchdays(matchdays_param)
        case matchdays_param
        when String
          # Handle comma-separated list of matchdays: "1,2,3"
          matchdays_param.split(',').map(&:to_i)
        when Array
          # Handle array of matchdays: [1,2,3]
          matchdays_param.map(&:to_i)
        else
          # Fall back to single matchday
          [matchdays_param.to_i]
        end
      end

      def round_prediction_params
        params.require(:round_predictions).permit(:matchday, :season_id, :competition_id, :stage)
      end

      def match_prediction_params
        params.require(:round_predictions)
              .permit(match_predictions: %i[home_score away_score
                                            match_id
                                            season_id
                                            competition_id])
      end
    end
  end
end
