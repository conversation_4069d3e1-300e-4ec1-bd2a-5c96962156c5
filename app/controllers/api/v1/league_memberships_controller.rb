module Api
  module V1
    class LeagueMembershipsController < ApplicationController
      before_action :authenticate_devise_api_token!

      def create
        league = League.find(params[:league_id])

        if current_devise_api_user.memberships.count >= 5
          render json: { errors: 'MAXIMUM_LEAGUES_REACHED' },
                 status: :unprocessable_entity
          return
        end

        # Check if this is a subscriber-only league
        if league.subscriber_only_league? && !league.user_meets_subscription_requirements?(current_devise_api_user)
          render json: {
            error: 'This league is only available to YouTube subscribers of the channel',
            youtube_channel_id: league.youtube_channel_id
          }, status: :forbidden
          return
        end

        if league.open?
          handle_open_league_join(league)
        else
          render json: {
            error: 'This is a private league. Please submit a join request.'
          }, status: :unprocessable_entity
        end
      end

      def destroy
        league = League.find(params[:league_id])
        if league.users.include?(current_devise_api_user)
          league.users.delete(current_devise_api_user)
          render json: { message: 'You have left the league.' }, status: :ok
        else
          render json: { error: 'You are not a member of this league.' }, status: :unprocessable_entity
        end
      end

      private

      def handle_open_league_join(league)
        if league.users.exclude?(current_devise_api_user)
          # Create the membership
          membership = league.memberships.new(user: current_devise_api_user)

          # If this is a subscriber-only league, set subscription status
          if league.subscriber_only_league?
            membership.subscription_status = 'active'
            membership.subscription_verified_at = Time.current
          end

          if membership.save
            render json: {
              message: 'You have joined the league.',
              league: {
                id: league.id,
                name: league.name,
                youtube_league: league.youtube_league,
                subscriber_only: league.subscriber_only
              }
            }, status: :created
          else
            render json: { errors: membership.errors.full_messages },
                   status: :unprocessable_entity
          end
        else
          render json: {
            error: 'You are already a member of this league.'
          }, status: :unprocessable_entity
        end
      end
    end
  end
end
