# app/controllers/api/v1/league_join_requests_controller.rb
module Api
  module V1
    class LeagueJoinRequestsController < ApplicationController
      before_action :authenticate_devise_api_token!
      before_action :set_league, except: :user_requests
      before_action :authorize_owner, only: [:update]

      def index
        requests = @league.league_join_requests.pending.includes(:user)

        render json: {
          data: ActiveModelSerializers::SerializableResource.new(
            requests,
            each_serializer: LeagueJoinRequestSerializer
          ),
          message: ['Join requests found'],
          status: 200,
          type: 'success'
        }
      end

      def create
        return render_already_member if @league.users.include?(current_devise_api_user)
        return render_maximum_leagues if current_devise_api_user.memberships.count >= 5

        # Check if this is a subscriber-only league
        if @league.subscriber_only_league? && !@league.user_meets_subscription_requirements?(current_devise_api_user)
          render json: {
            error: 'This league is only available to YouTube subscribers of the channel',
            youtube_channel_id: @league.youtube_channel_id
          }, status: :forbidden
          return
        end

        request = @league.league_join_requests.new(user: current_devise_api_user)

        if request.save
          render json: { message: 'Join request submitted successfully' },
                 status: :created
        else
          render json: { errors: request.errors.full_messages },
                 status: :unprocessable_entity
        end
      end

      def update
        request = @league.league_join_requests.find(params[:id])

        if params[:status] == 'accepted'
          accept_request(request)
        elsif params[:status] == 'rejected'
          reject_request(request)
        else
          render json: { error: 'Invalid status' }, status: :unprocessable_entity
        end
      end

      def user_requests
        requests = LeagueJoinRequest.includes(league: :owner)
                                    .where(user: current_devise_api_user)

        render json: {
          data: ActiveModelSerializers::SerializableResource.new(
            requests,
            each_serializer: UserLeagueJoinRequestSerializer
          ),
          message: ['User join requests found'],
          status: 200,
          type: 'success'
        }
      end

      private

      def set_league
        @league = League.find(params[:league_id])
      end

      def authorize_owner
        return if current_devise_api_user.id == @league.owner_id

        render json: { error: 'Unauthorized' }, status: :unauthorized
      end

      def accept_request(request)
        ActiveRecord::Base.transaction do
          request.accepted!

          # Create the membership
          membership = @league.memberships.new(user: request.user)

          # If this is a subscriber-only league, set subscription status
          if @league.subscriber_only_league?
            # Verify subscription status again before accepting
            unless @league.user_meets_subscription_requirements?(request.user)
              raise ActiveRecord::RecordInvalid.new(membership), 'User no longer meets subscription requirements'
            end

            membership.subscription_status = 'active'
            membership.subscription_verified_at = Time.current

          end

          membership.save!
          render json: { message: 'Request accepted' }
        end
      rescue ActiveRecord::RecordInvalid => e
        render json: { error: e.message || 'Unable to accept request' },
               status: :unprocessable_entity
      end

      def reject_request(request)
        request.rejected!
        render json: { message: 'Request rejected' }
      end

      def render_already_member
        render json: { error: 'You are already a member of this league' },
               status: :unprocessable_entity
      end

      def render_maximum_leagues
        render json: { errors: 'MAXIMUM_LEAGUES_REACHED' },
               status: :unprocessable_entity
      end
    end
  end
end
