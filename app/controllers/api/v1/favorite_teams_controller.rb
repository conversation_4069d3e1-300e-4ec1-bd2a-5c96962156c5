# app/controllers/api/v1/favorite_teams_controller.rb

module Api
  module V1
    class FavoriteTeamsController < ApplicationController
      before_action :authenticate_devise_api_token!
      before_action :set_favorite_team, only: :destroy
      before_action :authorize_favorite_team, only: :destroy

      def create
        competition = Competition.find(params[:competition_id])
        team = Team.find(params[:team_id])

        favorite_team = current_devise_api_user.favorite_teams.find_or_initialize_by(competition:)
        favorite_team.team = team

        if favorite_team.save
          render json: { message: "#{team.name} has been set as your favorite team for #{competition.name}" },
                 status: :created
        else
          render json: { errors: favorite_team.errors.full_messages },
                 status: :unprocessable_entity
        end
      end

      def destroy
        if @favorite_team.destroy
          render json: { message: 'Favorite team removed successfully' },
                 status: :ok
        else
          render json: { errors: @favorite_team.errors.full_messages },
                 status: :unprocessable_entity
        end
      end

      def index
        favorite_teams = current_devise_api_user.favorite_teams.includes(:team, :competition)
        render json: favorite_teams, each_serializer: Api::V1::FavoriteTeamSerializer
      end

      def show
        competition = Competition.find(params[:competition_id])
        favorite_team = current_devise_api_user.favorite_teams.find_by(competition:)

        if favorite_team
          render json: favorite_team, serializer: Api::V1::FavoriteTeamSerializer
        else
          render json: { favorite_team: nil }, status: :ok
        end
      end

      private

      def set_favorite_team
        @favorite_team = FavoriteTeam.find_by(id: params[:id])
        return unless @favorite_team.nil?

        render json: { error: 'No favorite team found with this ID' },
               status: :not_found
      end

      def authorize_favorite_team
        return if @favorite_team.user_id == current_devise_api_user.id

        render json: { error: 'You are not authorized to perform this action' },
               status: :unauthorized
      end
    end
  end
end
