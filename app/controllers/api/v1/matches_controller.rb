module Api
  module V1
    # TODO: Fix all rubocop disables
    class MatchesController < ApplicationController # rubocop:disable Metrics/ClassLength
      # skip_before_action :verify_authenticity_token, raise: false
      before_action :authenticate_devise_api_token!
      before_action :authenticate_admin!, only: :create
      before_action :find_season, only: :create

      def index
        set_competition_season_stage_and_matchday
        cache_key = "matches_#{@competition_code}_#{@match_day}"
        cache_key += "_#{@stage}" if @stage.present?

        # First level: Check if we have a cached response already
        cached_response = Rails.cache.read(cache_key)

        if cached_response
          # We have a cached response, use it with the appropriate HTTP status
          return render json: cached_response, status: cached_response[:status]
        end

        # Second level: Cache miss, so fetch matches from database
        @matches = @season.matches
                          .where(matchday: @match_day)
                          .merge(@stage.present? ? Match.where(stage: @stage) : Match.all)
                          .sort_by(&:utc_date)

        # Determine the appropriate cache expiration time based on match data
        expiration_time = cache_expiration_time

        # Create the response and store it in cache with the appropriate expiration
        response_data = if @matches.nil? || @matches.empty?
                          {
                            matches: [],
                            message: ['No matches found'],
                            status: 404,
                            type: 'error'
                          }
                        else
                          {
                            matches: ActiveModelSerializers::SerializableResource.new(@matches,
                                                                                      each_serializer: MatchSerializer),
                            message: ['Matches found'],
                            status: 200,
                            type: 'success'
                          }
                        end

        # Store in cache with the determined expiration time
        Rails.cache.write(cache_key, response_data, expires_in: expiration_time)

        # Return the response with the correct HTTP status code
        render json: response_data, status: response_data[:status]
      end

      def show
        match = Match.find_by(id: params[:id])

        unless match
          return render json: {
            data: nil,
            message: ['Match not found'],
            status: 404,
            type: 'error'
          }, status: :not_found
        end

        render json: {
          data: ActiveModelSerializers::SerializableResource.new(match, serializer: MatchSerializer),
          message: ['Match found'],
          status: 200,
          type: 'success'
        }
      end

      private

      def set_home_team
        home_team = Team.find_by(name: params[:homeTeamName])
        @match.home_team_id = home_team.id
        @season.teams << home_team unless @season.teams.include? home_team
      end

      def set_away_team
        away_team = Team.find_by(name: params[:awayTeamName])
        @match.away_team_id = away_team.id
        @season.teams << away_team unless @season.teams.include? away_team
      end

      def match_params
        params.slice(:match).require(:match).permit(:matchday, :status, :referee, :date)
      end

      def score_params
        params.slice(:score).require(:score).permit(
          :duration,
          :fullTimeAway,
          :halfTimeAway,
          :fullTimeHome,
          :halfTimeHome,
          :winner
        )
      end

      def find_match
        Match.find_by(home_team: Team.find_by(name: params[:homeTeamName]),
                      away_team: Team.find_by(name: params[:awayTeamName]),
                      season_id: params[:seasonId])
      end

      def set_competition_season_stage_and_matchday
        @competition_code = params[:competitionCode]
        @match_day = params[:matchday]
        @stage = params[:stage]
        find_season
      end

      def find_season
        @season = Season.find_by(id: params[:seasonId])
      end

      def cache_expiration_time
        # Simplify logic into clear cases
        return 1.minute if live_matches_exist?
        return 5.minutes if matches_starting_soon?(within_hours: 3)
        return 30.minutes if matches_today?
        return 3.hours if matches_tomorrow?

        1.day
      end

      def live_matches_exist?
        @matches.any? { |match| %w[LIVE IN_PLAY PAUSED].include?(match.status) }
      end

      def matches_starting_soon?(within_hours:)
        cutoff_time = Time.current + within_hours.hours
        @matches.any? { |match| match.utc_date <= cutoff_time && !match.finished? }
      end

      def matches_today?
        today = Time.current.beginning_of_day..Time.current.end_of_day
        @matches.any? { |match| today.cover?(match.utc_date) }
      end

      def matches_tomorrow?
        tomorrow = (Time.current + 1.day).beginning_of_day..(Time.current + 1.day).end_of_day
        @matches.any? { |match| tomorrow.cover?(match.utc_date) }
      end
    end
  end
end
