module Api
  module V1
    class TeamsController < ApplicationController
      before_action :authenticate_devise_api_token!, only: [:standings]
      before_action :set_team, only: %i[show standings]

      def index
        teams = Team.all

        render json: teams
      end

      def show
        if @team
          render json: @team
        else
          render json: { errors: ['Team not found'] }, status: :not_found
        end
      end

      def create
        area = Area.find_by(id: team_params[:area_id])
        return render json: { errors: ['Area not found'] }, status: :not_found unless area

        team = Team.new(team_params.merge(area:))
        if team.save
          render json: team, status: :created
        else
          render json: { errors: team.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def standings
        return render json: { errors: ['Team not found'] }, status: :not_found unless @team

        # Check if a specific season was requested
        season_id = params[:season_id].presence
        competition_id = params[:competition_id].presence

        # Get all users who have this team as their favorite
        fans = @team.fans

        if fans.empty?
          render json: {
            team: {
              id: @team.id,
              name: @team.name,
              short_name: @team.short_name,
              tla: @team.tla,
              crest_public_id: @team.crest_public_id,
              club_colors: @team.get_club_colors('home')
            },
            standings: [],
            message: ['No fans found for this team'],
            status: 200,
            type: 'success'
          }
          return
        end

        # Calculate standings for these users
        user_standings = calculate_team_fans_standings(fans, season_id, competition_id)

        # Get season info if provided
        season = season_id.present? ? Season.find(season_id) : nil
        competition = competition_id.present? ? Competition.find(competition_id) : nil

        render json: {
          team: {
            id: @team.id,
            name: @team.name,
            short_name: @team.short_name,
            tla: @team.tla,
            crest_public_id: @team.crest_public_id,
            club_colors: @team.get_club_colors('home')
          },
          competition: if competition
                         {
                           id: competition.id,
                           name: competition.name,
                           emblem_public_id: competition.emblem_public_id
                         }
                       else
                         nil
                       end,
          season: if season
                    {
                      id: season.id,
                      start_date: season.start_date,
                      end_date: season.end_date
                    }
                  else
                    nil
                  end,
          standings: user_standings,
          message: ['Team fans standings calculated'],
          status: 200,
          type: 'success'
        }
      end

      private

      def set_team
        @team = Team.find_by(id: params[:id])
      end

      def team_params
        # Allow nested club_colors parameters
        params.require(:team).permit(
          :name, :short_name, :tla, :venue, :crest_public_id, :external_service_id, :source, :area_id,
          club_colors: {
            home: %i[primary secondary tertiary],
            away: %i[primary secondary tertiary]
          }
        )
      end

      def calculate_team_fans_standings(fans, season_id = nil, competition_id = nil)
        # Ensure we have unique fans by user_id to prevent duplicates
        fans = fans.distinct

        # Base query for round predictions
        query = RoundPrediction.where(user_id: fans.pluck(:id))

        # Filter by season if specified
        query = query.where(season_id:) if season_id.present?

        # Filter by competition if specified
        query = query.where(competition_id:) if competition_id.present?

        # Group round predictions by user
        round_predictions_by_user = query.includes(:match_predictions).group_by(&:user_id)

        # If filtering by season or competition, only include fans who have predictions for that season/competition
        if season_id.present? || competition_id.present?
          # Get the user IDs who have predictions for the specified filters
          user_ids_with_predictions = round_predictions_by_user.keys
          # Only include these users in the standings
          fans = fans.where(id: user_ids_with_predictions)
        end

        # Calculate stats for each user
        user_stats = fans.map do |user|
          user_rps = round_predictions_by_user[user.id] || []

          # Calculate points for each round prediction
          user_rps.each(&:calculate_match_prediction_points)

          # Sum up the stats
          total_points = user_rps.sum(&:total_points)
          total_perfect = user_rps.sum(&:total_perfect)
          total_correct = user_rps.sum(&:total_correct)
          total_incorrect = user_rps.sum { |rp| rp.zero_points_count }

          {
            username: user.username,
            points: total_points,
            correct: total_correct,
            incorrect: total_incorrect,
            perfect: total_perfect
          }
        end

        # Sort by points in descending order and add position
        user_stats.sort_by { |stats| -stats[:points] }
                  .each_with_index.map do |stats, index|
          stats.merge(
            position: index + 1,
            top_message: get_top_position_message(index + 1, stats[:username])
          )
        end
      end

      def get_top_position_message(position, username)
        case position
        when 1
          "🏆 #{username} is leading among fans! Congratulations!"
        when 2
          "🥈 #{username} is in second place. Keep pushing!"
        when 3
          "🥉 #{username} is in third place. Great job!"
        else
          nil
        end
      end
    end
  end
end
