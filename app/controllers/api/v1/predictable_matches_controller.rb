module Api
  module V1
    class PredictableMatchesController < ApplicationController
      before_action :authenticate_devise_api_token!
      before_action :set_current_user, only: :refresh

      def index
        @predictable_matches = fetch_predictable_matches
        return render_season_over_response if season_over?

        render_matches_with_predictions
      end

      # This method is kept for admin purposes but doesn't directly call API services
      # Instead it schedules a job to perform the update
      def refresh
        competition_code = params[:competitionCode]

        # Only allow admins to trigger manual refreshes
        unless current_user&.admin?
          return render json: {
            message: ['Not authorized to refresh matches manually'],
            status: 403,
            type: 'error'
          }, status: :forbidden
        end

        # Schedule an immediate job for this
        TodaysMatchesUpdateJob.perform_later(competition_code)

        render json: {
          message: ['Match refresh scheduled successfully'],
          status: 200,
          type: 'success'
        }
      end

      private

      def set_current_user
        @current_user = current_devise_api_user
      end

      attr_reader :current_user

      def fetch_predictable_matches
        PredictableMatchesService.fetch_matches(
          competition_code: params[:competitionCode],
          season_id: params[:seasonId]
        ) || []
      end

      def season_over?
        @predictable_matches.instance_variable_defined?(:@season_over) &&
          @predictable_matches.instance_variable_get(:@season_over)
      end

      def render_season_over_response
        competition = Competition.find_by(code: params[:competitionCode])
        season_id = params[:seasonId].present? ? params[:seasonId] : competition&.current_season&.id

        render json: {
          matches: [],
          predictions: [],
          message: ['Season is over. No matches available for prediction.'],
          season_over: true,
          competition_id: competition&.id,
          season_id:,
          status: 200,
          type: 'success'
        }
      end

      def fetch_user_predictions
        return [] unless @predictable_matches.any?

        current_devise_api_user.match_predictions
                               .where(match_id: @predictable_matches.map(&:id))
                               .includes(:match)
      end

      def render_matches_with_predictions
        user_predictions = fetch_user_predictions
        competition = Competition.find_by(code: params[:competitionCode])
        season_id = params[:seasonId].present? ? params[:seasonId] : competition&.current_season&.id

        render json: {
          matches: ActiveModelSerializers::SerializableResource.new(@predictable_matches,
                                                                    each_serializer: MatchSerializer),
          predictions: ActiveModelSerializers::SerializableResource.new(user_predictions,
                                                                        each_serializer: MatchPredictionSerializer),
          message: @predictable_matches.any? ? ['Predictable matches found'] : ['No predictable matches found'],
          competition_id: competition&.id,
          season_id:,
          status: 200,
          type: 'success'
        }
      end
    end
  end
end
