# Competition controller
module Api
  module V1
    # TODO: Fix all rubocop disables
    class CompetitionsController < ApplicationController
      # skip_before_action :verify_authenticity_token, raise: false
      before_action :authenticate_devise_api_token!, except: %i[get_logos index show teams]
      before_action :find_area, only: :create
      before_action :set_competition, only: %i[show favorite_team teams standings]

      def index
        competitions = Competition.includes(:area, :current_season)
                                  .order(created_at: :desc)

        render json: {
          competitions: ActiveModelSerializers::SerializableResource.new(competitions,
                                                                         each_serializer: CompetitionSerializer),
          message: ['Competitions found'],
          status: 200,
          type: 'success'
        }
      end

      def teams
        teams = @competition.current_season.teams

        render json: {
          data: {
            teams: ActiveModelSerializers::SerializableResource.new(teams,
                                                                    each_serializer: Api::V1::CompetitionTeamSerializer),
            competition: ActiveModelSerializers::SerializableResource.new(@competition,
                                                                          serializer: Api::V1::CompetitionBasicSerializer)
          },
          message: ['Teams found'],
          status: 200,
          type: 'success'
        }
      end

      def get_logos # rubocop:disable Naming/AccessorMethodName
        competition_codes = params[:codes]

        if competition_codes.blank?
          render json: { errors: ['Competition code is required'], status: 400 }, status: 400
          return
        end

        competitions = Competition.where(code: competition_codes)

        render json: {
          data: ActiveModelSerializers::SerializableResource.new(competitions,
                                                                 each_serializer: CompetitionGetLogosSerializer),
          message: ['Logos found'],
          status: 200,
          type: 'success'
        }
      end

      def show
        if @competition
          render json: {
            data: ActiveModelSerializers::SerializableResource.new(@competition, serializer: CompetitionSerializer),
            message: ['Competition found'],
            status: 200,
            type: 'success'
          }
        else
          render json: { errors: ['Competition not found'], status: 404 }, status: :not_found
        end
      end

      def favorite_team
        favorite = current_devise_api_user.favorite_teams.find_by(competition: @competition)
        if favorite
          render json: favorite, serializer: Api::V1::FavoriteTeamSerializer
        else
          render json: { favorite_team: nil }, status: :ok
        end
      end

      def standings
        # Check if a specific season was requested
        season_id = params[:season_id].presence || @competition.current_season&.id

        unless season_id
          render json: {
            errors: ['No season found for this competition'],
            status: 404
          }, status: :not_found
          return
        end

        season = Season.find(season_id)

        # Get all users who have made predictions for this competition and season
        user_standings = calculate_competition_standings(season_id)

        render json: {
          competition: {
            id: @competition.id,
            name: @competition.name,
            season: {
              id: season.id,
              start_date: season.start_date,
              end_date: season.end_date
            },
            standings: user_standings
          },
          message: ['Competition standings calculated'],
          status: 200,
          type: 'success'
        }
      end

      private

      def competition_params
        competition_data = params.slice(:competition).require(:competition).permit(%i[code name emblem cid gender])
        competition_data.merge!(competition_type: 'LEAGUE') unless competition_data[:competition_type].present?
        competition_data[:emblem_public_id] = competition_data.delete(:emblem) if competition_data[:emblem]
        competition_data
      end

      def area_params
        params.slice(:area).require(:area).permit(%i[code name id])
      end

      def current_season_params
        season_data = params.slice(:currentSeason).require(:currentSeason).permit(:startDate, :endDate,
                                                                                  :currentMatchday)
        {
          start_date: season_data[:startDate],
          end_date: season_data[:endDate],
          current_matchday: season_data[:currentMatchday]
        }
      end

      def find_area
        @area = Area.find_or_create_by(area_params)
        logger.debug @area
      end

      def set_competition
        @competition = Competition.find_by(id: params[:id])
      end

      def calculate_competition_standings(season_id)
        # Find all users who have made predictions for this competition and season
        users_with_predictions = User.joins(:round_predictions)
                                     .where(round_predictions: { competition_id: @competition.id, season_id: })
                                     .distinct

        # Calculate stats for each user
        user_stats = users_with_predictions.map do |user|
          # Get all round predictions for this user, competition, and season
          round_predictions = user.round_predictions
                                  .where(competition_id: @competition.id, season_id:)

          # Calculate total points, correct, incorrect, and perfect predictions
          total_points = 0
          total_correct = 0
          total_incorrect = 0
          total_perfect = 0

          round_predictions.each do |rp|
            # Make sure points are calculated
            rp.calculate_match_prediction_points

            # Add up the stats
            total_points += rp.total_points
            total_perfect += rp.total_perfect
            total_correct += rp.total_correct
            total_incorrect += rp.zero_points_count
          end

          {
            username: user.username,
            points: total_points,
            correct: total_correct,
            incorrect: total_incorrect,
            perfect: total_perfect
          }
        end

        # Sort by points in descending order and add position
        user_stats.sort_by { |stats| -stats[:points] }
                  .each_with_index.map do |stats, index|
          stats.merge(
            position: index + 1,
            top_message: get_top_position_message(index + 1, stats[:username])
          )
        end
      end

      def get_top_position_message(position, username)
        case position
        when 1
          "🏆 #{username} is leading the competition! Congratulations!"
        when 2
          "🥈 #{username} is in second place. Keep pushing!"
        when 3
          "🥉 #{username} is in third place. Great job!"
        else
          nil
        end
      end
    end
  end
end
