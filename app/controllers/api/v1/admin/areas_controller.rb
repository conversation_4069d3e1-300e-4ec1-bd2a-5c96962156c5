# app/controllers/api/v1/areas_controller.rb
module Api
  module V1
    module Admin
      class AreasController < ApplicationController
        before_action :authenticate_devise_api_token!
        before_action :authenticate_admin!

        def index
          areas = Area.all
          render json: {
            data: ActiveModelSerializers::SerializableResource.new(areas,
                                                                   each_serializer: AreaSerializer),
            message: ['Areas found'],
            status: 200,
            type: 'success'
          }
        end

        def show
          area = Area.find_by(id: params[:id])
          if area
            render json: {
              data: ActiveModelSerializers::SerializableResource.new(area,
                                                                     serializer: AreaSerializer),
              message: ['Area found'],
              status: 200,
              type: 'success'
            }
          else
            render json: { errors: ['Area not found'], status: 404 }, status: 404
          end
        end

        def update
          area = Area.find_by(id: params[:id])
          if area.update(area_params)
            render json: {
              data: ActiveModelSerializers::SerializableResource.new(area,
                                                                     serializer: AreaSerializer),
              message: ['Area updated successfully'],
              status: 200,
              type: 'success'
            }
          else
            render json: { errors: area.errors.full_messages, status: 422 }, status: 422
          end
        end
      end
    end
  end
end
