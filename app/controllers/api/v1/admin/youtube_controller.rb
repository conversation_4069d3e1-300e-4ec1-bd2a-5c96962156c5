# app/controllers/api/v1/admin/youtube_controller.rb
module Api
  module V1
    module Admin
      class YoutubeController < ApplicationController
        before_action :authenticate_devise_api_token!
        before_action :authenticate_admin!
        # Get current YouTube API quota usage
        def quota_usage
          render json: {
            current_usage: YoutubeQuotaService.get_current_usage,
            daily_limit: YoutubeQuotaService::DAILY_QUOTA_LIMIT,
            remaining: YoutubeQuotaService.get_remaining_quota,
            percentage_used: (YoutubeQuotaService.get_current_usage.to_f /
                             YoutubeQuotaService::DAILY_QUOTA_LIMIT * 100).round(2)
          }
        end
      end
    end
  end
end
