# app/controllers/api/v1/ksi_controller.rb
module Api
  module V1
    module Admin
      class KsiController < ApplicationController
        before_action :initialize_soap_service

        # GET /api/v1/ksi/team_matches/:team_id
        def team_matches
          team_id = params[:team_id]
          options = {
            field_number: params[:field_number],
            group_number: params[:group_number],
            gender: params[:gender],
            date_from: params[:date_from],
            date_to: params[:date_to]
          }.compact

          result = @soap_service.team_matches(team_id, options)
          render json: result
        rescue StandardError => e
          render json: { error: e.message }, status: :unprocessable_entity
        end

        # GET /api/v1/ksi/tournament_standings/:tournament_id
        def tournament_standings
          tournament_id = params[:tournament_id]
          result = @soap_service.tournament_standings(tournament_id)
          render json: result
        rescue StandardError => e
          render json: { error: e.message }, status: :unprocessable_entity
        end

        # GET /api/v1/ksi/tournament_matches/:tournament_id
        def tournament_matches
          tournament_id = params[:tournament_id]
          result = @soap_service.tournament_matches(tournament_id)
          render json: result
        rescue StandardError => e
          render json: { error: e.message }, status: :unprocessable_entity
        end

        # GET /api/v1/ksi/top_scorers/:tournament_id
        def top_scorers
          tournament_id = params[:tournament_id]
          result = @soap_service.top_scorers(tournament_id)
          render json: result
        rescue StandardError => e
          render json: { error: e.message }, status: :unprocessable_entity
        end

        # GET /api/v1/ksi/groups
        def groups
          result = @soap_service.groups
          render json: result
        rescue StandardError => e
          render json: { error: e.message }, status: :unprocessable_entity
        end

        # GET /api/v1/ksi/yellow_cards/:tournament_id
        def yellow_cards
          tournament_id = params[:tournament_id]
          result = @soap_service.yellow_cards(tournament_id)
          render json: result
        rescue StandardError => e
          render json: { error: e.message }, status: :unprocessable_entity
        end

        # GET /api/v1/ksi/red_cards/:tournament_id
        def red_cards
          tournament_id = params[:tournament_id]
          result = @soap_service.red_cards(tournament_id)
          render json: result
        rescue StandardError => e
          render json: { error: e.message }, status: :unprocessable_entity
        end

        # GET /api/v1/ksi/match_events/:match_id
        def match_events
          match_id = params[:match_id]
          result = @soap_service.match_events(match_id)
          render json: result
        rescue StandardError => e
          render json: { error: e.message }, status: :unprocessable_entity
        end

        # GET /api/v1/ksi/match_players/:match_id
        def match_players
          match_id = params[:match_id]
          result = @soap_service.match_players(match_id)
          render json: result
        rescue StandardError => e
          render json: { error: e.message }, status: :unprocessable_entity
        end

        # GET /api/v1/ksi/match_referees/:match_id
        def match_referees
          match_id = params[:match_id]
          result = @soap_service.match_referees(match_id)
          render json: result
        rescue StandardError => e
          render json: { error: e.message }, status: :unprocessable_entity
        end

        # GET /api/v1/ksi/match_changes
        def match_changes
          from_date = params[:from_date]
          to_date = params[:to_date]
          result = @soap_service.match_changes(from_date, to_date)
          render json: result
        rescue StandardError => e
          render json: { error: e.message }, status: :unprocessable_entity
        end

        # GET /api/v1/ksi/score_changes
        def score_changes
          from_date = params[:from_date]
          to_date = params[:to_date]
          result = @soap_service.score_changes(from_date, to_date)
          render json: result
        rescue StandardError => e
          render json: { error: e.message }, status: :unprocessable_entity
        end

        # GET /api/v1/ksi/matches
        def matches
          match_numbers = params[:match_numbers].split(',')
          result = @soap_service.matches(match_numbers)
          render json: result
        rescue StandardError => e
          render json: { error: e.message }, status: :unprocessable_entity
        end

        private

        def initialize_soap_service
          @soap_service = KsiSoapService.new
        end
      end
    end
  end
end
