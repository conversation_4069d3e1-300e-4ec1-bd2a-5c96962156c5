# app/controllers/api/v1/competition_admin_controller.rb
module Api
  module V1
    module Admin
      class CompetitionsController < ApplicationController # rubocop:disable Metrics/ClassLength
        before_action :authenticate_devise_api_token!
        before_action :authenticate_admin!
        before_action :set_competition,
                      only: %i[show update update_teams update_matches current_season_teams set_winner
                               create_new_season new_season_requirements]

        def index
          competitions = Competition.includes(:area, :current_season)
          render json: {
            data: ActiveModelSerializers::SerializableResource.new(competitions,
                                                                   each_serializer: CompetitionAdminDataSerializer),
            message: ['Competitions found'],
            status: 200,
            type: 'success'
          }
        end

        def update
          if @competition.update(competition_params)
            render json: {
              data: ActiveModelSerializers::SerializableResource.new(@competition,
                                                                     serializer: CompetitionAdminDataSerializer),
              message: ['Competition updated successfully'],
              status: 200,
              type: 'success'
            }
          else
            render json: { errors: @competition.errors.full_messages, status: 422 }, status: 422
          end
        rescue StandardError => e
          Rails.logger.error "Error updating competition: #{e.message}"
          render json: { errors: [e.message], status: 422 }, status: 422
        end

        def show
          if @competition
            render json: {
              data: ActiveModelSerializers::SerializableResource.new(@competition,
                                                                     serializer: CompetitionAdminDataSerializer),
              message: ['Competition found'],
              status: 200,
              type: 'success'
            }
          elsif %w[PL CL SV BDkk BDkvk].include?(params[:id])
            render json: { data: 'Competition is not yet setup', message: 'Competition is not yet setup', status: 200,
                           type: 'warning' }
          else
            render json: { errors: ['Competition not found'], status: 404 }, status: 404
          end
        end

        def initialize_or_update
          competition_code = params[:code]
          if competition_code.blank?
            render json: { errors: ['Competition code is required'], status: 400 }, status: 400 and return
          end

          begin
            competition =
              if %w[PL CL].include?(competition_code)
                FootballDataService.new.import_competition(competition_code)
              elsif competition_code == 'SV'
                ApiFootballService.new.import_competition(competition_code)
              elsif competition_code == 'BDkk'
                KsiSoapService.new.setup_service(competition_code, 'male', 49_315)
              else
                render json: { errors: ['Unsupported competition code'], status: 400 }, status: 400 and return
              end

            render json: {
              data: ActiveModelSerializers::SerializableResource.new(competition, serializer: CompetitionSerializer),
              message: ['Competition initialized or updated successfully'],
              status: 200,
              type: 'success'
            }
          rescue FootballDataService::ApiError => e
            Rails.logger.error "API Error updating competition: #{e.message}"
            render json: { errors: ["API Error: #{e.message}"], status: 422 }, status: 422
          rescue NameError => e
            Rails.logger.error "Service not found: #{e.message}"
            render json: { errors: ["Service error: #{e.message}"], status: 500 }, status: 500
          rescue StandardError => e
            Rails.logger.error "Unexpected error updating competition: #{e.class} - #{e.message}"
            Rails.logger.error e.backtrace.join("\n")
            render json: { errors: ["Server error: #{e.message}"], status: 500 }, status: 500
          end
        end

        def update_teams
          competition_code = @competition.code
          if %w[PL CL].include?(competition_code)
            FootballDataService.new.import_teams_for_competition(@competition, competition_code)
          elsif competition_code == 'SV'
            ApiFootballService.new.import_teams_for_competition(@competition)
          else
            render json: { errors: ['Unsupported competition code'], status: 400 }, status: 400 and return
          end

          render json: {
            data: ActiveModelSerializers::SerializableResource.new(@competition.reload,
                                                                   serializer: CompetitionAdminDataSerializer),
            message: ['Teams updated successfully'],
            status: 200,
            type: 'success'
          }
        rescue StandardError => e
          Rails.logger.error "Error updating teams: #{e.message}"
          render json: { errors: [e.message], status: 422 }, status: 422
        end

        def update_matches
          competition_code = @competition.code
          if %w[PL CL].include?(competition_code)
            FootballDataService.new.update_matches(@competition, @competition.current_season)
          elsif competition_code == 'SV'
            ApiFootballService.new.update_matches(@competition)
          elsif competition_code == 'BDkk'
            KsiSoapService.new.update_matches(@competition.current_season.external_service_id)
          else
            render json: { errors: ['Unsupported competition code'], status: 400 }, status: 400 and return
          end

          render json: {
            data: ActiveModelSerializers::SerializableResource.new(@competition.reload,
                                                                   serializer: CompetitionAdminDataSerializer),
            message: ['Matches updated successfully'],
            status: 200,
            type: 'success'
          }
        rescue StandardError => e
          Rails.logger.error "Error updating matches: #{e.message}"
          render json: { errors: [e.message], status: 422 }, status: 422
        end

        def current_season_teams
          if @competition&.current_season
            teams = @competition.current_season.teams
            render json: {
              data: ActiveModelSerializers::SerializableResource.new(teams, each_serializer: TeamSerializer),
              message: ['Teams found'],
              status: 200,
              type: 'success'
            }
          else
            render json: { errors: ['Current season not found'], status: 404 }, status: 404
          end
        end

        def set_winner
          return render json: { errors: ['Competition not found'], status: 404 }, status: 404 unless @competition

          team = Team.find_by(id: params[:team_id])
          return render json: { errors: ['Team not found'], status: 404 }, status: 404 unless team

          # Verify the team belongs to the competition's current season
          unless @competition.current_season&.teams&.include?(team)
            return render json: { errors: ['Team is not part of the current season'], status: 400 }, status: 400
          end

          @competition.current_winner_id = team.id

          if @competition.save
            render json: {
              data: ActiveModelSerializers::SerializableResource.new(@competition,
                                                                     serializer: CompetitionAdminDataSerializer),
              message: ['Competition winner updated successfully'],
              status: 200,
              type: 'success'
            }
          else
            render json: { errors: @competition.errors.full_messages, status: 422 }, status: 422
          end
        end

        def new_season_requirements
          return render json: { errors: ['Competition not found'], status: 404 }, status: 404 unless @competition

          competition_code = @competition.code
          requirements = {}

          if %w[PL CL].include?(competition_code)
            # FootballData competitions don't need any additional parameters
            requirements = {
              needs_season_external_service_id: false,
              season_id_format: nil
            }
          elsif competition_code == 'SV'
            # ApiFootball competitions need a season external service ID in year format
            requirements = {
              needs_season_external_service_id: true,
              season_id_format: 'year',
              season_id_example: (Date.today.year + 1).to_s
            }

            # Check if competition has external_service_id
            requirements[:missing_competition_external_service_id] = true if @competition.external_service_id.blank?
          elsif competition_code == 'BDkk'
            # KSI competitions need a season external service ID in numeric ID format
            requirements = {
              needs_season_external_service_id: true,
              season_id_format: 'numeric_id',
              season_id_example: '50000'
            }
          else
            return render json: { errors: ['Unsupported competition code'], status: 400 }, status: 400
          end

          render json: {
            competition: {
              id: @competition.id,
              name: @competition.name,
              code: @competition.code,
              source: @competition.source,
              external_service_id: @competition.external_service_id
            },
            requirements:,
            status: 200
          }
        end

        def create_new_season
          return render json: { errors: ['Competition not found'], status: 404 }, status: 404 unless @competition

          begin
            competition_code = @competition.code

            if %w[PL CL].include?(competition_code)
              FootballDataService.new.create_new_season_for_competition(@competition)
            elsif competition_code == 'SV'
              # For ApiFootball service, we need the season external service ID and competition external_service_id
              season_external_service_id = params[:season_external_service_id]

              if season_external_service_id.blank?
                return render json: {
                  errors: ['Season external service ID is required for ApiFootball competitions'],
                  status: 400
                }, status: 400
              end

              if @competition.external_service_id.blank?
                return render json: {
                  errors: ['Competition external service ID is missing. Please update the competition first.'],
                  status: 400
                }, status: 400
              end

              ApiFootballService.new.create_new_season_for_competition(@competition, season_external_service_id)
            elsif competition_code == 'BDkk'
              # For KSI SOAP service, we need the season external service ID
              season_external_service_id = params[:season_external_service_id]

              if season_external_service_id.blank?
                return render json: {
                  errors: ['Season external service ID is required for KSI competitions'],
                  status: 400
                }, status: 400
              end

              KsiSoapService.new.create_new_season_for_competition(@competition, season_external_service_id)
            else
              return render json: { errors: ['Unsupported competition code'], status: 400 }, status: 400
            end

            render json: {
              data: ActiveModelSerializers::SerializableResource.new(@competition.reload,
                                                                     serializer: CompetitionAdminDataSerializer),
              message: ["New season created successfully for #{@competition.name}"],
              status: 200,
              type: 'success'
            }
          rescue StandardError => e
            Rails.logger.error "Error creating new season: #{e.class} - #{e.message}"
            Rails.logger.error e.backtrace.join("\n")
            render json: { errors: ["Failed to create new season: #{e.message}"], status: 422 }, status: 422
          end
        end

        private

        def set_competition
          @competition = if params[:id] =~ /^\d+$/
                           Competition.find_by(id: params[:id])
                         else
                           Competition.find_by(code: params[:id])
                         end
        end

        def competition_params
          params.require(:competition).permit(
            :name, :code, :competition_type, :gender, :emblem_public_id,
            colors: %i[primary secondary tertiary]
          )
        end
      end
    end
  end
end
