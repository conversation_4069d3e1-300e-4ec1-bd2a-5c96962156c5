module Api
  module V1
    module Admin
      class SeasonsController < ApplicationController
        before_action :authenticate_devise_api_token!
        before_action :authenticate_admin!

        def index
          seasons = Season.all.includes(:competition, :teams)
          render json: {
            data: ActiveModelSerializers::SerializableResource.new(seasons,
                                                                   each_serializer: SeasonSerializer),
            message: ['Seasons found'],
            status: 200,
            type: 'success'
          }
        end

        def show
          season = Season.find_by(id: params[:id])
          if season
            render json: {
              data: ActiveModelSerializers::SerializableResource.new(season,
                                                                     serializer: SeasonSerializer),
              message: ['Season found'],
              status: 200,
              type: 'success'
            }
          else
            render json: { errors: ['Season not found'], status: 404 }, status: 404
          end
        end

        def update
          season = Season.find_by(id: params[:id])
          if season.update(season_params)
            render json: {
              data: ActiveModelSerializers::SerializableResource.new(season,
                                                                     serializer: SeasonSerializer),
              message: ['Season updated successfully'],
              status: 200,
              type: 'success'
            }
          else
            render json: { errors: season.errors.full_messages, status: 422 }, status: 422
          end
        end
      end
    end
  end
end
