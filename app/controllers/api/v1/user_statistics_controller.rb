module Api
  module V1
    class UserStatisticsController < ApplicationController
      before_action :authenticate_devise_api_token!

      # Class method to invalidate a user's statistics cache
      def self.invalidate_cache(user_id)
        cache_pattern = "user_statistics:#{user_id}:*"
        Rails.logger.info "Invalidating user statistics cache for user #{user_id}"

        # Get all keys matching the pattern
        if Rails.cache.respond_to?(:redis)
          # For Redis cache store
          keys = Rails.cache.redis.keys(cache_pattern)
          keys.each { |key| Rails.cache.delete(key) }
          Rails.logger.info "Invalidated #{keys.size} cache entries for user #{user_id}"
        else
          # For memory store or other cache stores that don't support pattern matching
          # Just delete the most common types of statistics
          %w[basic advanced competition comparative all].each do |stats_type|
            key = "user_statistics:#{user_id}:#{stats_type}"
            Rails.cache.delete(key)
            Rails.logger.info "Invalidated cache entry: #{key}"
          end
        end
      end

      def show
        user = User.find(params[:user_id])

        unless authorized_to_access?(user)
          render json: {
            errors: ['You are not authorized to access this resource'],
            status: 403
          }, status: :forbidden
          return
        end

        stats_type = params[:type] || 'basic'

        # Build a cache key based on user, stats type, and filters
        cache_key = build_cache_key(user, stats_type, filter_params)

        # Cache the statistics with an expiration time
        stats = Rails.cache.fetch(cache_key, expires_in: 1.hour) do
          # Only calculate if cache miss
          Rails.logger.info "Cache miss for user statistics: #{cache_key}"
          UserStatisticsService.calculate(user, stats_type, filter_params)
        end

        render json: {
          data: stats,
          message: ['Statistics retrieved successfully'],
          status: 200,
          type: 'success'
        }
      end

      private

      def filter_params
        params.permit(:competition_id, :season_id, :time_period)
      end

      def authorized_to_access?(user)
        current_devise_api_user&.admin? || current_devise_api_user&.id == user.id
      end

      def build_cache_key(user, stats_type, filters)
        # Start with base key components
        key_parts = ['user_statistics', user.id, stats_type]

        # Add filter parameters to the key if present
        key_parts << "competition_#{filters[:competition_id]}" if filters[:competition_id].present?
        key_parts << "season_#{filters[:season_id]}" if filters[:season_id].present?
        key_parts << "time_period_#{filters[:time_period]}" if filters[:time_period].present?

        # Add a version number to allow for easy cache invalidation when the calculation logic changes
        key_parts << 'v1'

        # Add timestamp of the most recent prediction to automatically invalidate cache when new predictions are made
        latest_prediction = user.match_predictions.order(updated_at: :desc).first
        last_prediction_time = latest_prediction ? latest_prediction.updated_at.to_i : 0
        key_parts << "last_prediction_#{last_prediction_time}"

        # Join all parts with a colon to form the final cache key
        key_parts.join(':')
      end
    end
  end
end
