# app/controllers/api/v1/seasons_controller.rb
module Api
  module V1
    class SeasonsController < ApplicationController
      before_action :authenticate_devise_api_token!
      before_action :authenticate_admin!, only: %i[archive_leagues transition_leagues]

      def matchday_options
        season = Season.find(params[:id])

        cache_key = "season_#{season.id}_matchday_options"

        options = Rails.cache.fetch(cache_key, expires_in: 10.minutes) do
          MatchdayOptionsService.new(season).generate_matchday_options
        end

        render json: {
          data: options,
          message: ['Matchday options retrieved successfully'],
          status: 200,
          type: 'success'
        }
      end

      # Archive all leagues for a specific season
      def archive_leagues
        season = Season.find(params[:id])
        service = LeagueArchiveService.new(season)
        archived_count = service.archive_all_leagues

        render json: {
          message: ["#{archived_count} leagues archived successfully"],
          status: 200,
          type: 'success'
        }
      end

      # Transition leagues to a new season and archive leagues from the old season
      def transition_leagues
        season = Season.find(params[:id])
        competition = season.competition

        # Ensure this is the current season for the competition
        unless competition.current_season_id == season.id
          render json: {
            errors: ['This is not the current season for this competition'],
            status: 422
          }, status: :unprocessable_entity
          return
        end

        # Create the service and transition leagues
        service = LeagueSeasonTransitionService.new(competition, season)

        # Check if transition is needed
        unless service.new_season?
          render json: {
            message: ['Leagues are already using the current season'],
            status: 200,
            type: 'success'
          }
          return
        end

        # Perform the transition (this will also archive leagues from the old season)
        transitioned_count = service.transition_leagues_to_new_season

        render json: {
          message: ["#{transitioned_count} leagues transitioned to new season successfully. Old season leagues have been archived."],
          status: 200,
          type: 'success'
        }
      end

      private

      def authenticate_admin!
        return if current_devise_api_user&.admin?

        render json: { errors: ['Only admin can perform this action'] }, status: :unauthorized
      end
    end
  end
end
