module Api
  module V1
    class MatchPredictionsController < ApplicationController
      before_action :authenticate_devise_api_token!

      def bulk_create_or_update # rubocop:disable Metrics/MethodLength,Metrics/PerceivedComplexity
        match_predictions = bulk_create_predictions_params
        successful_predictions = []
        failed_predictions = []

        match_predictions.each do |prediction| # rubocop:disable Metrics/BlockLength
          match = Match.find(prediction[:match_id])

          next if match.nil?

          round_prediction = RoundPrediction.find_or_initialize_by(user: current_devise_api_user,
                                                                   matchday: match.matchday,
                                                                   stage: match.stage || 'REGULAR_SEASON', # TODO: is the stage needed?
                                                                   season_id: match.season.id,
                                                                   competition_id: match.season.competition.id)

          # Save round_prediction before associating it with match_prediction
          if round_prediction.save
            match_prediction = round_prediction.match_predictions.find_or_initialize_by(match_id: prediction[:match_id])

            match_prediction.assign_attributes(
              home_score: prediction[:home_score],
              away_score: prediction[:away_score],
              user: current_devise_api_user
            )

            if match_prediction.save
              successful_predictions << round_prediction unless successful_predictions.include?(round_prediction)
            else
              failed_predictions << {
                data: match_prediction.attributes,
                errors: match_prediction.errors.full_messages
              }
            end
          else
            failed_predictions << {
              data: round_prediction.attributes,
              errors: round_prediction.errors.full_messages
            }
          end
        end

        # Invalidate user statistics cache after creating/updating predictions
        Api::V1::UserStatisticsController.invalidate_cache(current_devise_api_user.id)

        if failed_predictions.empty?
          render json: {
            round_predictions: ActiveModelSerializers::SerializableResource.new(
              successful_predictions,
              each_serializer: RoundPredictionSerializer
            ),
            message: ["Successfully created #{successful_predictions.count} round predictions"],
            status: 200,
            type: 'success'
          }
        else
          render json: {
            round_predictions: ActiveModelSerializers::SerializableResource.new(
              successful_predictions,
              each_serializer: RoundPredictionSerializer
            ),
            failed: failed_predictions,
            message: ["Created #{successful_predictions.count} predictions with #{failed_predictions.count} failures"],
            status: 207, # Multi-Status
            type: 'partial_success'
          }, status: :multi_status
        end
      end

      private

      def bulk_create_predictions_params
        params.require(:predictions).map do |prediction|
          prediction.permit(:match_id, :home_score, :away_score)
        end
      end
    end
  end
end
