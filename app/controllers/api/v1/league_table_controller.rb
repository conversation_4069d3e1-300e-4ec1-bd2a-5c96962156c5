module Api
  module V1
    class LeagueTableController < ApplicationController
      before_action :authenticate_devise_api_token!

      def index
        league = League.find(params[:league_id])

        # Check if a specific season was requested
        if params[:season_id].present?
          # Get historical standings for the specified season
          standings = league.calculate_standings_for_season(params[:season_id])
          season = Season.find(params[:season_id])

          render json: {
            league: {
              id: league.id,
              name: league.name,
              season: {
                id: season.id,
                start_date: season.start_date,
                end_date: season.end_date
              },
              standings:
            },
            message: ['Historical league standings calculated'],
            status: 200,
            type: 'success'
          }
        else
          # Get current standings
          render json: {
            league: ActiveModelSerializers::SerializableResource.new(league,
                                                                     serializer: LeagueTableIndexSerializer),
            message: ['League standings calculated'],
            status: 200,
            type: 'success'
          }
        end
      end
    end
  end
end
