# app/controllers/api/v1/youtube_controller.rb
# Controller for YouTube-related functionality
# Handles YouTube leagues, subscription status, and content creator status
module Api
  module V1
    class YoutubeController < ApplicationController
      before_action :authenticate_devise_api_token!

      # Feature gates: disable Connect/Creator features until verification
      before_action :ensure_youtube_connect_enabled, only: %i[subscription_status]
      before_action :ensure_youtube_creator_enabled, only: %i[update_creator_status check_subscriber_league_eligibility]

      # Get YouTube-related leagues
      def leagues
        # Get all YouTube leagues
        youtube_leagues = League.youtube_leagues

        # Filter by user if requested
        if params[:user_id].present?
          user = User.find(params[:user_id])
          youtube_leagues = youtube_leagues.where(owner: user)
        end

        # Filter by subscriber-only if requested
        if params[:subscriber_only].present?
          youtube_leagues = youtube_leagues.where(subscriber_only: params[:subscriber_only] == 'true')
        end

        render json: {
          leagues: ActiveModelSerializers::SerializableResource.new(
            youtube_leagues,
            each_serializer: LeagueIndexSerializer
          ),
          message: ['YouTube leagues found'],
          status: 200,
          type: 'success'
        }
      end

      # Get subscription status for a channel
      def subscription_status
        unless params[:channel_id].present?
          render json: { error: 'Missing channel_id parameter' }, status: :bad_request
          return
        end

        unless current_devise_api_user.youtube_connected?
          render json: {
            status: 'error',
            connected: false,
            subscribed: false,
            message: 'YouTube account not connected'
          }, status: :ok
          return
        end

        # Create a YouTube service instance with the current user
        youtube_service = BragRightsYouTubeService.new(current_devise_api_user)

        # Check if the user is subscribed to the channel
        is_subscribed = youtube_service.verify_subscription?(params[:channel_id])

        # Get channel info
        channel_info = youtube_service.get_channel_info(params[:channel_id])

        render json: {
          status: 'success',
          connected: true,
          subscribed: is_subscribed,
          channel: if channel_info
                     {
                       id: channel_info[:id],
                       name: channel_info[:title],
                       thumbnail_url: channel_info[:thumbnail_url]
                     }
                   else
                     { id: params[:channel_id] }
                   end
        }, status: :ok
      end

      CREATOR_MODE_MAX_ATTEMPTS = 5
      CREATOR_MODE_WINDOW = 1.hour

      # Update user's content creator status
      def update_creator_status
        # Only allow users with connected YouTube accounts
        unless current_devise_api_user.youtube_connected?
          render json: { error: 'YouTube account not connected' }, status: :bad_request
          return
        end

        # Check rate limit
        if creator_mode_attempts_exceeded?
          render json: {
            error: 'Too many attempts. Please try again later',
            retry_after: creator_mode_retry_after
          }, status: :too_many_requests
          return
        end

        # Track attempt
        increment_creator_mode_attempts

        # Parse desired state
        desired_state = ActiveModel::Type::Boolean.new.cast(params[:is_content_creator])

        # Enforce minimum subscribers when enabling creator mode
        if desired_state
          min_required = YoutubeEligibilityService.min_subscribers_required
          current_count = current_devise_api_user.youtube_subscriber_count.to_i
          if current_count < min_required
            render json: {
              error: "You need at least #{min_required} subscribers to enable creator mode",
              subscriber_count: current_count,
              min_subscribers_required: min_required
            }, status: :unprocessable_entity
            return
          end
        end

        # Update the user's content creator status
        if current_devise_api_user.update(is_content_creator: desired_state)
          render json: {
            message: 'Content creator status updated successfully',
            is_content_creator: current_devise_api_user.is_content_creator
          }, status: :ok
        else
          render json: { error: 'Failed to update content creator status' }, status: :unprocessable_entity
        end
      end

      # Check if the user is eligible to create a subscriber-only league
      def check_subscriber_league_eligibility
        can_create = YoutubeEligibilityService.can_create_subscriber_league?(current_devise_api_user)

        if can_create
          render json: {
            eligible: true,
            message: 'You are eligible to create subscriber-only leagues',
            subscriber_count: current_devise_api_user.youtube_subscriber_count,
            min_subscribers_required: YoutubeEligibilityService.min_subscribers_required
          }, status: :ok
        else
          reason = YoutubeEligibilityService.subscriber_league_ineligibility_reason(current_devise_api_user)
          render json: {
            eligible: false,
            message: reason,
            subscriber_count: current_devise_api_user.youtube_subscriber_count,
            min_subscribers_required: YoutubeEligibilityService.min_subscribers_required
          }, status: :ok
        end
      end

      private

      def ensure_youtube_connect_enabled
        enabled = ActiveModel::Type::Boolean.new.cast(ENV.fetch('YOUTUBE_CONNECT_ENABLED',
                                                                Rails.env.production? ? 'false' : 'true'))
        return if enabled

        render json: { error: 'YouTube Connect is not available at this time' }, status: :forbidden
      end

      def ensure_youtube_creator_enabled
        enabled = ActiveModel::Type::Boolean.new.cast(ENV.fetch('YOUTUBE_CREATOR_ENABLED',
                                                                Rails.env.production? ? 'false' : 'true'))
        return if enabled

        render json: { error: 'YouTube Creator features are not available at this time' }, status: :forbidden
      end

      def creator_mode_attempts_exceeded?
        attempts = Rails.cache.read(creator_mode_attempts_key).to_i
        attempts >= CREATOR_MODE_MAX_ATTEMPTS
      end

      def increment_creator_mode_attempts
        current = Rails.cache.read(creator_mode_attempts_key).to_i
        Rails.cache.write(
          creator_mode_attempts_key,
          current + 1,
          expires_in: CREATOR_MODE_WINDOW
        )
      end

      def creator_mode_retry_after
        ttl = Rails.cache.redis.ttl(creator_mode_attempts_key)
        ttl.positive? ? ttl : CREATOR_MODE_WINDOW.to_i
      end

      def creator_mode_attempts_key
        "creator_mode_attempts:#{current_devise_api_user.id}"
      end
    end
  end
end
