# app/controllers/api/v1/youtube_auth_controller.rb
# Controller for YouTube authentication
# Handles connecting and disconnecting YouTube accounts, and verifying subscriptions

module Api
  module V1
    class YoutubeAuthController < ApplicationController
      # Allow unauthenticated access for OAuth callback, login and signup
      before_action :authenticate_devise_api_token!, except: %i[callback login signup]
      # Gate YouTube connect-related actions behind an ENV-based feature flag
      before_action :ensure_youtube_connect_enabled, only: %i[connect disconnect verify_subscription]

      # Handle the OAuth callback from YouTube
      def callback
        Rails.logger.info "YouTube OAuth callback received with code: #{params[:code].present? ? '[PRESENT]' : '[MISSING]'}"

        unless ENV['FRONTEND_URL'].present?
          error_msg = 'Missing required environment variable: FRONTEND_URL'
          Rails.logger.error error_msg
          render plain: "Configuration error: #{error_msg}", status: :internal_server_error
          return
        end

        # This would be implemented with OmniAuth in a real application
        # For now, we'll just redirect to a frontend URL with a code
        # that the frontend can exchange for a token
        redirect_url = "#{ENV['FRONTEND_URL']}/youtube/auth?code=#{params[:code]}"
        Rails.logger.info "Redirecting to: #{redirect_url}"
        redirect_to redirect_url
      end

      # YouTube-based login: exchange Google code for email, find user, issue Devise API token
      def login
        Rails.logger.info "[YouTubeAuth#login] Received login request. code present? #{params[:code].present?}"

        unless params[:code].present? && params[:redirect_uri].present? && params[:client_id].present?
          missing = []
          missing << 'code' unless params[:code].present?
          missing << 'redirect_uri' unless params[:redirect_uri].present?
          missing << 'client_id' unless params[:client_id].present?
          error_msg = "Missing required parameters: #{missing.join(', ')}"
          Rails.logger.error "[YouTubeAuth#login] #{error_msg}"
          render json: { error: error_msg }, status: :bad_request
          return
        end

        unless ENV['GOOGLE_CLIENT_SECRET'].present?
          Rails.logger.error '[YouTubeAuth#login] Missing GOOGLE_CLIENT_SECRET env var'
          render json: { error: 'Server configuration error' }, status: :internal_server_error
          return
        end

        # Validate OAuth client and redirect URI
        unless valid_oauth_client_params?(params[:client_id], params[:redirect_uri])
          Rails.logger.error '[YouTubeAuth#login] Invalid client_id or redirect_uri'
          render json: { error: 'Invalid OAuth client configuration' }, status: :bad_request
          return
        end

        token_data = exchange_code_for_tokens(params[:code], params[:client_id], params[:redirect_uri])
        unless token_data
          Rails.logger.error '[YouTubeAuth#login] token exchange failed'
          render json: { error: 'Failed to exchange authorization code' }, status: :bad_request
          return
        end

        userinfo = fetch_google_userinfo(token_data['access_token'])
        unless userinfo && userinfo['email'].present?
          Rails.logger.error "[YouTubeAuth#login] Failed to fetch userinfo or email missing. userinfo: #{userinfo.inspect}"
          render json: { error: 'Failed to fetch Google user info' }, status: :bad_request
          return
        end

        Rails.logger.info "[YouTubeAuth#login] Google user email: #{userinfo['email']}"
        user = User.find_by(email: userinfo['email'])
        unless user
          Rails.logger.warn "[YouTubeAuth#login] No user found for email #{userinfo['email']}"
          render json: { error: 'User not found for this Google account' }, status: :not_found
          return
        end

        token = issue_devise_api_token_for(user)
        unless token
          Rails.logger.error '[YouTubeAuth#login] Failed to issue API token'
          render json: { error: 'Failed to create session' }, status: :internal_server_error
          return
        end

        Rails.logger.info "[YouTubeAuth#login] Issued token for user #{user.id}"
        render json: token_payload(token, user), status: :ok
      rescue StandardError => e
        Rails.logger.error "[YouTubeAuth#login] Exception: #{e.class} - #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
        render json: { error: 'Unexpected server error' }, status: :internal_server_error
      end

      # YouTube-based signup: exchange Google code, create user if needed, issue Devise API token
      def signup
        Rails.logger.info "[YouTubeAuth#signup] Received signup request. code present? #{params[:code].present?}"

        unless params[:code].present? && params[:redirect_uri].present? && params[:client_id].present?
          missing = []
          missing << 'code' unless params[:code].present?
          missing << 'redirect_uri' unless params[:redirect_uri].present?
          missing << 'client_id' unless params[:client_id].present?
          error_msg = "Missing required parameters: #{missing.join(', ')}"
          Rails.logger.error "[YouTubeAuth#signup] #{error_msg}"
          render json: { error: error_msg }, status: :bad_request
          return
        end

        unless ENV['GOOGLE_CLIENT_SECRET'].present?
          Rails.logger.error '[YouTubeAuth#signup] Missing GOOGLE_CLIENT_SECRET env var'
          render json: { error: 'Server configuration error' }, status: :internal_server_error
          return
        end

        # Validate OAuth client and redirect URI
        unless valid_oauth_client_params?(params[:client_id], params[:redirect_uri])
          Rails.logger.error '[YouTubeAuth#signup] Invalid client_id or redirect_uri'
          render json: { error: 'Invalid OAuth client configuration' }, status: :bad_request
          return
        end

        token_data = exchange_code_for_tokens(params[:code], params[:client_id], params[:redirect_uri])
        unless token_data
          Rails.logger.error '[YouTubeAuth#signup] token exchange failed'
          render json: { error: 'Failed to exchange authorization code' }, status: :bad_request
          return
        end

        userinfo = fetch_google_userinfo(token_data['access_token'])
        unless userinfo && userinfo['email'].present?
          Rails.logger.error "[YouTubeAuth#signup] Failed to fetch userinfo or email missing. userinfo: #{userinfo.inspect}"
          render json: { error: 'Failed to fetch Google user info' }, status: :bad_request
          return
        end

        email = userinfo['email']
        name = userinfo['name'].presence || email.split('@').first
        Rails.logger.info "[YouTubeAuth#signup] Google user email: #{email}, name: #{name}"

        user = User.find_by(email:)
        if user
          Rails.logger.info "[YouTubeAuth#signup] User already exists for email #{email}, proceeding to issue token"
        else
          username = generate_unique_username(name)
          Rails.logger.info "[YouTubeAuth#signup] Creating user with email=#{email}, username=#{username}"
          user = User.new(email:, username:, password: SecureRandom.hex(12))
          # Mark as confirmed because identity is verified by Google
          user.confirmed_at = Time.current if user.respond_to?(:confirmed_at)

          unless user.save
            Rails.logger.error "[YouTubeAuth#signup] Failed to create user: #{user.errors.full_messages.join(', ')}"
            render json: { error: 'Failed to create user', details: user.errors.full_messages },
                   status: :unprocessable_entity
            return
          end
          Rails.logger.info "[YouTubeAuth#signup] User created with id=#{user.id}"
        end

        token = issue_devise_api_token_for(user)
        unless token
          Rails.logger.error '[YouTubeAuth#signup] Failed to issue API token'
          render json: { error: 'Failed to create session' }, status: :internal_server_error
          return
        end

        Rails.logger.info "[YouTubeAuth#signup] Issued token for user #{user.id}"
        render json: token_payload(token, user), status: :ok
      rescue StandardError => e
        Rails.logger.error "[YouTubeAuth#signup] Exception: #{e.class} - #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
        render json: { error: 'Unexpected server error' }, status: :internal_server_error
      end

      def status
        Rails.logger.info "Getting YouTube connection status for user: #{current_devise_api_user.id}"

        status_data = {
          connected: current_devise_api_user.youtube_connected?,
          is_content_creator: current_devise_api_user.is_content_creator,
          channel_id: current_devise_api_user.youtube_channel_id,
          channel_name: current_devise_api_user.youtube_channel_name,
          avatar_url: current_devise_api_user.youtube_avatar_url,
          subscriber_count: current_devise_api_user.youtube_subscriber_count,
          verified_at: current_devise_api_user.youtube_verified_at
        }

        Rails.logger.info "YouTube status for user #{current_devise_api_user.id}: connected=#{status_data[:connected]}, channel=#{status_data[:channel_id]}"
        render json: status_data, status: :ok
      end

      # Connect a YouTube account to the user's account
      def connect
        # Log the request parameters (excluding sensitive data)
        Rails.logger.info "YouTube Auth Connect request received with params: #{params.except(:credentials).to_json}"

        # Check environment variables
        check_required_env_variables

        # Check if we're receiving an OAuth code or direct channel data
        if params[:code].present?
          # OAuth flow - exchange code for tokens
          Rails.logger.info 'Processing OAuth code flow'

          unless params[:code].present? && params[:redirect_uri].present? && params[:client_id].present?
            missing_params = []
            missing_params << 'code' unless params[:code].present?
            missing_params << 'redirect_uri' unless params[:redirect_uri].present?
            missing_params << 'client_id' unless params[:client_id].present?

            error_msg = "Missing required parameters: #{missing_params.join(', ')}"
            Rails.logger.error error_msg
            render json: { error: error_msg }, status: :bad_request
            return
          end

          # Validate OAuth client and redirect URI
          unless valid_oauth_client_params?(params[:client_id], params[:redirect_uri])
            Rails.logger.error '[YouTubeAuth#connect] Invalid client_id or redirect_uri'
            render json: { error: 'Invalid OAuth client configuration' }, status: :bad_request
            return
          end

          Rails.logger.info "Exchanging authorization code for tokens with redirect_uri: #{params[:redirect_uri]}"

          # Exchange the authorization code for tokens
          begin
            response = HTTP.post(
              'https://oauth2.googleapis.com/token',
              form: {
                code: params[:code],
                client_id: params[:client_id],
                client_secret: ENV['GOOGLE_CLIENT_SECRET'],
                redirect_uri: params[:redirect_uri],
                grant_type: 'authorization_code'
              }
            )

            Rails.logger.info "Token exchange response status: #{response.status}"

            unless response.status.success?
              error_msg = "Failed to exchange authorization code. Status: #{response.status}, Body: #{response.body}"
              Rails.logger.error error_msg
              render json: {
                error: 'Failed to exchange authorization code',
                details: response.body.to_s,
                status_code: response.status.to_i
              }, status: :bad_request
              return
            end

            token_data = JSON.parse(response.body.to_s)
            Rails.logger.info "Successfully obtained tokens. Access token present: #{token_data['access_token'].present?}, Refresh token present: #{token_data['refresh_token'].present?}"
          rescue StandardError => e
            error_msg = "Exception during token exchange: #{e.class.name} - #{e.message}"
            Rails.logger.error error_msg
            Rails.logger.error e.backtrace.join("\n")
            render json: { error: error_msg }, status: :internal_server_error
            return
          end

          # Get user info from YouTube API
          begin
            Rails.logger.info 'Initializing YouTube API client'
            youtube_service = Google::Apis::YoutubeV3::YouTubeService.new

            # Use OAuth token instead of API key - don't set the key property
            youtube_service.authorization = token_data['access_token']

            # Set client options
            youtube_service.client_options.application_name = 'BragRights'
            youtube_service.client_options.application_version = '1.0.0'

            Rails.logger.info 'Fetching channel information from YouTube API'

            # Add more detailed logging
            Rails.logger.info "Using access token: #{token_data['access_token'][0..10]}... (truncated)"
            Rails.logger.info "Token type: #{token_data['token_type']}"
            Rails.logger.info "Scopes: #{token_data['scope']}"

            begin
              channels = youtube_service.list_channels('snippet,statistics', mine: true)

              Rails.logger.info "API response received: #{channels.inspect}"

              if channels.items.empty?
                error_msg = 'No YouTube channel found for this account'
                Rails.logger.error error_msg
                render json: { error: error_msg }, status: :bad_request
                return
              end

              Rails.logger.info "Found #{channels.items.count} channels"
            rescue Google::Apis::ClientError => e
              error_msg = "YouTube API client error: #{e.message}"
              Rails.logger.error error_msg
              render json: { error: error_msg, details: e.message }, status: :bad_request
              return
            rescue Google::Apis::ServerError => e
              error_msg = "YouTube API server error: #{e.message}"
              Rails.logger.error error_msg
              render json: { error: error_msg, details: e.message }, status: :bad_request
              return
            rescue Google::Apis::AuthorizationError => e
              error_msg = "YouTube API authorization error: #{e.message}"
              Rails.logger.error error_msg
              render json: { error: error_msg, details: e.message }, status: :bad_request
              return
            end

            channel = channels.items.first
            Rails.logger.info "Found YouTube channel: #{channel.id} (#{channel.snippet.title})"

            # Connect the YouTube account
            auth_data = {
              channel_id: channel.id,
              channel_name: channel.snippet.title,
              avatar_url: channel.snippet.thumbnails.default.url,
              subscriber_count: channel.statistics.subscriber_count,
              credentials: {
                access_token: token_data['access_token'],
                refresh_token: token_data['refresh_token'],
                expires_in: token_data['expires_in'],
                token_type: token_data['token_type'],
                scope: token_data['scope']
              }
            }

            Rails.logger.info "Prepared auth data for channel: #{auth_data[:channel_id]} (#{auth_data[:channel_name]})"
          rescue Google::Apis::Error => e
            error_msg = "YouTube API error: #{e.class.name} - #{e.message}"
            Rails.logger.error error_msg
            Rails.logger.error e.backtrace.join("\n")
            render json: {
              error: 'YouTube API error',
              details: e.message,
              status: e.status_code
            }, status: :bad_request
            return
          rescue StandardError => e
            error_msg = "Unexpected error during YouTube API call: #{e.class.name} - #{e.message}"
            Rails.logger.error error_msg
            Rails.logger.error e.backtrace.join("\n")
            render json: { error: error_msg }, status: :internal_server_error
            return
          end
        else
          # Direct channel data flow (for testing/development)
          Rails.logger.info 'Processing direct channel data flow'

          unless params[:channel_id].present? && params[:channel_name].present?
            missing_params = []
            missing_params << 'channel_id' unless params[:channel_id].present?
            missing_params << 'channel_name' unless params[:channel_name].present?

            error_msg = "Missing required parameters: #{missing_params.join(', ')}"
            Rails.logger.error error_msg
            render json: { error: error_msg }, status: :bad_request
            return
          end

          # Connect the YouTube account
          auth_data = {
            channel_id: params[:channel_id],
            channel_name: params[:channel_name],
            avatar_url: params[:avatar_url],
            subscriber_count: params[:subscriber_count],
            credentials: params[:credentials]
          }

          Rails.logger.info "Prepared direct auth data for channel: #{auth_data[:channel_id]} (#{auth_data[:channel_name]})"
        end

        Rails.logger.info "Connecting YouTube account for user: #{current_devise_api_user.id}"

        begin
          # Check if another user already has this YouTube channel connected
          existing_user = User.where(youtube_channel_id: auth_data[:channel_id])
                              .where.not(id: current_devise_api_user.id)
                              .first

          if existing_user.present?
            error_msg = "This YouTube channel is already connected to another account (#{existing_user.email})"
            Rails.logger.error error_msg
            render json: {
              error: error_msg,
              details: 'YouTube channel already connected to another account',
              status: 409 # Conflict
            }, status: :conflict
            return
          end

          if current_devise_api_user.connect_youtube_account(auth_data)
            Rails.logger.info "Successfully connected YouTube account for user: #{current_devise_api_user.id}, channel: #{current_devise_api_user.youtube_channel_id}"

            render json: {
              message: 'YouTube account connected successfully',
              user: {
                id: current_devise_api_user.id,
                youtube_channel_id: current_devise_api_user.youtube_channel_id,
                youtube_channel_name: current_devise_api_user.youtube_channel_name,
                is_content_creator: current_devise_api_user.is_content_creator
              }
            }, status: :ok
          else
            error_msg = "Failed to connect YouTube account for user: #{current_devise_api_user.id}"
            Rails.logger.error error_msg
            render json: { error: error_msg }, status: :unprocessable_entity
          end
        rescue StandardError => e
          error_msg = "Exception while connecting YouTube account: #{e.class.name} - #{e.message}"
          Rails.logger.error error_msg
          Rails.logger.error e.backtrace.join("\n")
          render json: { error: error_msg }, status: :internal_server_error
        end
      end

      # Disconnect a YouTube account from the user's account
      def disconnect
        Rails.logger.info "Disconnecting YouTube account for user: #{current_devise_api_user.id}"

        begin
          if current_devise_api_user.disconnect_youtube_account
            Rails.logger.info "Successfully disconnected YouTube account for user: #{current_devise_api_user.id}"
            render json: { message: 'YouTube account disconnected successfully' }, status: :ok
          else
            error_msg = "Failed to disconnect YouTube account for user: #{current_devise_api_user.id}"
            Rails.logger.error error_msg
            render json: { error: error_msg }, status: :unprocessable_entity
          end
        rescue StandardError => e
          error_msg = "Exception while disconnecting YouTube account: #{e.class.name} - #{e.message}"
          Rails.logger.error error_msg
          Rails.logger.error e.backtrace.join("\n")
          render json: { error: error_msg }, status: :internal_server_error
        end
      end

      # Verify if the user is subscribed to a channel
      def verify_subscription
        Rails.logger.info "Verifying subscription for user: #{current_devise_api_user.id}, params: #{params.to_json}"

        # Check environment variables
        check_required_env_variables

        unless params[:channel_id].present?
          error_msg = 'Missing channel_id parameter'
          Rails.logger.error error_msg
          render json: { error: error_msg }, status: :bad_request
          return
        end

        unless current_devise_api_user.youtube_connected?
          Rails.logger.info "User #{current_devise_api_user.id} does not have a connected YouTube account"
          render json: {
            status: 'error',
            connected: false,
            subscribed: false,
            message: 'YouTube account not connected'
          }, status: :ok
          return
        end

        begin
          # Create a YouTube service instance with the current user
          Rails.logger.info "Creating YouTube service for user: #{current_devise_api_user.id}"
          youtube_service = BragRightsYouTubeService.new(current_devise_api_user)

          # Check if the user is subscribed to the channel
          Rails.logger.info "Checking if user #{current_devise_api_user.id} is subscribed to channel: #{params[:channel_id]}"
          is_subscribed = youtube_service.verify_subscription?(params[:channel_id])
          Rails.logger.info "Subscription check result for user #{current_devise_api_user.id} to channel #{params[:channel_id]}: #{is_subscribed}"

          render json: {
            status: 'success',
            connected: true,
            subscribed: is_subscribed,
            message: is_subscribed ? 'User is subscribed to this channel' : 'User is not subscribed to this channel'
          }, status: :ok
        rescue StandardError => e
          error_msg = "Exception during subscription verification: #{e.class.name} - #{e.message}"
          Rails.logger.error error_msg
          Rails.logger.error e.backtrace.join("\n")
          render json: {
            status: 'error',

            connected: true,
            subscribed: false,
            message: "Error verifying subscription: #{e.message}"
          }, status: :ok
        end
      end

      private

      # Feature gate: disable Connect/Creator until verification. Enabled by default in non-production.
      def ensure_youtube_connect_enabled
        enabled = ActiveModel::Type::Boolean.new.cast(ENV.fetch('YOUTUBE_CONNECT_ENABLED',
                                                                Rails.env.production? ? 'false' : 'true'))
        return if enabled

        Rails.logger.info '[YouTubeAuth] YouTube Connect features are disabled by configuration'
        render json: { error: 'YouTube Connect is not available at this time' }, status: :forbidden
      end

      def exchange_code_for_tokens(code, client_id, redirect_uri)
        Rails.logger.info "[YouTubeAuth] Exchanging code for tokens with redirect_uri: #{redirect_uri}"
        response = HTTP.post(
          'https://oauth2.googleapis.com/token',
          form: {
            code:,
            client_id:,
            client_secret: ENV['GOOGLE_CLIENT_SECRET'],
            redirect_uri:,
            grant_type: 'authorization_code'
          }
        )
        Rails.logger.info "[YouTubeAuth] Token exchange status: #{response.status}"
        return JSON.parse(response.body.to_s) if response.status.success?

        Rails.logger.error "[YouTubeAuth] Token exchange failed. Status: #{response.status}, Body: #{response.body}"
        nil
      rescue StandardError => e
        Rails.logger.error "[YouTubeAuth] Exception during token exchange: #{e.class} - #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
        nil
      end

      def fetch_google_userinfo(access_token)
        Rails.logger.info '[YouTubeAuth] Fetching Google userinfo'
        response = HTTP.auth("Bearer #{access_token}").get('https://www.googleapis.com/oauth2/v3/userinfo')
        Rails.logger.info "[YouTubeAuth] Userinfo status: #{response.status}"
        return JSON.parse(response.body.to_s) if response.status.success?

        Rails.logger.error "[YouTubeAuth] Failed to fetch userinfo. Status: #{response.status}, Body: #{response.body}"
        nil
      rescue StandardError => e
        Rails.logger.error "[YouTubeAuth] Exception during userinfo fetch: #{e.class} - #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
        nil
      end

      def issue_devise_api_token_for(user)
        Rails.logger.info "[YouTubeAuth] Issuing Devise API token for user #{user.id}"
        Devise::Api::Token.create!(
          resource_owner: user,
          access_token: SecureRandom.hex(32),
          refresh_token: SecureRandom.hex(32),
          expires_in: 1.hour.to_i
        )
      rescue StandardError => e
        Rails.logger.error "[YouTubeAuth] Failed to create Devise API token: #{e.class} - #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
        nil
      end

      def token_payload(token, user)
        {
          token: token.access_token,
          refresh_token: token.refresh_token,
          token_type: 'Bearer',
          expires_in: token.expires_in,
          resource_owner: { id: user.id, email: user.email },
          admin: user.admin
        }
      end

      def generate_unique_username(base)
        base_slug = base.to_s.downcase.gsub(/[^a-z0-9]/, '_').gsub(/_+/, '_').gsub(/^_|_$/, '')
        base_slug = 'user' if base_slug.blank?
        candidate = base_slug
        idx = 1
        while User.exists?(username: candidate)
          idx += 1
          candidate = "#{base_slug}_#{idx}"
        end
        candidate
      end

      # Check if all required environment variables are present
      def check_required_env_variables
        required_vars = %w[GOOGLE_CLIENT_ID GOOGLE_CLIENT_SECRET YOUTUBE_API_KEY]
        missing_vars = required_vars.select { |var| ENV[var].blank? }

        if missing_vars.any?
          error_msg = "Missing required environment variables: #{missing_vars.join(', ')}"
          Rails.logger.error error_msg
          raise StandardError, error_msg
        end

        Rails.logger.info 'All required environment variables are present'
      end

      # Validate client_id and redirect_uri match backend configuration
      def valid_oauth_client_params?(client_id, redirect_uri)
        expected_client_id = ENV['GOOGLE_CLIENT_ID']
        expected_redirect = ENV['YOUTUBE_OAUTH_CALLBACK_URL']

        if expected_client_id.blank? || expected_redirect.blank?
          Rails.logger.warn '[YouTubeAuth] Skipping OAuth client param validation because GOOGLE_CLIENT_ID or YOUTUBE_OAUTH_CALLBACK_URL is not set'
          return true
        end

        valid_client = (client_id.to_s.strip == expected_client_id.to_s.strip)
        valid_redirect = (redirect_uri.to_s.strip == expected_redirect.to_s.strip)

        Rails.logger.info "[YouTubeAuth] OAuth client param validation: client_id_valid=#{valid_client}, redirect_uri_valid=#{valid_redirect}"
        valid_client && valid_redirect
      end
    end
  end
end

          # Get user info from YouTube API
          begin
            Rails.logger.info 'Initializing YouTube API client'
            youtube_service = Google::Apis::YoutubeV3::YouTubeService.new

            # Use OAuth token instead of API key - don't set the key property
            youtube_service.authorization = token_data['access_token']

            # Set client options
            youtube_service.client_options.application_name = 'BragRights'
            youtube_service.client_options.application_version = '1.0.0'

            Rails.logger.info 'Fetching channel information from YouTube API'

            # Add more detailed logging
            Rails.logger.info "Using access token: #{token_data['access_token'][0..10]}... (truncated)"
            Rails.logger.info "Token type: #{token_data['token_type']}"
            Rails.logger.info "Scopes: #{token_data['scope']}"

            begin
              channels = youtube_service.list_channels('snippet,statistics', mine: true)

              Rails.logger.info "API response received: #{channels.inspect}"

              if channels.items.empty?
                error_msg = 'No YouTube channel found for this account'
                Rails.logger.error error_msg
                render json: { error: error_msg }, status: :bad_request
                return
              end

              Rails.logger.info "Found #{channels.items.count} channels"
            rescue Google::Apis::ClientError => e
              error_msg = "YouTube API client error: #{e.message}"
              Rails.logger.error error_msg
              render json: { error: error_msg, details: e.message }, status: :bad_request
              return
            rescue Google::Apis::ServerError => e
              error_msg = "YouTube API server error: #{e.message}"
              Rails.logger.error error_msg
              render json: { error: error_msg, details: e.message }, status: :bad_request
              return
            rescue Google::Apis::AuthorizationError => e
              error_msg = "YouTube API authorization error: #{e.message}"
              Rails.logger.error error_msg
              render json: { error: error_msg, details: e.message }, status: :bad_request
              return
            end

            channel = channels.items.first
            Rails.logger.info "Found YouTube channel: #{channel.id} (#{channel.snippet.title})"

            # Connect the YouTube account
            auth_data = {
              channel_id: channel.id,
              channel_name: channel.snippet.title,
              avatar_url: channel.snippet.thumbnails.default.url,
              subscriber_count: channel.statistics.subscriber_count,
              credentials: {
                access_token: token_data['access_token'],
                refresh_token: token_data['refresh_token'],
                expires_in: token_data['expires_in'],
                token_type: token_data['token_type'],
                scope: token_data['scope']
              }
            }

            Rails.logger.info "Prepared auth data for channel: #{auth_data[:channel_id]} (#{auth_data[:channel_name]})"
          rescue Google::Apis::Error => e
            error_msg = "YouTube API error: #{e.class.name} - #{e.message}"
            Rails.logger.error error_msg
            Rails.logger.error e.backtrace.join("\n")
            render json: {
              error: 'YouTube API error',
              details: e.message,
              status: e.status_code
            }, status: :bad_request
            return
          rescue StandardError => e
            error_msg = "Unexpected error during YouTube API call: #{e.class.name} - #{e.message}"
            Rails.logger.error error_msg
            Rails.logger.error e.backtrace.join("\n")
            render json: { error: error_msg }, status: :internal_server_error
            return
          end
        else
          # Direct channel data flow (for testing/development)
          Rails.logger.info 'Processing direct channel data flow'

          unless params[:channel_id].present? && params[:channel_name].present?
            missing_params = []
            missing_params << 'channel_id' unless params[:channel_id].present?
            missing_params << 'channel_name' unless params[:channel_name].present?

            error_msg = "Missing required parameters: #{missing_params.join(', ')}"
            Rails.logger.error error_msg
            render json: { error: error_msg }, status: :bad_request
            return
          end

          # Connect the YouTube account
          auth_data = {
            channel_id: params[:channel_id],
            channel_name: params[:channel_name],
            avatar_url: params[:avatar_url],
            subscriber_count: params[:subscriber_count],
            credentials: params[:credentials]
          }

          Rails.logger.info "Prepared direct auth data for channel: #{auth_data[:channel_id]} (#{auth_data[:channel_name]})"
        end

        Rails.logger.info "Connecting YouTube account for user: #{current_devise_api_user.id}"

        begin
          # Check if another user already has this YouTube channel connected
          existing_user = User.where(youtube_channel_id: auth_data[:channel_id])
                              .where.not(id: current_devise_api_user.id)
                              .first

          if existing_user.present?
            error_msg = "This YouTube channel is already connected to another account (#{existing_user.email})"
            Rails.logger.error error_msg
            render json: {
              error: error_msg,
              details: 'YouTube channel already connected to another account',
              status: 409 # Conflict
            }, status: :conflict
            return
          end

          if current_devise_api_user.connect_youtube_account(auth_data)
            Rails.logger.info "Successfully connected YouTube account for user: #{current_devise_api_user.id}, channel: #{current_devise_api_user.youtube_channel_id}"

            render json: {
              message: 'YouTube account connected successfully',
              user: {
                id: current_devise_api_user.id,
                youtube_channel_id: current_devise_api_user.youtube_channel_id,
                youtube_channel_name: current_devise_api_user.youtube_channel_name,
                is_content_creator: current_devise_api_user.is_content_creator
              }
            }, status: :ok
          else
            error_msg = "Failed to connect YouTube account for user: #{current_devise_api_user.id}"
            Rails.logger.error error_msg
            render json: { error: error_msg }, status: :unprocessable_entity
          end
        rescue StandardError => e
          error_msg = "Exception while connecting YouTube account: #{e.class.name} - #{e.message}"
          Rails.logger.error error_msg
          Rails.logger.error e.backtrace.join("\n")
          render json: { error: error_msg }, status: :internal_server_error
        end
      end

      # Disconnect a YouTube account from the user's account
      def disconnect
        Rails.logger.info "Disconnecting YouTube account for user: #{current_devise_api_user.id}"

        begin
          if current_devise_api_user.disconnect_youtube_account
            Rails.logger.info "Successfully disconnected YouTube account for user: #{current_devise_api_user.id}"
            render json: { message: 'YouTube account disconnected successfully' }, status: :ok
          else
            error_msg = "Failed to disconnect YouTube account for user: #{current_devise_api_user.id}"
            Rails.logger.error error_msg
            render json: { error: error_msg }, status: :unprocessable_entity
          end
        rescue StandardError => e
          error_msg = "Exception while disconnecting YouTube account: #{e.class.name} - #{e.message}"
          Rails.logger.error error_msg
          Rails.logger.error e.backtrace.join("\n")
          render json: { error: error_msg }, status: :internal_server_error
        end
      end

      # Get the user's YouTube connection status

      # Verify if the user is subscribed to a channel
      def verify_subscription
        Rails.logger.info "Verifying subscription for user: #{current_devise_api_user.id}, params: #{params.to_json}"

        # Check environment variables
        check_required_env_variables

        unless params[:channel_id].present?
          error_msg = 'Missing channel_id parameter'
          Rails.logger.error error_msg
          render json: { error: error_msg }, status: :bad_request
          return
        end

        unless current_devise_api_user.youtube_connected?
          Rails.logger.info "User #{current_devise_api_user.id} does not have a connected YouTube account"
          render json: {
            status: 'error',
            connected: false,
            subscribed: false,
            message: 'YouTube account not connected'
          }, status: :ok
          return
        end

        begin
          # Create a YouTube service instance with the current user
          Rails.logger.info "Creating YouTube service for user: #{current_devise_api_user.id}"
          youtube_service = BragRightsYouTubeService.new(current_devise_api_user)

          # Check if the user is subscribed to the channel
          Rails.logger.info "Checking if user #{current_devise_api_user.id} is subscribed to channel: #{params[:channel_id]}"
          is_subscribed = youtube_service.verify_subscription?(params[:channel_id])
          Rails.logger.info "Subscription check result for user #{current_devise_api_user.id} to channel #{params[:channel_id]}: #{is_subscribed}"

          render json: {
            status: 'success',
            connected: true,
            subscribed: is_subscribed,
            message: is_subscribed ? 'User is subscribed to this channel' : 'User is not subscribed to this channel'
          }, status: :ok
        rescue StandardError => e
          error_msg = "Exception during subscription verification: #{e.class.name} - #{e.message}"
          Rails.logger.error error_msg
          Rails.logger.error e.backtrace.join("\n")
          render json: {
            status: 'error',

            connected: true,
            subscribed: false,
            message: "Error verifying subscription: #{e.message}"
          }, status: :ok
        end
      end

      private

      # Check if all required environment variables are present
      def check_required_env_variables
        required_vars = %w[GOOGLE_CLIENT_ID GOOGLE_CLIENT_SECRET YOUTUBE_API_KEY]
        missing_vars = required_vars.select { |var| ENV[var].blank? }

        if missing_vars.any?
          error_msg = "Missing required environment variables: #{missing_vars.join(', ')}"
          Rails.logger.error error_msg
          raise StandardError, error_msg
        end

        Rails.logger.info 'All required environment variables are present'
      end

      # Validate client_id and redirect_uri match backend configuration
      def valid_oauth_client_params?(client_id, redirect_uri)
        expected_client_id = ENV['GOOGLE_CLIENT_ID']
        expected_redirect = ENV['YOUTUBE_OAUTH_CALLBACK_URL']

        if expected_client_id.blank? || expected_redirect.blank?
          Rails.logger.warn '[YouTubeAuth] Skipping OAuth client param validation because GOOGLE_CLIENT_ID or YOUTUBE_OAUTH_CALLBACK_URL is not set'
          return true
        end

        valid_client = (client_id.to_s.strip == expected_client_id.to_s.strip)
        valid_redirect = (redirect_uri.to_s.strip == expected_redirect.to_s.strip)

        Rails.logger.info "[YouTubeAuth] OAuth client param validation: client_id_valid=#{valid_client}, redirect_uri_valid=#{valid_redirect}"
        valid_client && valid_redirect
      end
    end
  end
end
