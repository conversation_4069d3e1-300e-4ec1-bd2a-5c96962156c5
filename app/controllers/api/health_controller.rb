module Api
  class HealthController < ApplicationController
    # Use a more compatible way to skip authentication
    skip_before_action :authenticate_user! if respond_to?(:authenticate_user!)

    skip_before_action :authenticate_user_from_token! if respond_to?(:authenticate_user_from_token!)

    # Simple health check endpoint at /api/health
    def check
      render json: {
        status: 'ok',
        version: Rails.application.config.respond_to?(:version) ? Rails.application.config.version : 'unknown',
        environment: Rails.env,
        database: database_connected?,
        redis: redis_connected?
      }, status: :ok
    end

    private

    def database_connected?
      ActiveRecord::Base.connection.execute('SELECT 1')
      true
    rescue StandardError
      false
    end

    def redis_connected?
      Redis.new(url: ENV['REDIS_URL']).ping == 'PONG'
      true
    rescue StandardError
      false
    end
  end
end
