class TokensController < Devise::Api::TokensController
  rescue_from ActiveRecord::RecordInvalid, with: :handle_validation_error
  before_action :authenticate_user!, only: [:show]

  def sign_in
    super()
  end

  def create
    # Direct user creation instead of using build_resource
    @user = User.new(sign_up_params)
    @user.save!

    # Send confirmation email except in test environment
    unless Rails.env.test?
      begin
        @user.send_confirmation_instructions
      rescue StandardError => e
        Rails.logger.error "Failed to send confirmation email: #{e.message}"
        # Continue with the signup process even if email delivery fails
      end
    end

    # Generate authentication token
    @token = generate_auth_token(@user)

    render json: {
      status: :success,
      data: @user,
      token: @token,
      message: 'Please check your email to confirm your account'
    }, status: :created
  rescue ActiveRecord::RecordInvalid => e
    handle_validation_error(e)
  end

  def sign_up
    create # Use the same implementation as create
  end

  def show
    render json: {
      status: :success,
      data: {
        user: current_user,
        token: request.headers['Authorization']&.split(' ')&.last,
        confirmed: current_user.confirmed?
      }
    }
  end

  # Add confirmation endpoint
  def confirm
    token = params[:confirmation_token]
    user = User.confirm_by_token(token)

    if user.errors.empty?
      # Generate a new token since the user is now confirmed
      @token = generate_auth_token(user)

      render json: {
        status: :success,
        message: 'Your account has been successfully confirmed',
        data: {
          user:,
          token: @token
        }
      }
    else
      render json: {
        status: :error,
        message: 'Confirmation token is invalid or has expired',
        errors: user.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  # Add resend confirmation endpoint
  def resend_confirmation
    email = params[:email]
    user = User.find_by(email:)

    if user
      if user.confirmed?
        render json: {
          status: :info,
          message: 'This account has already been confirmed'
        }
      else
        user.resend_confirmation_instructions
        render json: {
          status: :success,
          message: 'Confirmation instructions have been sent to your email'
        }
      end
    else
      render json: {
        status: :error,
        message: 'Email not found'
      }, status: :not_found
    end
  end

  # Password reset functionality
  def forgot_password
    user = User.find_by(email: params[:email])

    if user
      user.send_reset_password_instructions
      render json: {
        status: :success,
        message: 'Password reset instructions have been sent to your email'
      }
    else
      render json: {
        status: :error,
        message: 'Email not found'
      }, status: :not_found
    end
  end

  def reset_password
    token = params[:reset_password_token]
    password = params[:password]
    password_confirmation = params[:password_confirmation]

    user = User.reset_password_by_token(
      reset_password_token: token,
      password:,
      password_confirmation:
    )

    if user.errors.empty?
      # Generate token after password reset
      @token = generate_auth_token(user)

      render json: {
        status: :success,
        message: 'Your password has been changed successfully',
        data: {
          user:,
          token: @token
        }
      }
    else
      render json: {
        status: :error,
        message: 'Password reset failed',
        errors: user.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  private

  def generate_auth_token(user)
    TokenService.generate_token(user)
  end

  def handle_validation_error(exception)
    render json: {
      status: :error,
      message: 'Validation failed',
      errors: exception.record.errors.full_messages
    }, status: :unprocessable_entity
  end

  def sign_up_params
    params.require(:user).permit(:email, :password, :password_confirmation, :username)
  end
end
