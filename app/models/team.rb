# Team model

class Team < ApplicationRecord
  SOURCES = %w[football_data api_football ksi_soap].freeze
  COLOR_NAMES_TO_HEX = {
    'Red' => '#FF0000',
    'Blue' => '#0000FF',
    'Green' => '#008000',
    'Yellow' => '#FFFF00',
    'White' => '#FFFFFF',
    'Black' => '#000000',
    'Orange' => '#FFA500',
    'Purple' => '#800080',
    'Brown' => '#A52A2A',
    'Pink' => '#FFC0CB',
    'Grey' => '#808080',
    'Navy' => '#000080',
    'Maroon' => '#800000',
    'Lime' => '#00FF00',
    'Teal' => '#008080',
    'Aqua' => '#00FFFF',
    'Silver' => '#C0C0C0',
    'Gold' => '#FFD700',
    'Crimson' => '#DC143C',
    'Violet' => '#EE82EE',
    'Indigo' => '#4B0082',
    'Cyan' => '#00FFFF',
    'Magenta' => '#FF00FF',
    'Olive' => '#808000',
    'Sky Blue' => '#87CEEB',
    'Royal Blue' => '#4169E1',
    'Dark Blue' => '#00008B',
    'Light Blue' => '#ADD8E6',
    'Dark Green' => '#006400',
    'Light Green' => '#90EE90',
    'Dark Red' => '#8B0000',
    'Light Red' => '#FF6347',
    'Dark Yellow' => '#B8860B',
    'Light Yellow' => '#FFFFE0',
    'Dark Purple' => '#4B0082',
    'Light Purple' => '#DA70D6',
    'Dark Orange' => '#FF8C00',
    'Light Orange' => '#FFA07A',
    'Dark Grey' => '#A9A9A9',
    'Light Grey' => '#D3D3D3',
    # Custom color mappings to match the frontend
    'Claret' => '#7F1734',
    'Burgundy' => '#800020',
    'Amber' => '#FFBF00',
    'Navy Blue' => '#000080'
  }.freeze

  validates :source, inclusion: { in: SOURCES }
  validates :external_service_id, presence: true, uniqueness: { scope: :source }

  scope :from_source, ->(source) { where(source:) }

  belongs_to :area
  has_and_belongs_to_many :seasons
  has_many :home_matches, class_name: 'Match', foreign_key: 'home_team_id', dependent: :destroy
  has_many :away_matches, class_name: 'Match', foreign_key: 'away_team_id', dependent: :destroy
  has_many :favorite_teams
  has_many :fans, through: :favorite_teams, source: :user

  validates :name, :short_name, :tla, :venue, :crest_public_id, presence: true
  validates :name, :short_name, uniqueness: true
  # validate :last_five_matches_ids_length

  # Set default club_colors if not present
  before_save :ensure_club_colors_structure

  def form(season_id)
    # First, get the 5 most recent matches by date
    last_5_matches_ids = Match.where(home_team_id: id)
                              .or(Match.where(away_team_id: id))
                              .where(season_id:)
                              .where(status: 'FINISHED')
                              .order(utc_date: :desc)
                              .limit(5)
                              .pluck(:id)

    # Then, retrieve those specific matches in chronological order
    last_5_finished_matches = Match.where(id: last_5_matches_ids)
                                   .includes(:home_team, :away_team, :score)
                                   .order(utc_date: :asc)

    {
      string: form_string(last_5_finished_matches),
      matches: last_5_finished_matches.map do |match|
        {
          homeTeam: match.home_team.name,
          awayTeam: match.away_team.name,
          score: {
            home: match.score.fullTimeHome,
            away: match.score.fullTimeAway
          }
        }
      end
    }
  end

  # Get club colors for a specific type (home or away)
  # If type is not specified or the requested type is empty, returns home colors
  def get_club_colors(type = nil)
    return {} unless club_colors.present?

    colors = club_colors.with_indifferent_access
    requested_colors = type.present? ? colors[type] : nil

    # If the requested type is empty or not specified, return home colors
    if requested_colors.blank? || requested_colors.values.all?(&:blank?)
      colors[:home] || {}
    else
      requested_colors
    end
  end

  # Check if two colors are similar
  # Returns true if the colors are similar, false otherwise
  def colors_similar?(color1, color2)
    return false if color1.blank? || color2.blank?

    # Convert hex to RGB
    rgb1 = hex_to_rgb(color1)
    rgb2 = hex_to_rgb(color2)
    return false if rgb1.nil? || rgb2.nil?

    # Calculate color difference using Euclidean distance
    # Lower threshold means colors need to be more similar to return true
    distance = Math.sqrt(
      (rgb1[:r] - rgb2[:r])**2 +
      (rgb1[:g] - rgb2[:g])**2 +
      (rgb1[:b] - rgb2[:b])**2
    )

    # Consider colors similar if distance is less than 100 (out of 441.67 max possible)
    distance < 100
  end

  # Convert hex color to RGB
  def hex_to_rgb(hex)
    # Remove # if present
    hex = hex.gsub('#', '')
    return nil unless hex.match?(/^[0-9A-Fa-f]{6}$/)

    {
      r: hex[0..1].to_i(16),
      g: hex[2..3].to_i(16),
      b: hex[4..5].to_i(16)
    }
  end

  # Parse club colors from string format (e.g., "Red / White / Blue")
  # and convert to the new format with hex values
  def parse_club_colors_from_string(colors_string, type = :home)
    return {} if colors_string.blank?

    colors = colors_string.split(' / ')
    {
      type => {
        primary: color_name_to_hex(colors[0]),
        secondary: colors[1].present? ? color_name_to_hex(colors[1]) : nil,
        tertiary: colors[2].present? ? color_name_to_hex(colors[2]) : nil
      }.compact
    }
  end

  # Convert color name to hex value
  def color_name_to_hex(color_name)
    return nil if color_name.blank?

    # Try exact match first
    return COLOR_NAMES_TO_HEX[color_name] if COLOR_NAMES_TO_HEX[color_name]

    # Try case-insensitive match
    COLOR_NAMES_TO_HEX.each do |key, value|
      return value if key.downcase == color_name.downcase
    end

    # Try without spaces (e.g., 'NavyBlue' -> 'Navy Blue')
    COLOR_NAMES_TO_HEX.each do |key, value|
      return value if key.downcase.gsub(' ', '') == color_name.downcase.gsub(' ', '')
    end

    # Return original if no match found
    color_name
  end

  # Ensure club_colors has the correct structure
  def ensure_club_colors_structure
    self.club_colors ||= {}

    # Convert from string to hash if needed (for backward compatibility)
    self.club_colors = parse_club_colors_from_string(club_colors) if club_colors.is_a?(String) && club_colors.present?

    # Ensure the structure is correct
    colors = club_colors.is_a?(Hash) ? club_colors.with_indifferent_access : {}

    # Initialize home and away if they don't exist
    colors[:home] ||= {}
    colors[:away] ||= {}

    self.club_colors = colors
  end

  private

  # def last_five_matches_ids_length
  #   return unless last_five_matches_ids.length > 5
  #   errors.add(:last_five_matches_ids, "can't have more than 5 matches")
  # end

  def form_string(last_5_matches)
    last_5_matches.map do |match|
      if match.home_team_id == id
        form_string_for_home_team(match)
      elsif match.away_team_id == id
        form_string_for_away_team(match)
      else
        'D'
      end
    end.join('')
  end

  def form_string_for_home_team(match)
    if match.score.winner == 'HOME_TEAM'
      'W'
    elsif match.score.winner == 'AWAY_TEAM'
      'L'
    else
      'D'
    end
  end

  def form_string_for_away_team(match)
    if match.score.winner == 'AWAY_TEAM'
      'W'
    elsif match.score.winner == 'HOME_TEAM'
      'L'
    else
      'D'
    end
  end
end
