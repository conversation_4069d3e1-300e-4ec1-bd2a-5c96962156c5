# LeagueSeason model - stores user performance data for a specific league and season
class LeagueSeason < ApplicationRecord
  belongs_to :league
  belongs_to :season
  belongs_to :user

  validates :user_id, uniqueness: { scope: %i[league_id season_id],
                                    message: 'already has a record for this league and season' }

  # Default values for new records
  after_initialize :set_default_values, if: :new_record?

  # Calculate position based on points
  def self.calculate_positions_for_league_season(league_id, season_id)
    where(league_id:, season_id:)
      .order(points: :desc)
      .each_with_index do |record, index|
        record.update(position: index + 1)
      end
  end

  private

  def set_default_values
    self.points ||= 0
    self.correct_predictions ||= 0
    self.incorrect_predictions ||= 0
    self.perfect_predictions ||= 0
  end
end
