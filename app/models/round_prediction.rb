# RoundPrediction model
class RoundPrediction < ApplicationRecord
  has_many :match_predictions
  belongs_to :user
  validates :matchday, presence: true
  before_destroy { match_predictions.destroy_all }

  validates :matchday, :stage, presence: true
  validates :stage, inclusion: { in: %w[REGULAR_SEASON LEAGUE_STAGE PLAYOFFS LAST_16 QUARTER_FINALS SEMI_FINALS FINAL],
                                 message: '%<value>s is not a valid stage' }

  validates :matchday,
            uniqueness: { scope: %i[season_id user_id stage],
                          message: 'can only have one prediction per matchday and stage for each user' }

  def calculate_match_prediction_points
    match_predictions.each(&:calculate_points)
  end

  def total_points
    match_predictions.sum { |mp| mp.points || 0 }
  end

  def total_perfect
    match_predictions.count { |mp| mp.points == 3 }
  end

  def total_correct
    match_predictions.count { |mp| mp.points.nil? ? false : mp.points == 1 }
  end

  def zero_points_count
    match_predictions.count { |mp| mp.points.nil? ? false : mp.points.zero? }
  end
end
