# Score model

class Score < ApplicationRecord
  belongs_to :match

  validates :winner, inclusion: {
    in: %w[HOME_TEAM DRAW AWAY_TEAM],
    message: '%<value>s is not a valid winner'
  }, if: :full_time_scores_present?

  before_validation :calculate_winner, if: :should_calculate_winner?

  private

  def full_time_scores_present?
    fullTimeHome.present? && fullTimeAway.present?
  end

  def should_calculate_winner?
    full_time_scores_present? && !winner_changed?
  end

  def calculate_winner
    home_score = fullTimeHome.to_i
    away_score = fullTimeAway.to_i

    self.winner = if home_score > away_score
                    'HOME_TEAM'
                  elsif home_score < away_score
                    'AWAY_TEAM'
                  else
                    'DRAW'
                  end
  end
end
