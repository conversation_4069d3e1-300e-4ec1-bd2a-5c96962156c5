# app/models/league_join_request.rb
class LeagueJoinRequest < ApplicationRecord
  belongs_to :user
  belongs_to :league

  enum status: { pending: 0, accepted: 1, rejected: 2 }

  validates :user_id, uniqueness: { scope: :league_id,
                                    message: 'has already requested to join this league' }
  validate :user_league_limit, on: :create

  private

  def user_league_limit
    return unless user
    return unless user.memberships.count >= 5

    errors.add(:user, 'is already a member of 5 leagues')
  end
end
