# Season model

class Season < ApplicationRecord
  SOURCES = %w[football_data api_football ksi_soap].freeze

  validates :source, inclusion: { in: SOURCES }
  validates :external_service_id, presence: true, uniqueness: { scope: :source }, unless: -> { source == 'ksi_soap' }

  scope :from_source, ->(source) { where(source:) }

  belongs_to :competition
  has_and_belongs_to_many :teams
  has_many :matches, dependent: :destroy
  validates :current_matchday, :start_date, :end_date, presence: true
  before_create :set_default_stage
  validate :end_date_after_start_date
  #  It needs to happen here if possible to avoid circular dependencies
  #  When season model is needed or requested check if its cup competition and then check what stage and matchday. if last matchday of a stage and all matches are finished then call external service and check if matches in next stage are with status TIMED. If yes then update current stage to next stage and matchday to 1. When all matches are finished but no TIMED matches in next stage then just we need to indicate to FE that all matches are finished and we are waiting for next stage to start. # rubocop:disable Layout/LineLength

  # Competition-specific stages
  # REGULAR_SEASON is for league competition types rest for cup competition.
  enum current_stage: {
    REGULAR_SEASON: 'REGULAR_SEASON',
    LEAGUE_STAGE: 'LEAGUE_STAGE',
    PLAYOFFS: 'PLAYOFFS',
    LAST_16: 'LAST_16',
    QUARTER_FINALS: 'QUARTER_FINALS',
    SEMI_FINALS: 'SEMI_FINALS',
    FINAL: 'FINAL'
  }

  CL_LEAGUE_STAGE_MATCHES = 8 # We can make this configurable later
  CL_KNOCKOUT_STAGES_MATCHES = 9 # We can make this configurable later

  def match_days
    return CL_LEAGUE_STAGE_MATCHES + CL_KNOCKOUT_STAGES_MATCHES if competition&.competition_type == 'CUP'

    teams_count = teams.count

    # When there are no teams, it means the season is not properly set up yet
    return 0 if teams_count.zero?

    # For a league with n teams:
    # - Each team plays against every other team twice (home and away)
    # - Total matches = n * (n-1)
    teams_count * (teams_count - 1)
  end

  def update_matchday_if_complete
    return unless all_matches_complete?

    if competition&.competition_type == 'CUP'
      cup_matchday_service = CupMatchdayService.new(self)
      current_stage_total = cup_matchday_service.current_stage_total_matchdays

      if current_matchday < current_stage_total
        update(current_matchday: current_matchday + 1)
      else
        check_stage_transition
      end
    else
      update(current_matchday: current_matchday + 1)
    end
  end

  def overall_matchday
    return current_matchday unless competition&.competition_type == 'CUP'

    CupMatchdayService.new(self).calculate_overall_matchday
  end

  def check_stage_transition
    cache_key = "season_#{id}_stage_check"
    last_check = Rails.cache.read(cache_key)

    return unless !last_check || Time.current - last_check > 1.hour

    StageTransitionService.new(self).update_stage_and_matchday!
    Rails.cache.write(cache_key, Time.current, expires_in: 1.day)
  end

  def current_matchday
    self[:current_matchday]
  end

  def calculate_stage_matchday
    stage_matchdays = matches.where(stage: current_stage).group(:matchday).count

    # Handle empty stage_matchdays case
    return 1 if stage_matchdays.empty?

    stage_matchdays.keys.sort.each do |matchday|
      return matchday unless matches_finished_for_stage_and_matchday?(current_stage, matchday)
    end

    # Safety check to prevent nil + 1 error
    max_matchday = stage_matchdays.keys.max
    return 1 if max_matchday.nil?

    max_matchday + 1
  end

  def matches_finished_for_stage_and_matchday?(stage, matchday)
    matches.where(stage:, matchday:).all? { |match| %w[FINISHED POSTPONED CANCELLED].include?(match.status) }
  end

  # Check if all matches for the season are completed
  def all_season_matches_completed?
    matches.where.not(status: %w[FINISHED POSTPONED CANCELLED]).count.zero?
  end

  # Check if the season is over (end date passed and all matches completed)
  def season_over?
    return true if completed?

    end_date_passed = end_date.present? && end_date < Date.current
    end_date_passed && all_season_matches_completed?
  end

  # Mark the season as completed
  def mark_as_completed!
    return if completed?

    update!(completed: true)
  end

  # Check if the season should be marked as completed
  def check_if_completed
    mark_as_completed! if season_over? && !completed?
  end

  private

  def all_matches_complete?
    matches.where(matchday: current_matchday).all? do |match|
      %w[FINISHED POSTPONED CANCELLED].include?(match.status)
    end
  end

  def set_default_stage
    comp_type = competition&.competition_type
    self.current_stage = (comp_type == 'CUP' ? 'LEAGUE_STAGE' : 'REGULAR_SEASON')
  end

  def end_date_after_start_date
    return if end_date.blank? || start_date.blank?

    errors.add(:end_date, 'must be after the start date') if end_date <= start_date
  end
end
