# Competition model

class Competition < ApplicationRecord
  SOURCES = %w[football_data api_football ksi_soap].freeze

  # Color name to hex mapping
  COLOR_NAMES_TO_HEX = {
    'Red' => '#FF0000',
    'Blue' => '#0000FF',
    'Green' => '#008000',
    'Yellow' => '#FFFF00',
    'White' => '#FFFFFF',
    'Black' => '#000000',
    'Orange' => '#FFA500',
    'Purple' => '#800080',
    'Brown' => '#A52A2A',
    'Pink' => '#FFC0CB',
    'Grey' => '#808080',
    'Navy' => '#000080',
    'Maroon' => '#800000',
    'Lime' => '#00FF00',
    'Teal' => '#008080',
    'Aqua' => '#00FFFF',
    'Silver' => '#C0C0C0',
    'Gold' => '#FFD700',
    'Crimson' => '#DC143C',
    'Violet' => '#EE82EE',
    'Indigo' => '#4B0082',
    'Cyan' => '#00FFFF',
    'Magenta' => '#FF00FF',
    'Olive' => '#808000',
    'Sky Blue' => '#87CEEB',
    'Royal Blue' => '#4169E1',
    'Dark Blue' => '#00008B',
    'Light Blue' => '#ADD8E6',
    'Dark Green' => '#006400',
    'Light Green' => '#90EE90',
    'Dark Red' => '#8B0000',
    'Light Red' => '#FF6347',
    'Dark Yellow' => '#B8860B',
    'Light Yellow' => '#FFFFE0',
    'Dark Purple' => '#4B0082',
    'Light Purple' => '#DA70D6',
    'Dark Orange' => '#FF8C00',
    'Light Orange' => '#FFA07A',
    'Dark Grey' => '#A9A9A9',
    'Light Grey' => '#D3D3D3',
    # Custom color mappings
    'Claret' => '#7F1734',
    'Burgundy' => '#800020',
    'Amber' => '#FFBF00',
    'Navy Blue' => '#000080'
  }.freeze

  belongs_to :area
  has_many :seasons, dependent: :destroy
  has_many :matches, through: :seasons, dependent: :destroy
  has_many :favorite_teams
  belongs_to :current_season, class_name: 'Season', optional: true
  belongs_to :current_winner, class_name: 'Team', optional: true

  validates :name, :code, :emblem_public_id, :gender, :competition_type, presence: true
  validates :code, uniqueness: true
  validates :gender, inclusion: { in: %w[male female],
                                  message: '%<value>s is not a valid gender, use either male or female' }
  validates :competition_type, inclusion: { in: %w[LEAGUE CUP],
                                            message: '%<value>s is not a valid type, use either LEAGUE or CUP' }
  validates :source, inclusion: { in: SOURCES }
  validates :external_service_id, uniqueness: { scope: :source }, unless: -> { source == 'ksi_soap' }
  validates :external_service_id, presence: true, unless: -> { source == 'ksi_soap' }

  before_destroy :clear_current_season_reference
  before_save :ensure_colors_structure

  scope :from_source, ->(source) { where(source:) }

  def current_winner=(team_id)
    self.current_winner_id = team_id
  end

  # Get colors for the competition
  # Returns a hash with primary, secondary, and optional tertiary colors
  def get_colors
    return {} unless colors.present?

    colors_hash = colors.with_indifferent_access
    colors_hash.presence || {}
  end

  # Convert color name to hex value
  def color_name_to_hex(color_name)
    return nil if color_name.blank?

    # Try exact match first
    return COLOR_NAMES_TO_HEX[color_name] if COLOR_NAMES_TO_HEX[color_name]

    # Try case-insensitive match
    COLOR_NAMES_TO_HEX.each do |key, value|
      return value if key.downcase == color_name.downcase
    end

    # Try without spaces (e.g., 'NavyBlue' -> 'Navy Blue')
    COLOR_NAMES_TO_HEX.each do |key, value|
      return value if key.downcase.gsub(' ', '') == color_name.downcase.gsub(' ', '')
    end

    # Return original if no match found
    color_name
  end

  private

  # Ensure colors has the correct structure
  def ensure_colors_structure
    self.colors ||= {}

    # Ensure the structure is correct
    colors_hash = colors.is_a?(Hash) ? colors.with_indifferent_access : {}

    # Initialize primary, secondary, tertiary if they don't exist
    colors_hash[:primary] ||= nil
    colors_hash[:secondary] ||= nil
    colors_hash[:tertiary] ||= nil

    self.colors = colors_hash.compact
  end

  def clear_current_season_reference
    update_column(:current_season_id, nil)
  end
end
