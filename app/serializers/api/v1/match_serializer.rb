module Api
  module V1
    class MatchSerializer < ActiveModel::Serializer
      attributes :id, :home_team, :away_team, :utc_date, :matchday, :status, :referee, :score, :venue

      def home_team
        team = Team.find_by(id: object.home_team_id)
        team_response(team, true)
      end

      def away_team
        team = Team.find_by(id: object.away_team_id)
        team_response(team, false)
      end

      def team_response(team, home_team)
        if team
          # For matches, use home colors for home team
          # For away team, check if primary colors are similar
          colors_type = if home_team
                          'home'
                        else
                          determine_away_team_colors_type(team)
                        end

          {
            name: team.name,
            crest_public_id: team.crest_public_id,
            shortName: team.short_name,
            tla: team.tla,
            form: team.form(object.season_id),
            venue: home_team ? team.venue : nil,
            club_colors: team.get_club_colors(colors_type)
          }
        else
          {
            name: nil,
            crest_public_id: nil,
            shortName: nil,
            tla: nil,
            form: nil,
            venue: nil,
            club_colors: nil
          }
        end
      end

      # Determine which colors to use for away team
      # If away team's away primary color is similar to home team's primary color,
      # use away team's home colors instead
      def determine_away_team_colors_type(away_team)
        home_team = Team.find_by(id: object.home_team_id)
        return 'away' unless home_team # Default to away if home team not found

        # Get primary colors
        home_primary = home_team.get_club_colors('home')[:primary]
        away_primary = away_team.get_club_colors('away')[:primary]

        # If away team's away primary color is similar to home team's primary color,
        # use away team's home colors
        if away_team.colors_similar?(home_primary, away_primary)
          'home'
        else
          'away'
        end
      end

      def venue
        object.venue || home_team[:venue]
      end

      def score
        if object.score
          {
            duration: object.score.duration,
            fullTimeAway: object.score.fullTimeAway,
            fullTimeHome: object.score.fullTimeHome,
            winner: object.score.winner
          }
        else
          {
            duration: nil,
            fullTimeAway: nil,
            fullTimeHome: nil,
            winner: nil
          }
        end
      end
    end
  end
end
