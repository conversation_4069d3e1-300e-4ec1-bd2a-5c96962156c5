module Api
  module V1
    class LeagueIndexSerializer < ActiveModel::Serializer
      # TODO: go over whats needed in this serializer
      attributes :id, :competition, :name, :open, :users, :owner, :archived, :archived_at,
                 :youtube_league, :subscriber_only, :youtube_channel_info

      def users
        object.users.length
      end

      def owner
        User.find(object.owner_id).username
      end

      def competition
        competition = Competition.find(object.competition_id)
        {
          id: competition.id,
          name: competition.name,
          emblem_public_id: competition.emblem_public_id,
          country: competition.area.name
        }
      end

      def youtube_channel_info
        return nil unless object.youtube_league? && object.youtube_channel_id.present?

        # Try to get channel info from the owner
        owner = User.find_by(id: object.owner_id)
        if owner.youtube_channel_id == object.youtube_channel_id
          return {
            id: owner.youtube_channel_id,
            name: owner.youtube_channel_name
          }
        end

        # If owner's channel doesn't match or info is missing, return minimal info
        { id: object.youtube_channel_id }
      end
    end
  end
end
