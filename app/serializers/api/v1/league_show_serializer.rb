include Rails.application.routes.url_helpers

module Api
  module V1
    class LeagueShowSerializer < ActiveModel::Serializer
      attributes :id, :competition, :name, :open, :owner, :starting_matchday, :archived, :archived_at,
                 :season, :historical_seasons, :youtube_league, :youtube_channel_id, :subscriber_only,
                 :min_subscriber_date, :subscriber_requirement_type, :unsubscribe_policy, :youtube_channel_info
      has_many :users, through: :memberships, only: %i[id username]

      def starting_matchday
        object.starting_matchday || 1
      end

      def owner
        owner = User.find_by(id: object.owner_id)
        {
          name: owner.username,
          id: owner.id
        }
      end

      def competition
        competition = Competition.find(object.competition_id)
        current_season = competition.current_season

        competition_data = {
          id: competition.id,
          name: competition.name,
          emblem_public_id: competition.emblem_public_id,
          country: competition.area.name
        }

        if current_season.present?
          competition_data[:current_season] = {
            id: current_season.id,
            current_matchday: current_season.current_matchday
          }
        end

        competition_data
      end

      def season
        return nil unless object.season_id.present?

        season = Season.find(object.season_id)
        {
          id: season.id,
          start_date: season.start_date,
          end_date: season.end_date,
          current_matchday: season.current_matchday
        }
      end

      def historical_seasons
        # Get all seasons for this league from league_seasons
        league_seasons = object.league_seasons
                               .select('DISTINCT ON (season_id) season_id')
                               .includes(:season)
                               .where.not(season_id: object.season_id)
                               .order(:season_id)

        league_seasons.map do |ls|
          {
            id: ls.season_id,
            start_date: ls.season.start_date,
            end_date: ls.season.end_date,
            standings_url: api_v1_league_table_index_path(object.id, season_id: ls.season_id)
          }
        end
      end

      def youtube_channel_info
        return nil unless object.youtube_league? && object.youtube_channel_id.present?

        # Try to get channel info from the owner
        owner = User.find_by(id: object.owner_id)
        if owner.youtube_channel_id == object.youtube_channel_id
          return {
            id: owner.youtube_channel_id,
            name: owner.youtube_channel_name,
            avatar_url: owner.youtube_avatar_url,
            subscriber_count: owner.youtube_subscriber_count
          }
        end

        # If owner's channel doesn't match or info is missing, fetch from YouTube API
        begin
          channel_info = BragRightsYouTubeService.new.get_channel_info(object.youtube_channel_id)
          return nil unless channel_info

          {
            id: channel_info[:id],
            name: channel_info[:title],
            avatar_url: channel_info[:thumbnail_url],
            subscriber_count: channel_info[:subscriber_count]
          }
        rescue StandardError => e
          Rails.logger.error "Error fetching YouTube channel info: #{e.message}"
          nil
        end
      end
    end
  end
end
