module Api
  module V1
    class UserShowSerializer < ActiveModel::Serializer
      attributes :id, :email, :username, :admin, :leagues, :favorite_teams

      def leagues
        object.leagues.select(:id, :name, :competition_id).map do |league|
          competition = Competition.find(league.competition_id)
          {
            id: league.id,
            name: league.name,
            competitionName: competition.name,
            competitionId: competition.id
          }
        end
      end

      def favorite_teams
        object.favorite_teams.includes(:team, :competition).map do |favorite|
          {
            id: favorite.id,
            team: {
              id: favorite.team.id,
              name: favorite.team.name,
              short_name: favorite.team.short_name,
              tla: favorite.team.tla,
              crest_public_id: favorite.team.crest_public_id,
              club_colors: favorite.team.get_club_colors('home') # For favorite teams, return home colors
            },
            competition: {
              id: favorite.competition.id,
              name: favorite.competition.name,
              emblem_public_id: favorite.competition.emblem_public_id
            }
          }
        end
      end
    end
  end
end
