module Api
  module V1
    class CompetitionSerializer < ActiveModel::Serializer
      attributes :id, :code, :name, :emblem_public_id,
                 :gender, :area, :current_season,
                 :current_winners, :type, :colors

      def area
        {
          id: object.area.id,
          name: object.area.name,
          flag_public_id: object.area.flag_public_id
        }
      end

      def current_season
        season = object.current_season
        season.check_stage_transition if season.present?

        {
          id: season.id,
          currentMatchday: season.overall_matchday,
          stageMatchday: season.current_matchday,
          startDate: season.start_date,
          endDate: season.end_date,
          matchDays: season.match_days,
          stage: season.current_stage
        }
      end

      def current_winners
        winner = Team.find_by(id: object.current_winner_id)

        if winner.nil?
          {
            id: nil,
            name: nil,
            tla: nil,
            shortName: nil,
            crest_public_id: nil
          }
        else
          {
            id: winner.id,
            name: winner.name,
            tla: winner.tla,
            shortName: winner.short_name,
            crest_public_id: winner.crest_public_id
          }
        end
      end

      def type
        object.competition_type
      end

      def colors
        object.get_colors
      end
    end
  end
end
