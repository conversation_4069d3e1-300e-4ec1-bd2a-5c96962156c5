module Api
  module V1
    class RoundPredictionSerializer < ActiveModel::Serializer
      attributes :id, :matchday, :season_id, :competition_id, :match_predictions, :stage

      def match_predictions
        object.match_predictions.map do |match_prediction|
          {
            id: match_prediction.id,
            away_score: match_prediction.away_score,
            home_score: match_prediction.home_score,
            match_id: match_prediction.match_id,
            points: match_prediction.points
          }
        end
      end
    end
  end
end
