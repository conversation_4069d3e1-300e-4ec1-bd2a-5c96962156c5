module Api
  module V1
    class LeagueStandingSerializer < ActiveModel::Serializer
      attributes :id, :name, :season, :standings

      def id
        object.id
      end

      def name
        object.name
      end

      def season
        return nil unless object.season_id.present?

        season = Season.find(object.season_id)
        {
          id: season.id,
          start_date: season.start_date,
          end_date: season.end_date,
          current_matchday: season.current_matchday
        }
      end

      def standings
        object.calculate_standings
      end
    end
  end
end
