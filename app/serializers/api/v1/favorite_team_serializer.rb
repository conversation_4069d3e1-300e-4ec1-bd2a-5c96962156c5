# app/serializers/api/v1/favorite_team_serializer.rb

module Api
  module V1
    class FavoriteTeamSerializer < ActiveModel::Serializer
      attributes :id

      belongs_to :competition
      belongs_to :team

      def competition
        {
          id: object.competition.id,
          name: object.competition.name,
          emblem_public_id: object.competition.emblem_public_id
        }
      end

      def team
        {
          id: object.team.id,
          name: object.team.name,
          short_name: object.team.short_name,
          tla: object.team.tla,
          crest_public_id: object.team.crest_public_id,
          club_colors: object.team.get_club_colors('home') # For favorite teams, return home colors
        }
      end
    end
  end
end
