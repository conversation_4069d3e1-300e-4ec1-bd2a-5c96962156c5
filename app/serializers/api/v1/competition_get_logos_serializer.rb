include Rails.application.routes.url_helpers

module Api
  module V1
    class CompetitionGetLogosSerializer < ActiveModel::Serializer
      attributes :code, :name, :emblem, :teams

      def teams
        object.current_season.teams.map do |team|
          {
            name: team.name,
            crest_public_id: team.crest_public_id
          }
        end
      end

      def emblem
        url_for(object.emblem_public_id)
      end
    end
  end
end
