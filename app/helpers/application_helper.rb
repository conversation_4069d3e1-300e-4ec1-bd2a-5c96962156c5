module ApplicationHelper
  # Add common helper methods that might be needed in mailers
  def app_name
    'Brag Rights'
  end

  def app_email
    '<EMAIL>'
  end

  def app_url
    Rails.env.production? ? 'https://bragrights.football' : 'http://localhost:3000'
  end

  # Generate frontend confirmation URL
  def frontend_confirmation_url(token)
    base_url = Rails.env.production? ? 'https://bragrights.football' : 'http://localhost:3000'
    "#{base_url}/verify-email?token=#{token}"
  end

  # Generate frontend password reset URL
  def frontend_reset_password_url(token)
    base_url = Rails.env.production? ? 'https://bragrights.football' : 'http://localhost:3000'
    "#{base_url}/reset-password?token=#{token}"
  end
end
