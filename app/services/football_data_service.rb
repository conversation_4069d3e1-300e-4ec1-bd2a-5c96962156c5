require 'httparty'
require 'open-uri'

class FootballDataService # rubocop:disable Metrics/ClassLength
  class ApiError < StandardError; end

  def initialize
    @api_key = ENV['FOOTBALL_API_KEY']
    @base_url = 'https://api.football-data.org/v4'
    @cloudinary_service = CloudinaryService.new

    # Ensure API key is available
    return unless @api_key.blank?

    Rails.logger.error 'FOOTBALL_API_KEY environment variable is missing'
    raise ApiError, 'Missing required API key. Please configure FOOTBALL_API_KEY in your environment.'
  end

  def import_competition(competition_code)
    Rails.logger.info "Starting import for competition code: #{competition_code}"
    competition_data = fetch_competition(competition_code)

    unless competition_data
      Rails.logger.error "No data returned for competition code: #{competition_code}"
      raise ApiError, "Failed to fetch competition data for code: #{competition_code}"
    end

    ActiveRecord::Base.transaction do
      competition = update_or_create_competition(competition_data)
      if competition.persisted?
        import_teams_for_competition(competition, competition_code)
        update_matches(competition, competition.current_season)
        update_stage_transition(competition.current_season)
      else
        Rails.logger.error "Failed to persist competition: #{competition.errors.full_messages.join(', ')}"
        raise ApiError, "Failed to save competition: #{competition.errors.full_messages.join(', ')}"
      end
      competition
    rescue StandardError => e
      Rails.logger.error "Transaction failed in import_competition: #{e.class} - #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise ApiError, "Failed during competition import: #{e.message}"
    end
  rescue StandardError => e
    Rails.logger.error "Failed to import competition: #{e.class} - #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    raise ApiError, "Failed to import competition: #{e.message}"
  end

  def create_new_season_for_competition(competition)
    Rails.logger.info "Creating new season for competition: #{competition.code}"

    # Fetch the latest competition data to get the current season
    competition_data = fetch_competition(competition.code)

    unless competition_data && competition_data['currentSeason']
      Rails.logger.error "No current season data returned for competition: #{competition.code}"
      raise ApiError, "Failed to fetch current season data for competition: #{competition.code}"
    end

    # Check if the season already exists
    existing_season = Season.find_by(
      external_service_id: competition_data['currentSeason']['id'],
      source: 'football_data'
    )

    if existing_season && competition.current_season_id == existing_season.id
      Rails.logger.info "Season already exists and is set as current season for competition: #{competition.code}"
      return existing_season
    end

    ActiveRecord::Base.transaction do
      # Create the new season
      new_season = create_season(competition_data['currentSeason'], competition.id)

      # Update the competition to use the new season
      competition.update!(current_season_id: new_season.id)

      # Import teams for the new season
      import_teams_for_competition(competition, competition.code)

      # Update matches for the new season
      update_matches(competition, new_season)

      # Update stage transition for the new season
      update_stage_transition(new_season)

      # Transition leagues to the new season
      transition_leagues_to_new_season(competition, new_season)

      new_season
    rescue StandardError => e
      Rails.logger.error "Transaction failed in create_new_season_for_competition: #{e.class} - #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise ApiError, "Failed during new season creation: #{e.message}"
    end
  rescue StandardError => e
    Rails.logger.error "Failed to create new season: #{e.class} - #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    raise ApiError, "Failed to create new season: #{e.message}"
  end

  def fetch_competition(competition_code)
    response = HTTParty.get(
      "#{@base_url}/competitions/#{competition_code}",
      headers: { 'X-Auth-Token' => @api_key }
    )

    result = handle_response(response)

    if result.nil?
      Rails.logger.error "API returned no data for competition #{competition_code}. Response code: #{response.code}"
    end

    result
  end

  def update_matches_matchday(competition, match_day, stage, season)
    api_response = FootballDataMatchesService.new.fetch_matches(competition.code, match_day, stage)
    update_matches_from_api(api_response['matches'], season)

    return unless competition.competition_type == 'LEAGUE'
    # For leagues, just check if we should advance the matchday
    return unless match_day == season.current_matchday

    matches = season.matches.where(matchday: match_day)
    return unless matches.all? { |match| %w[FINISHED POSTPONED].include?(match.status) }

    season.update!(current_matchday: match_day + 1)
  end

  def update_matches_by_date_range(competition, start_date, end_date, season)
    matches_service = FootballDataMatchesService.new

    # Format dates as required by the API (YYYY-MM-DD)
    formatted_start_date = start_date.strftime('%Y-%m-%d')
    formatted_end_date = end_date.strftime('%Y-%m-%d')

    response = matches_service.fetch_matches_by_date_range(
      competition.code,
      formatted_start_date,
      formatted_end_date
    )

    return unless response.success?

    data = JSON.parse(response.body)
    match_data_updates = data['matches'] || []

    match_data_updates.each do |match_data|
      update_or_create_match(match_data, season)
    end

    # Only update stage transition if any matches were updated and it's a cup competition
    return unless match_data_updates.any? && competition.competition_type == 'CUP'

    # Check if any of the updated matches are finished
    finished_matches = match_data_updates.any? { |m| m['status'] == 'FINISHED' }
    update_stage_transition(season, force_refresh: true) if finished_matches
  end

  def update_all_matches(competition, season)
    update_matches(competition, season)
  end

  def update_matches(competition, season)
    data = fetch_matches(competition.code)

    data['matches'].each do |match_data|
      update_or_create_match(match_data, season)
    end

    # Only update stage transition if it's a cup competition and there are finished matches
    return unless competition.competition_type == 'CUP' && data['matches'].any? { |m| m['status'] == 'FINISHED' }

    update_stage_transition(season, force_refresh: true)
  end

  def import_teams_for_competition(competition, competition_code)
    response = HTTParty.get(
      "#{@base_url}/competitions/#{competition_code}/teams",
      headers: { 'X-Auth-Token' => @api_key }
    )

    data = handle_response(response)
    return unless data && data['teams']

    data['teams'].each do |team_data|
      create_team(team_data, competition)
    end
  end

  def create_team(data, competition)
    team = Team.find_or_initialize_by(external_service_id: data['id'], source: 'football_data')

    # Upload crest to Cloudinary
    crest_public_id = nil
    begin
      crest_file = URI.open(data['crest'])
      crest_public_id = @cloudinary_service.upload_image(
        crest_file,
        folder: 'teams/crests'
      )
    rescue OpenURI::HTTPError => e
      Rails.logger.error "Failed to download team crest: #{e.message}"
    end

    team.assign_attributes(
      name: data['name'],
      short_name: data['shortName'],
      tla: data['tla'],
      crest_public_id:,
      area_id: create_area(data['area']).id,
      founded: data['founded'],
      venue: data['venue'],
      website: data['website'],
      club_colors: parse_club_colors(data['clubColors']),
      source: 'football_data',
      external_service_id: data['id']
    )

    team.save!

    competition.current_season.teams << team unless competition.current_season.teams.include?(team)
    team
  end

  private

  def parse_club_colors(colors_string)
    return { home: {}, away: {} } if colors_string.blank?

    colors = colors_string.split(' / ')
    {
      home: {
        primary: color_name_to_hex(colors[0]),
        secondary: colors[1].present? ? color_name_to_hex(colors[1]) : nil,
        tertiary: colors[2].present? ? color_name_to_hex(colors[2]) : nil
      }.compact,
      away: {}
    }
  end

  def color_name_to_hex(color_name)
    return nil if color_name.blank?

    # Try exact match first
    return Team::COLOR_NAMES_TO_HEX[color_name] if Team::COLOR_NAMES_TO_HEX[color_name]

    # Try case-insensitive match
    Team::COLOR_NAMES_TO_HEX.each do |key, value|
      return value if key.downcase == color_name.downcase
    end

    # Try without spaces (e.g., 'NavyBlue' -> 'Navy Blue')
    Team::COLOR_NAMES_TO_HEX.each do |key, value|
      return value if key.downcase.gsub(' ', '') == color_name.downcase.gsub(' ', '')
    end

    # Return original if no match found
    color_name
  end

  def fetch_matches(competition_code)
    response = HTTParty.get(
      "#{@base_url}/competitions/#{competition_code}/matches",
      headers: { 'X-Auth-Token' => @api_key }
    )

    handle_response(response)
  end

  def update_or_create_competition(data)
    competition = Competition.find_by(external_service_id: data['id'], source: 'football_data')

    emblem_public_id = handle_emblem_upload(data['emblem']) || 'default_emblem_id'
    area = create_area(data['area'])

    # Make sure to enforce CUP type for Champions League (CL)
    competition_type = data['code'] == 'CL' ? 'CUP' : data['type'] || 'default_type'

    attributes = {
      name: data['name'],
      code: data['code'],
      competition_type:,
      gender: 'male',
      emblem_public_id:,
      area_id: area.id,
      source: 'football_data',
      external_service_id: data['id']
    }

    if competition
      competition.update!(attributes)
    else
      competition = Competition.create!(attributes)
    end

    if data['currentSeason']
      season = create_season(data['currentSeason'], competition.id)
      competition.update!(current_season_id: season.id)
    end

    competition
  end

  def create_area(data)
    return nil unless data

    area = Area.find_or_initialize_by(external_service_id: data['id'], source: 'football_data')

    # Upload flag to Cloudinary if present
    flag_public_id = nil
    if data['flag']
      begin
        flag_file = URI.open(data['flag'])
        flag_public_id = @cloudinary_service.upload_image(
          flag_file,
          folder: 'areas/flags'
        )
      rescue OpenURI::HTTPError => e
        Rails.logger.error "Failed to download area flag: #{e.message}"
      end
    end

    area.assign_attributes(
      name: data['name'],
      code: data['code'],
      flag_public_id:
    )

    area.save!

    area
  end

  def create_season(data, competition_id)
    return nil unless data && competition_id

    season = Season.find_or_initialize_by(external_service_id: data['id'], source: 'football_data')
    season.assign_attributes(
      start_date: data['startDate'],
      end_date: data['endDate'],
      current_matchday: data['currentMatchday'],
      winner_id: data['winner']&.dig('id'),
      competition_id:, # Fixed: properly setting the competition_id parameter
      source: 'football_data'
    )

    season.save!
    season
  end

  def transition_leagues_to_new_season(competition, new_season)
    # Create a service to transition leagues to the new season
    service = LeagueSeasonTransitionService.new(competition, new_season)

    # Check if transition is needed
    return 0 unless service.new_season?

    # Perform the transition (this will also archive leagues from the old season)
    transitioned_count = service.transition_leagues_to_new_season

    Rails.logger.info "Transitioned #{transitioned_count} leagues to new season #{new_season.id} for competition #{competition.code}"

    transitioned_count
  end

  def update_matches_from_api(matches, season)
    matches.each do |api_match|
      next if api_match['homeTeam']['id'].blank? || api_match['awayTeam']['id'].blank?

      update_or_create_match(api_match, season)
    end
  end

  def update_or_create_match(match_data, season)
    stages = %w[REGULAR_SEASON LEAGUE_STAGE PLAYOFFS LAST_16 QUARTER_FINALS SEMI_FINALS FINAL]
    return unless stages.include?(match_data['stage'])

    Match.find_or_initialize_by(external_service_id: match_data['id'], source: 'football_data').tap do |match|
      update_match_attributes(match, match_data, season)
    end
  end

  def update_match_attributes(match, match_data, season)
    if match_data['homeTeam']['id'].nil? || match_data['awayTeam']['id'].nil?
      # Create match with minimal data for future updates
      match_without_teams(match, match_data, season)
    else
      # Regular match update with teams
      home_team = Team.find_by(external_service_id: match_data['homeTeam']['id'])
      away_team = Team.find_by(external_service_id: match_data['awayTeam']['id'])

      raise ApiError, "Home or Away team not found for match #{match_data['id']}" unless home_team && away_team

      match_with_teams(match, match_data, season, home_team, away_team)
    end

    update_or_create_score(match, match_data['score'])
  end

  def match_with_teams(match, match_data, season, home_team, away_team)
    match.update!(
      season:,
      matchday: match_data['matchday'],
      status: match_data['status'],
      utc_date: match_data['utcDate'],
      home_team_id: home_team&.id,
      away_team_id: away_team&.id,
      stage: match_data['stage'],
      referee: match_data['referees'].first&.dig('name')
    )
  end

  def match_without_teams(match, match_data, season)
    match.update!(
      season:,
      matchday: match_data['matchday'],
      status: match_data['status'],
      utc_date: match_data['utcDate'],
      stage: match_data['stage'],
      referee: match_data['referees'].first&.dig('name')
    )
  end

  def update_or_create_score(match, score_data)
    return unless score_data # Skip if score_data is nil

    # Initialize default values
    full_time = score_data['fullTime'] || {}
    half_time = score_data['halfTime'] || {}

    # Skip if no scores are available
    return if full_time['home'].nil? && full_time['away'].nil?

    # Calculate winner if not provided or if it's invalid
    winner = calculate_winner(full_time['home'], full_time['away']) || score_data['winner']

    # Create or update the score with safe values
    Score.find_or_initialize_by(match:).tap do |score|
      score.update!(
        duration: score_data['duration'],
        fullTimeHome: full_time['home'],
        fullTimeAway: full_time['away'],
        halfTimeHome: half_time['home'],
        halfTimeAway: half_time['away'],
        winner:
      )
    end
  end

  def calculate_winner(home_score, away_score)
    return nil if home_score.nil? || away_score.nil?

    home_score = home_score.to_i
    away_score = away_score.to_i

    if home_score > away_score
      'HOME_TEAM'
    elsif home_score < away_score
      'AWAY_TEAM'
    else
      'DRAW'
    end
  end

  def update_stage_transition(season, force_refresh: false)
    return unless season.competition.competition_type == 'CUP'

    cache_key = "season_#{season.id}_stage_transition"

    if force_refresh
      # Force a refresh by bypassing the cache
      Rails.logger.info "Forcing refresh of stage transition for season #{season.id}"
      StageTransitionService.new(season).update_stage_and_matchday!
      Rails.cache.write(cache_key, Time.current, expires_in: 1.day)
    else
      # Use the cache as before
      Rails.cache.fetch(cache_key, expires_in: 1.day) do
        StageTransitionService.new(season).update_stage_and_matchday!
      end
    end
  end

  def handle_response(response)
    case response.code
    when 200
      JSON.parse(response.body)
    when 404
      Rails.logger.error "API returned 404: #{response.body}"
      nil
    when 403
      Rails.logger.error "API returned 403 Forbidden: #{response.body}"
      raise ApiError, 'Access forbidden. Check your API key and permissions.'
    when 429
      Rails.logger.error "API rate limit exceeded: #{response.body}"
      raise ApiError, 'API rate limit exceeded. Try again later.'
    else
      Rails.logger.error "API request failed with status #{response.code}: #{response.body}"
      raise ApiError, "API request failed with status #{response.code}: #{response.body}"
    end
  end

  def handle_emblem_upload(emblem_url)
    return nil unless emblem_url

    begin
      emblem_file = URI.open(emblem_url)
      @cloudinary_service.upload_image(
        emblem_file,
        folder: 'competitions/emblems'
      )
    rescue OpenURI::HTTPError => e
      Rails.logger.error "Failed to download competition emblem: #{e.message}"
      nil
    end
  end
end
