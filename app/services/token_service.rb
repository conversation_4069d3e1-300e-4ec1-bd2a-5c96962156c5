require 'jwt'

class TokenService
  def self.generate_token(user)
    payload = {
      sub: user.id,
      jti: SecureRandom.uuid,
      iat: Time.now.to_i,
      exp: 24.hours.from_now.to_i,
      email: user.email,
      username: user.username
    }

    JWT.encode(payload, Rails.application.credentials.secret_key_base, 'HS256')
  end

  def self.decode_token(token)
    JWT.decode(token, Rails.application.credentials.secret_key_base, true, { algorithm: 'HS256' })
  rescue JWT::DecodeError
    nil
  end
end
