class MatchesFetcherService
  attr_reader :season, :match_day, :stage

  def initialize(season, match_day, stage = nil)
    @season = season
    @match_day = match_day
    @stage = stage
  end

  def fetch
    matches = season.matches.where(matchday: match_day)
    matches = matches.where(stage:) if stage.present?
    matches
  end

  def serialize
    matches = fetch

    if matches.empty?
      empty_response
    else
      matches_response(matches)
    end
  end

  private

  def matches_response(matches)
    {
      matches: ActiveModelSerializers::SerializableResource.new(
        matches,
        each_serializer: MatchSerializer
      ),
      message: ['Matches found'],
      status: 200,
      type: 'success'
    }
  end

  def empty_response
    {
      matches: [],
      message: ['No matches found'],
      status: 404,
      type: 'error'
    }
  end
end
