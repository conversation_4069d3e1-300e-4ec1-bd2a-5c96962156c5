class UniqueJobScheduler
  # Schedule a job only if no similar job is already scheduled
  # Returns true if scheduled, false if skipped (duplicate)
  def self.schedule(job_class, args, scheduled_time = nil)
    job_key = generate_job_key(job_class, args)

    # Check if we already have this job in Redis
    return false if job_exists?(job_key)

    # Store the job key with an expiration (24 hours by default)
    store_job_key(job_key)

    # Schedule the job
    if scheduled_time
      job_class.set(wait_until: scheduled_time).perform_later(*args)
    else
      job_class.perform_later(*args)
    end

    true
  end

  # Force schedule a job even if a similar one exists (will replace the existing job)
  # Returns true always since it always schedules
  def self.force_schedule(job_class, args, scheduled_time = nil)
    job_key = generate_job_key(job_class, args)

    # Remove any existing job key and store the new one
    remove_job_key(job_key)
    store_job_key(job_key)

    # Schedule the job
    if scheduled_time
      job_class.set(wait_until: scheduled_time).perform_later(*args)
    else
      job_class.perform_later(*args)
    end

    true
  end

  private

  def self.generate_job_key(job_class, args)
    # Generate a unique key based on job class and arguments
    # We use JSON to consistently serialize the args
    serialized_args = args.map(&:to_s).join('-')
    "scheduled_job:#{job_class.name}:#{serialized_args}"
  end

  def self.job_exists?(job_key)
    # Check if the job key exists in Redis
    # If Redis is not available, assume the job doesn't exist

    Sidekiq.redis { |conn| conn.exists?(job_key) }
  rescue Redis::BaseError => e
    Rails.logger.error "Redis error checking job existence: #{e.message}"
    false
  end

  def self.store_job_key(job_key, expiration = 24.hours.to_i)
    # Store job key in Redis with expiration

    Sidekiq.redis { |conn| conn.setex(job_key, expiration, Time.current.iso8601) }
  rescue Redis::BaseError => e
    Rails.logger.error "Redis error storing job key: #{e.message}"
    false
  end

  def self.remove_job_key(job_key)
    # Remove a job key from Redis

    Sidekiq.redis { |conn| conn.del(job_key) }
  rescue Redis::BaseError => e
    Rails.logger.error "Redis error removing job key: #{e.message}"
    false
  end
end
