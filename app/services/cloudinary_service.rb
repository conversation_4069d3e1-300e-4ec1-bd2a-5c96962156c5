# app/services/cloudinary_service.rb

require 'cloudinary'
require 'securerandom'

class CloudinaryService
  class CloudinaryUploadError < StandardError; end

  def initialize
    return if mock_service?

    configure_cloudinary
  end

  def upload_image(image, folder: nil)
    return nil unless image.present?

    if mock_service?
      handle_mock_upload(image, folder)
    else
      handle_real_upload(image, folder)
    end
  rescue StandardError => e
    Rails.logger.error "Cloudinary upload failed: #{e.message}"
    raise CloudinaryUploadError, "Failed to upload image: #{e.message}"
  end

  def delete_image(public_id)
    return unless public_id.present?

    if mock_service?
      Rails.logger.info "Mock Cloudinary: Would delete image with public_id: #{public_id}"
      true
    else
      begin
        Cloudinary::Uploader.destroy(public_id)
      rescue StandardError => e
        Rails.logger.error "Cloudinary deletion failed: #{e.message}"
        raise CloudinaryUploadError, "Failed to delete image: #{e.message}"
      end
    end
  end

  private

  def mock_service?
    Rails.env.development? || Rails.env.test?
  end

  def handle_mock_upload(image, folder)
    # Generate a predictable public_id for development
    extension = File.extname(image.try(:original_filename) || '')
    public_id = [
      folder,
      "mock_#{SecureRandom.hex(8)}#{extension}"
    ].compact.join('/')

    Rails.logger.info "Mock Cloudinary: Generated public_id: #{public_id}"

    # Store the file locally in public/uploads if it's a file
    if image.respond_to?(:path)
      storage_dir = Rails.root.join('public', 'uploads', folder.to_s)
      FileUtils.mkdir_p(storage_dir)

      local_path = storage_dir.join(File.basename(public_id))
      FileUtils.cp(image.path, local_path)
      Rails.logger.info "Mock Cloudinary: Stored file locally at #{local_path}"
    end

    public_id
  end

  def handle_real_upload(image, folder)
    response = Cloudinary::Uploader.upload(
      image,
      folder:,
      resource_type: 'image'
    )
    response['public_id']
  end

  def configure_cloudinary
    Cloudinary.config do |config|
      config.cloud_name = ENV['CLOUDINARY_CLOUD_NAME']
      config.api_key = ENV['CLOUDINARY_API_KEY']
      config.api_secret = ENV['CLOUDINARY_API_SECRET']
      config.secure = true
    end
  end
end
