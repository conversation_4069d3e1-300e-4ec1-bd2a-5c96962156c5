require 'httparty'
require 'open-uri'

class ApiFootballService # rubocop:disable Metrics/ClassLength
  class ApiError < StandardError; end

  BASE_URL = 'https://v3.football.api-sports.io'.freeze
  # BASE_URL =
  #   if Rails.env.development?
  #     'http://localhost:3001'.freeze
  #   else
  #     'https://v3.football.api-sports.io'.freeze
  #   end

  def initialize
    @api_key = ENV['API_FOOTBALL_KEY']
    @headers = {
      'x-apisports-key' => @api_key,
      'x-rapidapi-host' => 'v3.football.api-sports.io'
    }
    @cloudinary_service = CloudinaryService.new
  end

  def import_competition(_ignored = nil, league_id = nil, season_year = nil)
    # Both league_id and season_year must be provided
    raise ApiError, 'Both league_id and season_year must be provided' unless league_id && season_year

    comp_data = fetch_competition(league_id, season_year)
    return unless comp_data

    ActiveRecord::Base.transaction do
      competition = update_or_create_competition(comp_data)
      if competition.persisted?
        import_teams_for_competition(competition, league_id, season_year)
        update_matches(competition, league_id, season_year)
        update_stage_transition(competition.current_season)
      end
      competition
    end
  rescue StandardError => e
    Rails.logger.error "API-SPORTS failed: #{e.message}"
    raise ApiError, "API-SPORTS error: #{e.message}"
  end

  def create_new_season_for_competition(competition, season_id)
    Rails.logger.info "Creating new season for competition: #{competition.code}"

    # Both competition.external_service_id and season_id must be provided
    unless competition.external_service_id && season_id
      raise ApiError, 'Both competition.external_service_id and season_id must be provided'
    end

    # Use the competition's external_service_id as the league_id
    league_id = competition.external_service_id
    season_year = season_id

    # Fetch the latest competition data to get the current season
    comp_data = fetch_competition(league_id, season_year)

    unless comp_data && comp_data['response']&.first && comp_data['response'].first['seasons']&.any?
      Rails.logger.error "No season data returned for competition: #{competition.code}"
      raise ApiError, "Failed to fetch season data for competition: #{competition.code}"
    end

    league_item = comp_data['response'].first
    season_data = league_item['seasons'].find { |s| s['year'].to_i == season_year.to_i } || league_item['seasons'].first

    # Check if the season already exists
    existing_season = Season.find_by(
      external_service_id: season_data['year'],
      source: 'api_football'
    )

    if existing_season && competition.current_season_id == existing_season.id
      Rails.logger.info "Season already exists and is set as current season for competition: #{competition.code}"
      return existing_season
    end

    ActiveRecord::Base.transaction do
      # Create the new season
      new_season = Season.find_or_initialize_by(external_service_id: season_data['year'], source: 'api_football')
      new_season.assign_attributes(
        start_date: season_data['start'],
        end_date: season_data['end'],
        current_matchday: season_data['current'] || season_data['currentMatchday'] || 1,
        competition_id: competition.id,
        source: 'api_football'
      )
      new_season.save!

      # Update the competition to use the new season
      competition.update!(current_season_id: new_season.id)

      # Import teams for the new season
      import_teams_for_competition(competition, league_id, season_year)

      # Update matches for the new season
      update_matches(competition, league_id, season_year)

      # Update stage transition for the new season
      update_stage_transition(new_season)

      # Transition leagues to the new season
      transition_leagues_to_new_season(competition, new_season)

      new_season
    rescue StandardError => e
      Rails.logger.error "Transaction failed in create_new_season_for_competition: #{e.class} - #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise ApiError, "Failed during new season creation: #{e.message}"
    end
  rescue StandardError => e
    Rails.logger.error "Failed to create new season: #{e.class} - #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    raise ApiError, "Failed to create new season: #{e.message}"
  end

  def fetch_competition(league_id, season_year)
    # Both league_id and season_year must be provided
    raise ApiError, 'Both league_id and season_year must be provided' unless league_id && season_year

    url = "#{BASE_URL}/leagues?id=#{league_id}&season=#{season_year}"
    response = HTTParty.get(url, headers: @headers)
    handle_response(response)
  end

  def update_matches_matchday(league_id, match_day, season)
    data = ApiFootballMatchesService.new.fetch_matches_for_round(league_id, match_day, season.external_service_id)
    return unless data && data['response']

    data['response'].each do |match_item|
      update_or_create_match(match_item, season)
    end

    # Only update stage transition if any matches were updated and it's a cup competition
    return unless data['response'].any? && season.competition.competition_type == 'CUP'

    # Check if any of the updated matches are finished
    finished_matches = data['response'].any? { |m| m['fixture']['status']['short'] == 'FT' }
    update_stage_transition(season, force_refresh: true) if finished_matches
  end

  def update_matches_by_date_range(league_id, start_date, end_date, season)
    # We need to create ApiFootballMatchesService class and method if it doesn't exist
    data = ApiFootballMatchesService.new.fetch_matches_by_date_range(league_id, start_date, end_date)
    return unless data && data['response']

    data['response'].each do |match_item|
      update_or_create_match(match_item, season)
    end

    # Only update stage transition if any matches were updated and it's a cup competition
    return unless data['response'].any? && season.competition.competition_type == 'CUP'

    # Check if any of the updated matches are finished
    finished_matches = data['response'].any? { |m| m['fixture']['status']['short'] == 'FT' }
    update_stage_transition(season, force_refresh: true) if finished_matches
  end

  def update_all_matches(competition)
    update_matches(competition)
  end

  def update_matches(competition, league_id = nil, season_year = nil)
    # Use competition attributes if parameters are not provided
    league_id ||= competition.external_service_id
    season_year ||= competition.current_season&.external_service_id

    # Both league_id and season_year must be available
    raise ApiError, 'Both league_id and season_year must be available' unless league_id && season_year

    # Move the fetching to ApiFootballMatchesService
    url = "#{BASE_URL}/fixtures?league=#{league_id}&season=#{season_year}"
    response = HTTParty.get(url, headers: @headers)
    data = handle_response(response)
    return unless data && data['response']

    data['response'].each do |match_item|
      update_or_create_match(match_item, competition.current_season)
    end

    # Only update stage transition if it's a cup competition and there are finished matches
    if competition.competition_type == 'CUP' && data['response'].any? { |m| m['fixture']['status']['short'] == 'FT' }
      update_stage_transition(competition.current_season, force_refresh: true)
    end
  end

  private

  def handle_response(response)
    case response.code
    when 200 then JSON.parse(response.body)
    when 404 then nil
    else
      raise ApiError, "API request failed with status #{response.code}: #{response.body}"
    end
  end

  def update_or_create_competition(data) # rubocop:disable Metrics/AbcSize,Metrics/CyclomaticComplexity,Metrics/MethodLength,Metrics/PerceivedComplexity
    league_item = data['response']&.first
    raise ApiError, 'Invalid data from API-SPORTS: missing league response' unless league_item

    league_info = league_item['league']
    area_info   = league_item['country']
    raise ApiError, 'Invalid data from API-SPORTS: missing country data' unless area_info

    # Process emblem/logo upload
    emblem_public_id = nil
    if league_info['logo']
      begin
        emblem_file = URI.open(league_info['logo'])
        emblem_public_id = @cloudinary_service.upload_image(emblem_file, folder: 'competitions/emblems')
      rescue OpenURI::HTTPError => e
        Rails.logger.error "Failed to download league logo: #{e.message}"
      end
    end

    area = create_area(area_info)
    attributes = {
      name: league_info['name'],
      code: 'SV',
      competition_type: league_info['type'].upcase,
      gender: 'male',
      emblem_public_id:,
      area_id: area.id,
      external_service_id: league_info['id'] # unique identifier from API-SPORTS
    }

    competition = Competition.find_by(external_service_id: league_info['id'])
    if competition
      competition.update!(attributes)
    else
      competition = Competition.create!(attributes)
    end

    # Create season from the first available season in the API response

    if league_item['seasons']&.any?
      season_data = league_item['seasons'].first
      season = Season.find_or_initialize_by(external_service_id: season_data['year'], source: 'api_football')
      season.assign_attributes(
        start_date: season_data['start'],
        end_date: season_data['end'],
        current_matchday: season_data['current'] || season_data['currentMatchday'] || 1,
        competition_id: competition.id,
        source: 'api_football'
      )
      season.save!
      competition.update!(current_season_id: season.id)
    end

    competition
  end

  def create_area(data)
    area = Area.find_or_initialize_by(external_service_id: data['code'])
    flag_public_id = nil
    if data['flag']
      begin
        flag_file = URI.open(data['flag']) # rubocop:disable Security/Open
        flag_public_id = @cloudinary_service.upload_image(flag_file, folder: 'areas/flags')
      rescue OpenURI::HTTPError => e
        Rails.logger.error "Failed to download area flag: #{e.message}"
      end
    end
    area.assign_attributes(
      name: data['name'],
      code: data['code'],
      flag_public_id:
    )
    area.save!
    area
  end

  def import_teams_for_competition(competition, league_id = nil, season_year = nil)
    # Use competition attributes if parameters are not provided
    league_id ||= competition.external_service_id
    season_year ||= competition.current_season&.external_service_id

    # Both league_id and season_year must be available
    raise ApiError, 'Both league_id and season_year must be available' unless league_id && season_year

    url = "#{BASE_URL}/teams?league=#{league_id}&season=#{season_year}"
    response = HTTParty.get(url, headers: @headers)
    data = handle_response(response)
    return unless data && data['response']

    data['response'].each do |team_item|
      create_team(team_item, competition)
    end
  end

  def create_team(team_item, competition)
    team_info = team_item['team']
    team_venue = team_item['venue']
    team = Team.find_or_initialize_by(external_service_id: team_info['id'])
    crest_public_id = nil

    if team_info['logo']
      begin
        crest_file = URI.open(team_info['logo']) # rubocop:disable Security/Open
        crest_public_id = @cloudinary_service.upload_image(crest_file, folder: 'teams/crests')
      rescue OpenURI::HTTPError => e
        Rails.logger.error "Failed to download team crest: #{e.message}"
      end
    end
    team.assign_attributes(
      name: team_info['name'],
      short_name: team_info['name'], # adjust if a separate short name is provided
      crest_public_id:,
      external_service_id: team_info['id'],
      tla: team_info['code'],
      venue: team_venue['name'],
      area_id: competition.area_id # use the competition’s area
    )
    # Initialize club_colors structure
    team.club_colors = { home: {}, away: {} } if team.club_colors.blank?

    team.save!
    competition.current_season.teams << team unless competition.current_season.teams.include?(team)
    team
  end

  def update_or_create_match(match_item, season)
    external_service_id = match_item['fixture']['id']
    match = Match.find_or_initialize_by(external_service_id:)
    home_team = Team.find_by(external_service_id: match_item['teams']['home']['id'])
    away_team = Team.find_by(external_service_id: match_item['teams']['away']['id'])

    stage, matchday = match_item['league']['round'].split(' - ')
    api_status = match_item['fixture']['status']['short'] || match_item['fixture']['status']['long']
    mapped_status = map_status(api_status, match_item['fixture']['date'])
    match.update!(
      season:,
      matchday: matchday || nil,
      status: mapped_status,
      utc_date: match_item['fixture']['date'],
      home_team_id: home_team&.id,
      away_team_id: away_team&.id,
      stage: stage.upcase.gsub(' ', '_'),
      referee: match_item['fixture'][:referee]
    )
    update_or_create_score(match, match_item['score'])
  end

  def map_status(api_status, utc_date) # rubocop:disable Metrics/CyclomaticComplexity
    # Example logic for deciding SCHEDULED vs TIMED if needed
    is_date_finalized = utc_date.present? && utc_date > Time.current

    case api_status
    when 'TBD'
      'SCHEDULED'
    when 'NS'
      is_date_finalized ? 'TIMED' : 'SCHEDULED'
    when '1H', '2H', 'LIVE', 'ET', 'P'
      'IN_PLAY'
    when 'HT', 'BT'
      'PAUSED'
    when 'FT', 'AET', 'PEN'
      'FINISHED'
    when 'SUSP', 'INT', 'ABD'
      'SUSPENDED'
    when 'PST'
      'POSTPONED'
    when 'CANC'
      'CANCELLED'
    when 'AWD', 'WO'
      'AWARDED'
    else
      'SCHEDULED'
    end
  end

  def update_or_create_score(match, score_data)
    return unless score_data # Skip if score_data is nil

    # Use either camelCase or lowercase keys
    full_time = score_data['fullTime'] || score_data['fulltime'] || {}
    half_time = score_data['halfTime'] || score_data['halftime'] || {}

    if full_time.empty? || half_time.empty?
      Rails.logger.warn "Incomplete score data for match #{match.external_service_id}: #{score_data.inspect}"
    end

    # Only proceed if we have valid score data
    return if full_time['home'].nil? && full_time['away'].nil?

    Score.find_or_initialize_by(match:).tap do |score|
      duration = get_duration(score_data)
      winner = get_winner(full_time)
      score.update!(
        duration:,
        fullTimeHome: full_time['home'],
        fullTimeAway: full_time['away'],
        halfTimeHome: half_time['home'],
        halfTimeAway: half_time['away'],
        winner:
      )
    end
  end

  def get_winner(full_time)
    value_home = full_time['home'].to_i
    value_away = full_time['away'].to_i
    if value_home > value_away
      'HOME_TEAM'
    elsif value_home < value_away
      'AWAY_TEAM'
    else
      'DRAW'
    end
  end

  def update_stage_transition(season, force_refresh: false)
    return unless season.competition.competition_type == 'CUP'

    cache_key = "season_#{season.id}_stage_transition"

    if force_refresh
      # Force a refresh by bypassing the cache
      Rails.logger.info "Forcing refresh of stage transition for season #{season.id}"
      StageTransitionService.new(season).update_stage_and_matchday!
      Rails.cache.write(cache_key, Time.current, expires_in: 1.day)
    else
      # Use the cache as before
      Rails.cache.fetch(cache_key, expires_in: 1.day) do
        StageTransitionService.new(season).update_stage_and_matchday!
      end
    end
  end

  def get_duration(score_data)
    # Default to 'regular' if extratime or penalty data is missing
    extratime = score_data['extratime'] || {}
    penalty = score_data['penalty'] || {}

    return 'regular' if extratime['home'].nil? && extratime['away'].nil?
    return 'extratime' if penalty['home'].nil? && penalty['away'].nil?

    'penalties'
  end

  def transition_leagues_to_new_season(competition, new_season)
    # Create a service to transition leagues to the new season
    service = LeagueSeasonTransitionService.new(competition, new_season)

    # Check if transition is needed
    return 0 unless service.new_season?

    # Perform the transition (this will also archive leagues from the old season)
    transitioned_count = service.transition_leagues_to_new_season

    Rails.logger.info "Transitioned #{transitioned_count} leagues to new season #{new_season.id} for competition #{competition.code}"

    transitioned_count
  end
end
