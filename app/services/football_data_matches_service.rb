require 'open-uri'

class FootballDataMatchesService
  include HTTParty
  base_uri 'https://api.football-data.org/v4'

  def initialize
    @options = {
      headers: {
        'X-Auth-Token' => ENV['FOOTBALL_API_KEY']
      }
    }
  end

  def fetch_matches(competition_code, match_day, stage)
    self.class.get("/competitions/#{competition_code}/matches?matchday=#{match_day}&stage=#{stage}",
                   @options)
  end

  def fetch_matches_by_stage(competition_code, stage)
    self.class.get("/competitions/#{competition_code}/matches?stage=#{stage}", @options)
  end

  def fetch_single_match(match_id)
    self.class.get("/matches/#{match_id}", @options)
  end

  def fetch_matches_by_date_range(competition_code, start_date, end_date)
    self.class.get(
      "/competitions/#{competition_code}/matches?dateFrom=#{start_date}&dateTo=#{end_date}",
      @options
    )
  end

  private
end
