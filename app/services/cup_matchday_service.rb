class CupMatchdayService
  STAGE_MATCHDAYS = {
    'LEAGUE_STAGE' => 8,
    'PLAYOFFS' => 2,
    'LAST_16' => 2,
    'QUARTER_FINALS' => 2,
    'SEMI_FINALS' => 2,
    'FINAL' => 1
  }.freeze

  def initialize(season)
    @season = season
  end

  def current_stage_total_matchdays
    STAGE_MATCHDAYS[@season.current_stage] || 0
  end

  def calculate_overall_matchday
    return @season.current_matchday unless @season.competition.competition_type == 'CUP'

    calculate_completed_stages_matchdays + 1
  end

  private

  def calculate_completed_stages_matchdays
    total = 0
    current_stage_index = StageTransitionService::STAGES_ORDER.index(@season.current_stage)
    return 0 unless current_stage_index

    StageTransitionService::STAGES_ORDER[0..current_stage_index].each do |stage|
      stage_matchdays = STAGE_MATCHDAYS[stage] || 0
      stage_matchdays.times do |matchday|
        total += 1 if matches_finished_for_stage_and_matchday?(stage, matchday + 1)
      end
    end
    total
  end

  # TODO: same method in matchday_options_service. Refactor to shared module
  def matches_finished_for_stage_and_matchday?(stage, matchday)
    @season.matches.where(stage:, matchday:).all? { |match| %w[FINISHED POSTPONED].include?(match.status) }
  end
end
