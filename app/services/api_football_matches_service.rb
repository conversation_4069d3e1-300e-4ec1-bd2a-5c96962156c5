require 'open-uri'

class ApiFootballMatchesService
  class ApiError < StandardError; end

  include HTTParty
  BASE_URL = 'https://v3.football.api-sports.io'.freeze

  def initialize
    @api_key = ENV['API_FOOTBALL_KEY']
    @headers = {
      'x-apisports-key' => @api_key,
      'x-rapidapi-host' => 'v3.football.api-sports.io'
    }
  end

  def fetch_matches_for_round(league_id, round, season_id)
    url = "#{BASE_URL}/fixtures?league=#{league_id}&round=#{round}&season=#{season_id}"
    response = HTTParty.get(url, headers: @headers)
    handle_response(response)
  end

  def fetch_matches_by_date_range(league_id, start_date, end_date)
    # Format dates as required by the API (YYYY-MM-DD)
    formatted_start_date = start_date.strftime('%Y-%m-%d')
    formatted_end_date = end_date.strftime('%Y-%m-%d')

    url = "#{BASE_URL}/fixtures?league=#{league_id}&from=#{formatted_start_date}&to=#{formatted_end_date}"
    response = HTTParty.get(url, headers: @headers)
    handle_response(response)
  end

  private

  def handle_response(response)
    case response.code
    when 200 then JSON.parse(response.body)
    when 404 then nil
    else
      Rails.logger.error "API request failed with status #{response.code}: #{response.body}"
      nil
    end
  end
end
