# app/services/youtube_quota_service.rb
# Service for monitoring and managing YouTube API quota usage
class YoutubeQuotaService
  DAILY_QUOTA_LIMIT = ENV.fetch('YOUTUBE_API_DAILY_QUOTA', 10_000).to_i
  QUOTA_BUFFER = ENV.fetch('YOUTUBE_API_QUOTA_BUFFER', 1_000).to_i

  def self.track_api_call(method_name, units_used)
    today = Date.today.to_s
    key = "youtube_api:usage:#{today}"

    begin
      # Increment usage counter
      current_usage = RedisConnection.redis.incrby(key, units_used)

      # Set expiry to ensure counter resets
      RedisConnection.redis.expire(key, 48.hours)

      # Log usage
      Rails.logger.info "YouTube API usage: #{current_usage}/#{DAILY_QUOTA_LIMIT} units (#{method_name}: +#{units_used})"

      # Return current usage
      current_usage
    rescue StandardError => e
      # Handle Redis connection errors gracefully
      Rails.logger.warn "Failed to track YouTube API usage: #{e.message}. Continuing without tracking."
      0 # Return 0 as a fallback
    end
  end

  def self.can_make_api_call?(method_name, units_needed)
    today = Date.today.to_s
    key = "youtube_api:usage:#{today}"

    begin
      # Get current usage
      current_usage = RedisConnection.redis.get(key).to_i

      # Check if we have enough quota remaining
      remaining = DAILY_QUOTA_LIMIT - current_usage
      can_proceed = remaining > (units_needed + QUOTA_BUFFER)

      unless can_proceed
        Rails.logger.warn "YouTube API quota nearly exhausted: #{current_usage}/#{DAILY_QUOTA_LIMIT} units. Blocking call to #{method_name} (#{units_needed} units)"
      end

      can_proceed
    rescue StandardError => e
      # Handle Redis connection errors gracefully
      Rails.logger.warn "Failed to check YouTube API quota: #{e.message}. Allowing API call to proceed."
      true # Allow the call to proceed as a fallback
    end
  end

  def self.get_current_usage
    today = Date.today.to_s
    key = "youtube_api:usage:#{today}"

    begin
      RedisConnection.redis.get(key).to_i
    rescue StandardError => e
      Rails.logger.warn "Failed to get YouTube API usage: #{e.message}"
      0 # Return 0 as a fallback
    end
  end

  def self.get_remaining_quota
    DAILY_QUOTA_LIMIT - get_current_usage
  end

  def self.reset_usage
    # For testing purposes only
    today = Date.today.to_s
    key = "youtube_api:usage:#{today}"

    begin
      RedisConnection.redis.del(key)
      true
    rescue StandardError => e
      Rails.logger.warn "Failed to reset YouTube API usage: #{e.message}"
      false
    end
  end
end
