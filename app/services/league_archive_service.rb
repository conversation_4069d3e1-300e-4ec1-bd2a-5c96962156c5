# app/services/league_archive_service.rb
class LeagueArchiveService
  def initialize(season)
    @season = season
  end

  # Archive all active leagues for a season
  def archive_all_leagues
    leagues = League.for_season(@season.id).active
    archived_count = 0

    leagues.each do |league|
      league.archive!
      archived_count += 1
    rescue StandardError => e
      Rails.logger.error "Failed to archive league #{league.id}: #{e.message}"
    end

    archived_count
  end

  # Check if a season is completed (end date is in the past)
  def season_completed?
    @season.end_date.present? && @season.end_date < Date.current
  end

  # Check if all matches for a season are completed
  def all_matches_completed?
    @season.matches.where.not(status: %w[FINISHED POSTPONED CANCELLED]).count.zero?
  end

  # Archive leagues if season is completed
  def archive_leagues_if_season_completed
    return 0 unless season_completed? && all_matches_completed?

    archive_all_leagues
  end
end
