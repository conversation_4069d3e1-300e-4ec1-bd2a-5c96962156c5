# app/services/ksi_soap_service.rb
require 'savon'
require 'active_support/all' # Ensure ActiveSupport is fully loaded

class KsiSoapService # rubocop:disable Metrics/ClassLength
  ENDPOINT = 'http://www2.ksi.is/vefthjonustur/mot.asmx'.freeze
  WSDL = "#{ENDPOINT}?WSDL".freeze

  attr_reader :client

  def initialize
    @client = Savon.client(
      wsdl: WSDL,
      endpoint: ENDPOINT,
      log: Rails.env.development?,
      log_level: :debug,
      pretty_print_xml: true,
      convert_request_keys_to: :none, # Don't convert keys - use exactly as provided
      convert_response_tags_to: ->(tag) { safe_snakecase(tag).to_sym }, # Use safe_snakecase
      soap_version: 2 # SOAP 1.2
    )
    @cloudinary_service = CloudinaryService.new
  end

  def setup_service(competition_code, gender, season_external_service_id)
    find_or_create_area('Iceland')
    find_or_create_competition(competition_code, gender)
    tournament_matches = fetch_tournament_matches(season_external_service_id)[:mot_leikur]

    find_or_create_season(season_external_service_id,
                          tournament_matches.first[:leik_dagur],
                          tournament_matches.last[:leik_dagur])

    @competition.current_season = @season
    @competition.save!

    tournament_matches.map { |match| find_or_create_match(match) }
  end

  def create_new_season_for_competition(competition, season_external_service_id)
    Rails.logger.info "Creating new season for competition: #{competition.code} with external service ID: #{season_external_service_id}"

    # Set instance variables
    @competition = competition
    find_or_create_area('Iceland')

    # Fetch tournament matches to get start and end dates
    tournament_data = fetch_tournament_matches(season_external_service_id)

    unless tournament_data && tournament_data[:mot_leikur]
      Rails.logger.error "No match data returned for season: #{season_external_service_id}"
      raise "Failed to fetch match data for season: #{season_external_service_id}"
    end

    tournament_matches = tournament_data[:mot_leikur]

    # Check if the season already exists
    existing_season = Season.find_by(
      external_service_id: season_external_service_id,
      source: 'ksi_soap'
    )

    if existing_season && competition.current_season_id == existing_season.id
      Rails.logger.info "Season already exists and is set as current season for competition: #{competition.code}"
      @season = existing_season
      return existing_season
    end

    # Create the new season
    find_or_create_season(
      season_external_service_id,
      tournament_matches.first[:leik_dagur],
      tournament_matches.last[:leik_dagur]
    )

    # Update the competition to use the new season
    competition.update!(current_season_id: @season.id)

    # Import matches for the new season
    tournament_matches.map { |match| find_or_create_match(match) }

    # Transition leagues to the new season
    transition_leagues_to_new_season(competition, @season)

    @season
  end

  def update_matches(season_external_service_id)
    # TODO: We need to update current_matchday for the season
    find_season(season_external_service_id)
    tournament_data = fetch_tournament_matches(season_external_service_id)
    return [] unless tournament_data && tournament_data[:mot_leikur]

    tournament_matches = tournament_data[:mot_leikur]
    tournament_matches.map { |match| update_match(match) }
  end

  # Lists available operations from the WSDL
  def operations
    @client.operations
  end

  # Get matches for a specific team
  def get_matches(team_number, date_from, date_to, options = {})
    params = {
      'FelagNumer' => team_number.to_s,
      'DagsFra' => date_from.to_s,
      'DagsTil' => date_to.to_s,
      'Kyn' => options[:gender] || '',
      'FlokkurNumer' => options[:category] || '',
      'VollurNumer' => options[:stadium] || ''
    }

    # Remove nil values but keep empty strings
    params.compact!

    response = @client.call(:felog_leikir, message: params)
    process_response(response.body[:felog_leikir_response][:array_felog_leikir])
  end

  # Get players for a specific match
  def get_players(match_id)
    response = @client.call(:leikur_leikmenn, message: { 'LeikurNumer' => match_id.to_s })
    process_response(response.body[:leikur_leikmenn_response][:array_leikur_leikmenn])
  end

  # Get standings for a specific tournament
  def tournament_standings(tournament_number)
    response = @client.call(:mot_stada, message: { 'MotNumer' => tournament_number.to_s })
    process_response(response.body[:mot_stada_response][:mot_stada_result])
  end

  # Get matches in a tournament
  def fetch_tournament_matches(tournament_number)
    response = if Rails.env.development?
                 # Use mock response in development when enabled
                 SoapMocker.mock_response('tournament_matches')
               else
                 @client.call(:mot_leikir, message: { 'MotNumer' => tournament_number.to_s })
               end

    process_response(response.body) # Pass the entire response body
  end

  # Get top scorers in a tournament
  def top_scorers(tournament_number)
    response = @client.call(:mot_markahaestu, message: { 'MotNumer' => tournament_number.to_s })
    process_response(response.body[:mot_markahaestu_response][:mot_markahaestu_result])
  end

  # Get groups (categories)
  def groups
    response = @client.call(:flokkur)
    process_response(response.body[:flokkur_response][:flokkur_result])
  end

  # Get yellow cards in a tournament
  def yellow_cards(tournament_number)
    response = @client.call(:mot_spjold_gul, message: { 'MotNumer' => tournament_number.to_s })
    process_response(response.body[:mot_spjold_gul_response][:mot_spjold_gul_result])
  end

  # Get red cards in a tournament
  def red_cards(tournament_number)
    response = @client.call(:mot_spjold_raud, message: { 'MotNumer' => tournament_number.to_s })
    process_response(response.body[:mot_spjold_raud_response][:mot_spjold_raud_result])
  end

  # Get events in a match
  def match_events(match_number)
    response = @client.call(:leikur_atburdir, message: { 'LeikurNumer' => match_number.to_s })
    process_response(response.body[:leikur_atburdir_response][:leikur_atburdir_result])
  end

  # Get players in a match
  def match_players(match_number)
    response = @client.call(:leikur_leikmenn, message: { 'LeikurNumer' => match_number.to_s })
    process_response(response.body[:leikur_leikmenn_response][:leikur_leikmenn_result])
  end

  # Get referees in a match
  def match_referees(match_number)
    response = @client.call(:leikur_domarar, message: { 'LeikurNumer' => match_number.to_s })
    process_response(response.body[:leikur_domarar_response][:leikur_domarar_result])
  end

  # Get match changes within a time period
  def match_changes(from_date, to_date)
    response = @client.call(:leikir_breyting, message: {
                              'DagurTimiFra' => from_date.to_s,
                              'DagurTimiTra' => to_date.to_s
                            })
    process_response(response.body[:leikir_breyting_response][:leikir_breyting_result])
  end

  # Get score changes within a time period
  def score_changes(from_date, to_date)
    response = @client.call(:leikir_breyting_urslit, message: {
                              'DagurTimiFra' => from_date.to_s,
                              'DagurTimiTra' => to_date.to_s
                            })
    process_response(response.body[:leikir_breyting_urslit_response][:leikir_breyting_urslit_result])
  end

  # Get specific matches by match numbers
  def matches(match_numbers)
    match_numbers_str = match_numbers.join(',')
    response = @client.call(:leikir, message: { 'LeikirNumer' => match_numbers_str })
    process_response(response.body[:leikir_response][:leikir_result])
  end

  # TODO: Verify its not used and remove it
  def import_competition(competition_code, gender)
    # emblem_public_id bestaDeildin_xp2mg6, name "Besta Deildin"
    # no external_service_id, code "BDkk" for male and "BDkvk" for female gender.
    # competition_type "LEAGUE", source "ksi_soap"

    @competition = Competition.find_or_initialize_by(code: competition_code).tap do |competition|
      competition.assign_attributes(
        name: competition[:name],
        area: find_or_create_area(competition_data[:area]),
        source: 'ksi_soap',
        gender:
      )
      competition.save!
    end
  end

  def map_match_data(raw_match_data) # rubocop:disable Metrics/CyclomaticComplexity,Metrics/PerceivedComplexity
    {
      external_service_id: raw_match_data[:leikur_numer]&.to_i || raw_match_data['leikur_numer']&.to_i,
      matchday: raw_match_data[:umferd_numer]&.to_i || raw_match_data['umferd_numer']&.to_i,
      date: raw_match_data[:leik_dagur] || raw_match_data['leik_dagur'],
      status: map_match_status(raw_match_data[:skyrsla_stada] || raw_match_data['skyrsla_stada']),
      home_team: {
        name: raw_match_data[:felag_heima_nafn] || raw_match_data['felag_heima_nafn'],
        external_service_id: raw_match_data[:felag_heima_numer]&.to_i || raw_match_data['felag_heima_numer']&.to_i,
        venue: raw_match_data[:vollur_nafn] || raw_match_data['vollur_nafn']
      },
      away_team: {
        name: raw_match_data[:felag_uti_nafn] || raw_match_data['felag_uti_nafn'],
        external_service_id: raw_match_data[:felag_uti_numer]&.to_i || raw_match_data['felag_uti_numer']&.to_i
      },
      scores: {
        fullTimeHome: raw_match_data[:urslit_heima]&.to_i || raw_match_data['urslit_heima']&.to_i,
        fullTimeAway: raw_match_data[:urslit_uti]&.to_i || raw_match_data['urslit_uti']&.to_i,
        halfTimeHome: raw_match_data[:stada_fyrri_halfleik_heima]&.to_i ||
          raw_match_data['stada_fyrri_halfleik_heima']&.to_i,
        halfTimeAway: raw_match_data[:stada_fyrri_halfleik_uti]&.to_i ||
          raw_match_data['stada_fyrri_halfleik_uti']&.to_i
      },
      venue: raw_match_data[:vollur_nafn] || raw_match_data['vollur_nafn']
    }
  end

  private

  def find_or_create_team(team_data)
    team_to_update = Team.find_or_initialize_by(external_service_id: team_data[:external_service_id]).tap do |team|
      external_service_id = team_data[:external_service_id]
      crest_public_id = find_or_upload_crest(external_service_id)

      team.assign_attributes(
        name: team_data[:name],
        area: @area,
        source: 'ksi_soap',
        short_name: team_data[:name],
        tla: team_data[:name][0, 3].upcase,
        venue: team_data[:venue].presence || team.venue || 'temp',
        crest_public_id:,
        external_service_id:
      )
      team.save!
      @competition.current_season.teams << team unless @competition.current_season.teams.include?(team)
    end

    team_to_update.update(venue: team_data[:venue]) if team_to_update.venue == 'temp' && team_data[:venue].present?

    team_to_update
  end

  def find_or_create_match(match_data)
    mapped_match_data = map_match_data(match_data)

    match = Match.find_or_initialize_by(external_service_id: mapped_match_data[:external_service_id])

    match.assign_attributes(
      season: @season,
      utc_date: mapped_match_data[:date],
      home_team: find_or_create_team(mapped_match_data[:home_team]),
      away_team: find_or_create_team(mapped_match_data[:away_team]),
      venue: mapped_match_data[:venue],
      matchday: mapped_match_data[:matchday],
      status: mapped_match_data[:status],
      stage: 'REGULAR_SEASON',
      source: 'ksi_soap'
    )

    # Save the match first to ensure it has an ID
    match.save!

    # Now handle the score
    if mapped_match_data[:scores].present?
      # Find or create the associated score
      score = match.score || match.build_score
      score.update!(
        fullTimeHome: mapped_match_data[:scores][:fullTimeHome],
        fullTimeAway: mapped_match_data[:scores][:fullTimeAway],
        halfTimeHome: mapped_match_data[:scores][:halfTimeHome],
        halfTimeAway: mapped_match_data[:scores][:halfTimeAway],
        winner: get_winner(mapped_match_data)
      )
    end

    match
  end

  def update_match(match_data)
    mapped_match_data = map_match_data(match_data)

    match = Match.find_by(external_service_id: mapped_match_data[:external_service_id])
    Rails.logger.warn "Match with external_service_id #{mapped_match_data[:external_service_id]} not found" unless match
    return unless match

    match.assign_attributes(
      utc_date: mapped_match_data[:date],
      venue: mapped_match_data[:venue],
      status: mapped_match_data[:status],
      stage: 'REGULAR_SEASON'
    )

    # Save the match first to ensure it has an ID
    match.save!

    # Now handle the score
    if mapped_match_data[:scores].present?
      # Find or create the associated score
      score = match.score || match.build_score
      score.update!(
        fullTimeHome: mapped_match_data[:scores][:fullTimeHome],
        fullTimeAway: mapped_match_data[:scores][:fullTimeAway],
        halfTimeHome: mapped_match_data[:scores][:halfTimeHome],
        halfTimeAway: mapped_match_data[:scores][:halfTimeAway],
        winner: get_winner(mapped_match_data)
      )
    end

    match
  end

  def find_team_by_external_service_id(external_service_id)
    Team.find_by(external_service_id:)
  end

  def find_season(external_service_id)
    @season = Season.find_by(external_service_id:)
  end

  def find_or_create_season(external_service_id, season_start, season_end)
    @season = Season.find_or_initialize_by(external_service_id:).tap do |season|
      season.assign_attributes(
        competition: @competition,
        start_date: season_start,
        end_date: season_end,
        source: 'ksi_soap',
        current_matchday: 1
      )
      season.save!
    end
  end

  def find_or_create_competition(competition_code, gender)
    @competition = Competition.find_or_initialize_by(code: competition_code, gender:).tap do |competition|
      competition.assign_attributes(
        name: 'Besta deildin',
        area: @area,
        source: 'ksi_soap',
        gender:,
        competition_type: 'LEAGUE',
        emblem_public_id: 'competitions/emblems/bestaDeildin_xp2mg6',
        external_service_id: nil
      )
      competition.save!
    end
  end

  def find_or_create_area(area_name)
    # There is only one area for this service. name "Iceland", code "IS",
    # no external_service_id, flag_public_id Flag_of_Iceland.svg_j8ircz
    @area = Area.find_or_initialize_by(name: area_name, source: 'ksi_soap').tap do |area|
      area.code = 'IS'
      area.flag_public_id = 'areas/flags/Flag_of_Iceland.svg_j8ircz'
      area.save!
    end
  end

  def get_winner(mapped_match_data)
    # Determine the winner based on the scores
    # If home team score is greater than away team score, home team wins and set winner to HOME_TEAM
    # If away team score is greater than home team score, away team wins and set winner to AWAY_TEAM
    # If scores are equal, it's a draw and set winner to DRAW
    # If no scores are available, set winner to nil

    return nil unless mapped_match_data[:scores] &&
                      mapped_match_data[:scores][:fullTimeHome] &&
                      mapped_match_data[:scores][:fullTimeAway]

    home_score = mapped_match_data[:scores][:fullTimeHome]
    away_score = mapped_match_data[:scores][:fullTimeAway]

    if home_score > away_score
      'HOME_TEAM'
    elsif home_score < away_score
      'AWAY_TEAM'
    else
      'DRAW'
    end
  end

  def map_match_status(status)
    case status
    when 'U'
      'FINISHED'
    when 'S'
      'FINISHED'
    when nil
      'TIMED'
    when 'L'
      'FINISHED'
    when 'H'
      'IN_PLAY'
    when 'A'
      'IN_PLAY'
    else
      raise "Unknown match status: #{status}"
    end
  end

  def find_or_upload_crest(external_service_id)
    crest_file_path = Rails.root.join('app', 'assets', 'logos', 'clubLogos', "#{external_service_id}.png")
    unless File.exist?(crest_file_path)
      crest_file_path = Rails.root.join('app', 'assets', 'logos', 'clubLogos',
                                        "#{external_service_id}.svg")
    end

    if File.exist?(crest_file_path)
      crest_public_id = @cloudinary_service.upload_image(crest_file_path, folder: 'teams/crests')
    else
      Rails.logger.warn "Crest file not found for external_service_id: #{external_service_id}"
      crest_public_id = "#{external_service_id}_crest"
    end
    crest_public_id
  end

  # Safe fallback for snakecase
  def safe_snakecase(tag)
    if tag.respond_to?(:snakecase)
      tag.snakecase
    else
      tag.to_s.gsub(/::/, '/')
         .gsub(/([A-Z]+)([A-Z][a-z])/, '\1_\2')
         .gsub(/([a-z\d])([A-Z])/, '\1_\2')
         .tr('-', '_')
         .downcase
    end
  end

  def transition_leagues_to_new_season(competition, new_season)
    # Create a service to transition leagues to the new season
    service = LeagueSeasonTransitionService.new(competition, new_season)

    # Check if transition is needed
    return 0 unless service.new_season?

    # Perform the transition (this will also archive leagues from the old season)
    transitioned_count = service.transition_leagues_to_new_season

    Rails.logger.info "Transitioned #{transitioned_count} leagues to new season #{new_season.id} for competition #{competition.code}"

    transitioned_count
  end

  def process_response(response_body)
    # Log the raw response body for debugging
    Rails.logger.debug("Raw SOAP Response Body: #{response_body.inspect}")

    # Ensure the response body is not nil
    raise 'Unexpected Response: Response body is nil' if response_body.nil?

    # Handle different response formats
    if response_body.key?(:mot_leikir_response) && response_body[:mot_leikir_response].key?(:mot_leikir_svar)
      # Direct access to mot_leikur array if it exists at this level
      if response_body[:mot_leikir_response][:mot_leikir_svar].key?(:mot_leikur)
        return response_body[:mot_leikir_response][:mot_leikir_svar]
      end

      # Check for error codes in the response
      if response_body.dig(:mot_leikir_response, :mot_leikir_svar, :villa_numer).to_i != 0
        raise "KSI API Error: #{response_body.dig(:mot_leikir_response, :mot_leikir_svar,
                                                  :villa_texti) || 'Unknown error'}"
      end

      # Try to navigate to the array key
      array_key = response_body.dig(:mot_leikir_response, :mot_leikir_svar, :array_mot_leikir)
      return array_key if array_key
    end

    # For other response formats, return the body as is
    Rails.logger.info("Using alternative response format: #{response_body.keys}")
    response_body
  rescue Savon::SOAPFault => e
    Rails.logger.error("SOAP Fault: #{e.message}")
    raise "SOAP Fault: #{e.message}"
  rescue Savon::HTTPError => e
    Rails.logger.error("HTTP Error: #{e.message}")
    raise "HTTP Error: #{e.message}"
  rescue StandardError => e
    Rails.logger.error("Unexpected Error: #{e.message}")
    raise "Unexpected Error: #{e.message}"
  end
end
