# app/services/soap_mocker.rb
module Soap<PERSON>ocker
  def self.mock_response(operation, _params = {})
    case operation
    when 'tournament_matches'
      json_path = Rails.root.join('mock_data', 'tournament_matches.json')
      raise "Mock file not found: #{json_path}" unless File.exist?(json_path)

      json_data = JSON.parse(File.read(json_path))
      mock_tournament_matches_response(json_data)
    else
      # Original XML-based mocking for other operations
      fixture_name = "#{operation}_response.xml"
      fixture_path = Rails.root.join('spec/fixtures/soap_responses', fixture_name)
      raise "Mock file not found: #{fixture_path}" unless File.exist?(fixture_path)

      xml = File.read(fixture_path)
      mock_savon_response(xml)
    end
  end

  def self.mock_tournament_matches_response(json_data)
    # Extract matches from the nested structure
    matches_data = json_data.dig('mot_leikur', 'mot_leikur')
    raise 'No matches found in tournament_matches.json' unless matches_data.is_a?(Array)

    # Convert string keys to symbols and add required stage for each match
    symbolized_data = matches_data.map do |match|
      transformed_match = match.transform_keys(&:to_sym)
      transformed_match[:stage] = 'REGULAR_SEASON' # Add stage to match production behavior
      transformed_match
    end

    # Create a response structure that matches the production SOAP response
    response = OpenStruct.new
    response.body = {
      mot_leikir_response: {
        mot_leikir_svar: {
          mot_leikur: symbolized_data
        }
      }
    }
    response
  end

  def self.mock_savon_response(xml_string)
    # Existing implementation for XML responses
    response = OpenStruct.new
    nori = Nori.new(
      convert_tags_to: ->(tag) { tag.underscore.to_sym },
      strip_namespaces: true
    )
    parsed_xml = nori.parse(xml_string)

    Rails.logger.debug("Parsed XML: #{parsed_xml.inspect}")

    unless parsed_xml[:envelope] && parsed_xml[:envelope][:body]
      raise "Invalid SOAP response structure. Parsed XML: #{parsed_xml.inspect}"
    end

    response.body = parsed_xml[:envelope][:body]
    response
  end
end
