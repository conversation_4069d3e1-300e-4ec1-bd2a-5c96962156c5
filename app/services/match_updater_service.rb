class MatchUpdaterService
  def self.update_matches(competition)
    case competition.source
    when 'football_data'
      FootballDataService.new.update_matches(competition, competition.current_season)
    when 'api_football'
      ApiFootballService.new.update_matches(competition)
    when 'ksi_soap'
      KsiSoapService.new.update_matches(competition.current_season.external_service_id)
    else
      raise "Unknown source: #{competition.source}"
    end
  end

  def self.update_matches_for_matchday(competition_code, match_day, stage)
    # LLimit the updates. PL and CL max every 10 mins, SV every 60 mins. can we use a cron job? or change the update frequency depending on if any matches are ongoing? # rubocop:disable Layout/LineLength

    competition = Competition.find_by(code: competition_code)
    return unless competition

    season = competition.current_season
    return unless season

    case competition.source
    when 'football_data'
      FootballDataService.new.update_matches_matchday(competition, match_day, stage, season)
      # Explicitly check stage transition after updating matches if it's a cup competition
      season.check_stage_transition if competition.competition_type == 'CUP'
    when 'api_football'
      ApiFootballService.new.update_matches_matchday(competition.external_service_id, match_day, season)
    when 'ksi_soap'
      KsiSoapService.new.update_matches(competition.current_season.external_service_id)
      #  update matches for the matchday is not implemented in KSI SOAP service yet
      # KsiSoapService.new.update_matches_matchday(season.external_service_id, match_day)
    else
      raise "Unknown source: #{competition.source}"
    end
  end
end
