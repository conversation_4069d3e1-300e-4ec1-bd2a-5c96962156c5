class UserStatisticsService
  def self.calculate(user, stats_type = 'basic', filters = {})
    case stats_type
    when 'basic'
      calculate_basic_stats(user, filters)
    when 'advanced'
      calculate_advanced_stats(user, filters)
    when 'competition'
      calculate_competition_stats(user, filters)
    when 'comparative'
      calculate_comparative_stats(user, filters)
    when 'all'
      calculate_all_stats(user, filters)
    else
      calculate_basic_stats(user, filters)
    end
  end

  def self.calculate_basic_stats(user, filters = {})
    # Filter round predictions based on filters
    round_predictions = filter_round_predictions(user, filters)

    # Calculate match predictions based on round predictions
    match_predictions = MatchPrediction.where(round_prediction_id: round_predictions.pluck(:id))

    # Calculate total counts
    total_predictions = match_predictions.count
    perfect_count = match_predictions.where(points: 3).count
    correct_count = match_predictions.where(points: 1).count
    incorrect_count = match_predictions.where(points: 0).count

    # Calculate percentages
    prediction_accuracy = if total_predictions > 0
                            ((perfect_count + correct_count).to_f / total_predictions * 100).round(2)
                          else
                            0
                          end
    perfect_percentage = if total_predictions > 0
                           (perfect_count.to_f / total_predictions * 100).round(2)
                         else
                           0
                         end
    correct_percentage = if total_predictions > 0
                           (correct_count.to_f / total_predictions * 100).round(2)
                         else
                           0
                         end
    incorrect_percentage = if total_predictions > 0
                             (incorrect_count.to_f / total_predictions * 100).round(2)
                           else
                             0
                           end

    # Calculate total points
    total_points = round_predictions.sum(&:total_points)

    # Calculate highest, average, and most perfect in a round
    round_stats = calculate_round_stats(round_predictions)

    {
      total_points:,
      total_predictions:,
      prediction_accuracy:,
      perfect_predictions: {
        count: perfect_count,
        percentage: perfect_percentage
      },
      correct_predictions: {
        count: correct_count,
        percentage: correct_percentage
      },
      incorrect_predictions: {
        count: incorrect_count,
        percentage: incorrect_percentage
      },
      highest_round_score: round_stats[:highest_round_score],
      average_round_score: round_stats[:average_round_score],
      most_perfect_predictions_in_a_round: round_stats[:most_perfect_predictions_in_a_round]
    }
  end

  def self.calculate_advanced_stats(user, filters = {})
    # Get basic stats first
    basic_stats = calculate_basic_stats(user, filters)

    # Filter round predictions based on filters
    round_predictions = filter_round_predictions(user, filters)

    # Calculate round stats for consistency and streaks
    round_stats = round_predictions.map do |rp|
      rp.calculate_match_prediction_points unless rp.total_points
      {
        id: rp.id,
        matchday: rp.matchday,
        competition_id: rp.competition_id,
        season_id: rp.season_id,
        total_points: rp.total_points,
        perfect_predictions: rp.match_predictions.where(points: 3).count,
        is_finished: rp.match_predictions.all? { |mp| mp.points.present? }
      }
    end

    # Calculate consistency (standard deviation of round points)
    finished_rounds = round_stats.select { |rs| rs[:is_finished] }
    total_points_array = finished_rounds.map { |rs| rs[:total_points] }
    consistency_rating = calculate_standard_deviation(total_points_array)

    # Calculate streak data
    current_streak, longest_streak = calculate_streaks(round_stats)

    # Merge with basic stats and return
    basic_stats.merge({
                        consistency_rating:,
                        streaks: {
                          current: current_streak,
                          longest: longest_streak
                        }
                      })
  end

  def self.calculate_competition_stats(user, filters = {})
    # Get competition IDs from filters or all user's competitions
    competition_ids = if filters[:competition_id]
                        [filters[:competition_id].to_i]
                      else
                        user.round_predictions.pluck(:competition_id).uniq
                      end

    # Calculate stats for each competition
    competition_stats = competition_ids.map do |comp_id|
      competition = Competition.find(comp_id)

      # Apply season filter if provided
      season_id = filters[:season_id] || competition.current_season&.id

      # Skip if no season found
      next unless season_id

      # Get round predictions for this competition and season
      round_predictions = user.round_predictions.where(competition_id: comp_id, season_id:)

      # Get match predictions
      match_predictions = MatchPrediction.where(round_prediction_id: round_predictions.pluck(:id))

      # Calculate stats
      total = match_predictions.count
      perfect = match_predictions.where(points: 3).count
      correct = match_predictions.where(points: 1).count
      incorrect = match_predictions.where(points: 0).count
      points = round_predictions.sum(&:total_points)

      # Get favorite team performance if user has one for this competition
      favorite_team_performance = calculate_favorite_team_performance(user, comp_id, season_id)

      {
        competition_id: comp_id,
        competition_name: competition.name,
        season_id:,
        points:,
        total_predictions: total,
        accuracy: total > 0 ? ((perfect + correct).to_f / total * 100).round(2) : 0,
        perfect_predictions: {
          count: perfect,
          percentage: total > 0 ? (perfect.to_f / total * 100).round(2) : 0
        },
        correct_predictions: {
          count: correct,
          percentage: total > 0 ? (correct.to_f / total * 100).round(2) : 0
        },
        incorrect_predictions: {
          count: incorrect,
          percentage: total > 0 ? (incorrect.to_f / total * 100).round(2) : 0
        },
        favorite_team_performance:
      }
    end.compact

    { competition_performance: competition_stats }
  end

  def self.calculate_comparative_stats(user, filters = {})
    # Get basic stats
    basic_stats = calculate_basic_stats(user, filters)

    # Calculate global rank
    global_rank = calculate_global_rank(user, filters)

    # Calculate percentile
    percentile = calculate_percentile_ranking(user, global_rank[:total_users])

    # Calculate points behind leader
    points_behind_leader = if global_rank[:user_points] > 0
                             global_rank[:top_user_points] - global_rank[:user_points]
                           else
                             0
                           end

    {
      total_points: basic_stats[:total_points],
      global_rank: global_rank[:rank],
      total_users: global_rank[:total_users],
      percentile_ranking: percentile,
      points_behind_leader:
    }
  end

  def self.calculate_all_stats(user, filters = {})
    # Combine all stat types
    basic = calculate_basic_stats(user, filters)
    advanced = calculate_advanced_stats(user, filters)
    competition = calculate_competition_stats(user, filters)
    comparative = calculate_comparative_stats(user, filters)

    # Remove duplicates and merge
    basic.merge(
      advanced.except(:total_points, :total_predictions, :prediction_accuracy,
                      :perfect_predictions, :correct_predictions, :incorrect_predictions)
    ).merge(
      competition
    ).merge(
      comparative.except(:total_points)
    )
  end

  # Helper methods

  def self.filter_round_predictions(user, filters = {})
    round_predictions = user.round_predictions

    # Filter by competition if provided
    round_predictions = round_predictions.where(competition_id: filters[:competition_id]) if filters[:competition_id]

    # Filter by season if provided
    round_predictions = round_predictions.where(season_id: filters[:season_id]) if filters[:season_id]

    # Filter by time period if provided
    if filters[:time_period]
      case filters[:time_period]
      when 'current_season'
        # Get current seasons for all competitions
        current_season_ids = Competition.all.map { |c| c.current_season&.id }.compact
        round_predictions = round_predictions.where(season_id: current_season_ids)
      when 'last_month'
        round_predictions = round_predictions.joins(:match_predictions)
                                             .where('match_predictions.created_at >= ?', 1.month.ago)
                                             .distinct
      end
    end

    round_predictions
  end

  def self.calculate_round_stats(round_predictions)
    # Ensure points are calculated
    round_predictions.each do |rp|
      rp.calculate_match_prediction_points unless rp.total_points
    end

    # Get finished rounds
    finished_rounds = round_predictions.select do |rp|
      rp.match_predictions.all? { |mp| mp.points.present? }
    end

    # Calculate stats
    total_points = finished_rounds.map(&:total_points)
    perfect_predictions = round_predictions.map do |rp|
      rp.match_predictions.where(points: 3).count
    end

    {
      highest_round_score: total_points.max || 0,
      average_round_score: total_points.any? ? (total_points.sum.to_f / total_points.size).round(2) : 0,
      most_perfect_predictions_in_a_round: perfect_predictions.max || 0
    }
  end

  def self.calculate_standard_deviation(values)
    return 0 if values.empty?

    mean = values.sum.to_f / values.size
    sum_squared_differences = values.sum { |value| (value - mean)**2 }
    Math.sqrt(sum_squared_differences / values.size).round(2)
  end

  def self.calculate_streaks(round_stats)
    # Sort round_stats by matchday
    sorted_rounds = round_stats.sort_by { |rs| rs[:matchday] || 0 }

    current_streak = 0
    longest_streak = 0
    temp_streak = 0

    sorted_rounds.each do |rs|
      if rs[:is_finished] && rs[:perfect_predictions] > 0
        temp_streak += 1
        longest_streak = [longest_streak, temp_streak].max
      else
        temp_streak = 0
      end
    end

    # Calculate current streak (most recent rounds)
    current_streak = 0
    sorted_rounds.reverse.each do |rs|
      break unless rs[:is_finished] && rs[:perfect_predictions] > 0

      current_streak += 1
    end

    [current_streak, longest_streak]
  end

  def self.calculate_favorite_team_performance(user, competition_id, season_id)
    # Get user's favorite team for this competition
    favorite = user.favorite_teams.find_by(competition_id:)
    return nil unless favorite

    team = favorite.team

    # Get matches involving this team in the specified season
    team_matches = Match.where(competition_id:, season_id:)
                        .where('home_team_id = ? OR away_team_id = ?', team.id, team.id)
                        .pluck(:id)

    # Get user's predictions for these matches
    predictions = user.match_predictions.joins(:round_prediction)
                      .where(match_id: team_matches)
                      .where(round_predictions: { season_id: })

    total = predictions.count
    perfect = predictions.where(points: 3).count
    correct = predictions.where(points: 1).count
    incorrect = predictions.where(points: 0).count

    {
      team_id: team.id,
      team_name: team.name,
      total_predictions: total,
      accuracy: total > 0 ? ((perfect + correct).to_f / total * 100).round(2) : 0,
      perfect_predictions: {
        count: perfect,
        percentage: total > 0 ? (perfect.to_f / total * 100).round(2) : 0
      },
      correct_predictions: {
        count: correct,
        percentage: total > 0 ? (correct.to_f / total * 100).round(2) : 0
      },
      incorrect_predictions: {
        count: incorrect,
        percentage: total > 0 ? (incorrect.to_f / total * 100).round(2) : 0
      }
    }
  end

  def self.calculate_global_rank(user, filters = {})
    # Get all users with points
    all_users = User.all

    # Calculate points for each user based on filters
    user_points = {}

    all_users.each do |u|
      # Filter round predictions based on filters
      round_predictions = filter_round_predictions(u, filters)

      # Calculate total points
      points = round_predictions.sum(&:total_points)

      user_points[u.id] = points
    end

    # Sort users by points (descending)
    sorted_users = user_points.sort_by { |_, points| -points }

    # Find user's position (1-based index)
    user_rank = sorted_users.find_index { |id, _| id == user.id }
    user_rank = user_rank ? user_rank + 1 : all_users.count

    # Get top user's points
    top_user_points = sorted_users.first ? sorted_users.first[1] : 0

    {
      rank: user_rank,
      total_users: all_users.count,
      user_points: user_points[user.id] || 0,
      top_user_points:
    }
  end

  def self.calculate_percentile_ranking(user, total_users, rank = nil)
    rank ||= calculate_global_rank(user)[:rank]

    return nil unless rank && total_users > 0

    # Higher percentile is better (99th percentile is top 1%)
    ((total_users - rank + 1).to_f / total_users * 100).round(2)
  end
end
