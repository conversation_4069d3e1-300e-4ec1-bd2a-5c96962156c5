# app/services/league_season_transition_service.rb
class LeagueSeasonTransitionService
  def initialize(competition, new_season)
    @competition = competition
    @new_season = new_season
  end

  # Update all leagues for this competition to use the new season
  def transition_leagues_to_new_season
    # Find the old season (if any)
    old_season = find_old_season

    # Get all leagues for this competition
    leagues = League.for_competition(@competition.id)
    transitioned_count = 0

    # First, archive leagues from the old season if it exists
    archive_old_season_leagues(old_season) if old_season

    leagues.each do |league|
      # Store current season data
      league.store_current_season_data if league.season_id.present?

      # Update to new season
      league.update!(season_id: @new_season.id)

      # Always unarchive leagues for the new season
      league.unarchive! if league.archived?

      transitioned_count += 1
    rescue StandardError => e
      Rails.logger.error "Failed to transition league #{league.id} to new season: #{e.message}"
    end

    transitioned_count
  end

  # Archive all leagues for the old season
  def archive_old_season_leagues(old_season)
    Rails.logger.info "Archiving leagues for old season #{old_season.id}"
    service = LeagueArchiveService.new(old_season)
    archived_count = service.archive_all_leagues
    Rails.logger.info "Archived #{archived_count} leagues from old season #{old_season.id}"
  end

  # Find the old season (the previous current_season for this competition)
  def find_old_season
    # Look for the most recent season that isn't the current one
    Season.where(competition_id: @competition.id)
          .where.not(id: @new_season.id)
          .order(end_date: :desc)
          .first
  end

  # Check if a competition has a new season
  def new_season?
    @competition.current_season_id == @new_season.id &&
      League.for_competition(@competition.id).where.not(season_id: @new_season.id).exists?
  end
end
