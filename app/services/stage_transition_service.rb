# app/services/stage_transition_service.rb
class StageTransitionService
  STAGES_ORDER = %w[LEAGUE_STAGE PLAYOFFS LAST_16 QUARTER_FINALS SEMI_FINALS FINAL].freeze

  def initialize(season)
    @season = season
    @cup_matchday_service = CupMatchdayService.new(season)
  end

  def update_stage_and_matchday!
    return unless @season.competition.competition_type == 'CUP'

    current_stage = @season.current_stage
    Rails.logger.info "Current stage: #{current_stage}"

    return unless current_stage

    # Find the most advanced stage that has finished matches
    most_advanced_stage_with_finished = find_most_advanced_stage_with_finished

    if most_advanced_stage_with_finished && STAGES_ORDER.index(most_advanced_stage_with_finished) > STAGES_ORDER.index(current_stage)
      Rails.logger.info "Found finished matches in advanced stage: #{most_advanced_stage_with_finished}. Updating current stage."

      # Find the latest matchday in this stage with finished matches
      latest_matchday = find_latest_matchday_in_stage(most_advanced_stage_with_finished)

      # Update to this stage and matchday
      overall_matchday = @cup_matchday_service.calculate_overall_matchday
      @season.update!(
        current_stage: most_advanced_stage_with_finished,
        current_matchday: latest_matchday,
        overall_matchday:
      )

      Rails.logger.info "Stage updated to: #{most_advanced_stage_with_finished}, stage matchday: #{latest_matchday}, overall matchday: #{overall_matchday}"
      return
    end

    # Normal stage transition logic if no advanced stages have finished matches
    if matches_complete_for_stage?(current_stage)
      next_stage = next_stage(current_stage)
      Rails.logger.info "Next stage: #{next_stage}"

      return unless next_stage

      fetch_and_check_next_stage_matches(next_stage)
    else
      update_matchday_within_stage(current_stage)
    end
  end

  private

  def find_most_advanced_stage_with_finished
    # Start from the end of STAGES_ORDER and work backwards
    STAGES_ORDER.reverse.each do |stage|
      matches_in_stage = Match.where(stage:, season_id: @season.id)
      return stage if matches_in_stage.any? && matches_in_stage.any? { |m| m.status == 'FINISHED' }
    end
    nil
  end

  def find_latest_matchday_in_stage(stage)
    # Find the highest matchday number that has any finished matches
    matches = Match.where(stage:, season_id: @season.id, status: 'FINISHED')
    return 1 if matches.empty?

    max_matchday = matches.maximum(:matchday)
    max_matchday || 1
  end

  def update_matchday_within_stage(stage)
    current_matchday = @season.current_matchday
    return unless matches_complete_for_stage_and_matchday?(stage, current_matchday)

    @season.update!(current_matchday: current_matchday + 1)
    Rails.logger.info "Matchday updated to: #{current_matchday + 1} within stage: #{stage}"
  end

  def fetch_and_check_next_stage_matches(next_stage)
    competition = Competition.find_by(code: @season.competition.code)

    if competition && competition.current_season.matches.where(stage: next_stage).any? do |match|
         %w[TIMED SCHEDULED FINISHED].include?(match.status)
       end

      overall_matchday = @cup_matchday_service.calculate_overall_matchday

      @season.update!(
        current_stage: next_stage,
        current_matchday: 1,
        overall_matchday:
      )
      Rails.logger.info "Stage updated to: #{next_stage}, stage matchday: 1, overall matchday: #{overall_matchday}"
    end
  end

  def matches_complete_for_stage?(stage)
    matches = Match.where(stage:, season_id: @season.id)
    return false if matches.empty?

    matches.all? { |match| %w[FINISHED POSTPONED].include?(match.status) }
  end

  def matches_complete_for_stage_and_matchday?(stage, matchday)
    matches = Match.where(stage:, matchday:, season_id: @season.id)
    return false if matches.empty?

    matches.all? { |match| %w[FINISHED POSTPONED].include?(match.status) }
  end

  def next_stage(current_stage)
    current_index = STAGES_ORDER.index(current_stage)
    return unless current_index && current_index < STAGES_ORDER.length - 1

    STAGES_ORDER[current_index + 1]
  end
end
