class MatchdayOptionsService
  STAGE_MATCHDAYS = {
    'LEAGUE_STAGE' => 8,
    'PLAYOFFS' => 2,
    'LAST_16' => 2,
    'QUARTER_FINALS' => 2,
    'SEMI_FINALS' => 2,
    'FINAL' => 1
  }.freeze

  STAGES_ORDER = %w[LEAGUE_STAGE PLAYOFFS LAST_16 QUARTER_FINALS SEMI_FINALS FINAL].freeze

  def initialize(season)
    @season = season
  end

  def generate_matchday_options
    return regular_season_options if @season.competition.competition_type == 'LEAGUE'

    cup_competition_options
  end

  private

  def regular_season_options
    # Get all distinct matchdays
    all_matchdays = @season.matches.distinct.pluck(:matchday).sort

    # Get playable matchdays - those with at least one FINISHED match or ongoing match
    playable_matchdays = all_matchdays.select do |matchday|
      matchday_has_finished_or_ongoing_matches?('REGULAR_SEASON', matchday)
    end

    # Get the current matchday (even if no matches are ongoing)
    current_matchday = determine_current_matchday('REGULAR_SEASON')

    # Combine all types of matchdays
    playable_matchdays = (playable_matchdays + [current_matchday]).compact.uniq.sort

    # Create options for each playable matchday
    playable_matchdays.map do |matchday|
      {
        matchday:,
        stage: 'REGULAR_SEASON',
        overall_matchday: matchday
      }
    end
  end

  def cup_competition_options
    options = []
    overall_matchday = 0

    STAGES_ORDER.each do |stage|
      # Skip future stages
      break if stage_is_future?(stage)

      # Find all matchdays that exist for this stage
      all_stage_matchdays = @season.matches.where(stage:).distinct.pluck(:matchday).sort

      # Get current matchday for this stage
      current_stage_matchday = determine_current_matchday(stage)

      # Add matchdays that have finished/ongoing matches or are the current matchday
      all_stage_matchdays.each do |matchday|
        next unless matchday_has_finished_or_ongoing_matches?(stage, matchday) ||
                    matchday == current_stage_matchday

        overall_matchday += 1
        options << create_option(matchday, stage, overall_matchday)
      end
    end

    options
  end

  # Find the current matchday even when no matches are ongoing
  def determine_current_matchday(stage) # rubocop:disable Metrics/CyclomaticComplexity
    # First check if there are any ongoing matches for this stage
    matches = @season.matches.where(stage:)
    ongoing_matches = matches.select { |match| %w[LIVE IN_PLAY PAUSED].include?(match.status) }

    return ongoing_matches.first.matchday if ongoing_matches.any?

    # If no ongoing matches, find the most recent finished matchday
    finished_matchdays = matches.select { |match| match.status == 'FINISHED' }
                                .map(&:matchday).uniq.sort

    return finished_matchdays.last if finished_matchdays.any?

    # If no finished matches either, return the earliest scheduled matchday
    scheduled_matchdays = matches.select { |match| match.status == 'SCHEDULED' || match.status == 'TIMED' }
                                 .map(&:matchday).uniq.sort

    scheduled_matchdays.first
  end

  def stage_is_future?(stage)
    current_stage_index = STAGES_ORDER.index(@season.current_stage)
    stage_index = STAGES_ORDER.index(stage)

    stage_index > current_stage_index
  end

  def create_option(stage_matchday, stage, overall_matchday)
    {
      matchday: stage_matchday,
      stage:,
      overall_matchday:
    }
  end

  def matches_all_finished_for_stage_and_matchday?(stage, matchday)
    matches = @season.matches.where(stage:, matchday:)
    return false if matches.empty?

    matches.all? { |match| %w[FINISHED POSTPONED].include?(match.status) }
  end

  def matches_have_ongoing_for_stage_and_matchday?(stage, matchday)
    matches = @season.matches.where(stage:, matchday:)
    return false if matches.empty?

    matches.any? { |match| %w[LIVE IN_PLAY PAUSED].include?(match.status) }
  end

  # Helper method to check if a matchday has any FINISHED or ongoing matches
  def matchday_has_finished_or_ongoing_matches?(stage, matchday)
    matches = @season.matches.where(stage:, matchday:)
    return false if matches.empty?

    matches.any? { |match| match.status == 'FINISHED' || %w[LIVE IN_PLAY PAUSED].include?(match.status) }
  end
end
