class CustomDeviseMailer < Devise::Mailer
  helper :application # Use proper Rails way to include helpers
  include ApplicationHelper # Include the helper module directly
  include Devise::Controllers::UrlHelpers

  default from: -> { "#{app_name} <#{app_email}>" }
  default reply_to: -> { app_email }

  layout 'mailer'

  # Override devise methods as needed
  def confirmation_instructions(record, token, opts = {})
    @token = token
    @resource = record
    mail(to: record.email, subject: "Confirm your #{app_name} account")
  end

  def reset_password_instructions(record, token, opts = {})
    @token = token
    @resource = record
    mail(to: record.email, subject: "Reset your #{app_name} password")
  end
end
