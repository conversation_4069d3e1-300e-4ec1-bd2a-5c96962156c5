# app/jobs/subscription_verification_job.rb
# This job verifies that users in subscriber-only leagues are still subscribed to the required YouTube channels
# Uses a tiered approach to reduce API calls based on user activity
class SubscriptionVerificationJob < ApplicationJob
  queue_as :youtube

  def perform(tier = nil)
    # Process grace period expirations regardless of tier
    process_expired_grace_periods

    # Only verify a specific tier if provided
    # This relies on scheduled jobs with explicit tier arguments
    verify_tier(tier) if tier
  end

  private

  def verify_tier(tier)
    Rails.logger.info "Starting subscription verification for #{tier} users"

    # Get users to verify based on tier
    users = case tier.to_sym
            when :active
              # Users active in the last 7 days
              User.where('last_active_at > ?', 7.days.ago)
            when :semi_active
              # Users active in the last 30 days but not in the last 7 days
              User.where('last_active_at > ? AND last_active_at <= ?', 30.days.ago, 7.days.ago)
            when :inactive
              # Users not active in the last 30 days
              User.where('last_active_at <= ? OR last_active_at IS NULL', 30.days.ago)
            else
              User.none
            end

    # Only verify users with YouTube credentials
    users = users.where.not(youtube_credentials: nil)

    Rails.logger.info "Found #{users.count} #{tier} users to verify"

    # Check if we have enough quota for all verifications
    total_quota_needed = users.count * 100 # 100 units per verification
    if users.count.positive? && !YoutubeQuotaService.can_make_api_call?("batch_verification_#{tier}",
                                                                        total_quota_needed)
      Rails.logger.error "Not enough quota to verify all #{tier} users. Needed: #{total_quota_needed}"
      return
    end

    # Process users in batches to avoid memory issues
    users.find_in_batches(batch_size: 50) do |batch|
      batch.each do |user|
        verify_user_subscriptions(user)
      end
    end

    Rails.logger.info "Completed subscription verification for #{tier} users"
  end

  def verify_user_subscriptions(user)
    # Find all subscriber-only leagues the user is a member of
    memberships = Membership.joins(:league)
                            .where(user:)
                            .where(leagues: { subscriber_only: true })

    memberships.each do |membership|
      league = membership.league

      # Skip leagues without a YouTube channel ID
      next unless league.youtube_channel_id.present?

      # Check if user is still subscribed
      is_subscribed = league.user_meets_subscription_requirements?(user)

      # Update membership status
      if is_subscribed
        # Update verification timestamp
        membership.update(
          subscription_status: 'active',
          subscription_verified_at: Time.current
        )
      else
        handle_unsubscribed_user(membership, league)
      end
    end
  end

  def process_expired_grace_periods
    # Check users in grace period whose time has expired
    grace_period_memberships = Membership.where(
      subscription_status: 'grace_period',
      grace_period_ends_at: ..Time.current
    )

    Rails.logger.info "Processing #{grace_period_memberships.count} expired grace periods"

    grace_period_memberships.each do |membership|
      # Remove user from league if grace period has expired
      user = membership.user
      league = membership.league

      membership.destroy

      # Notify user (would use mailer in a real implementation)
      Rails.logger.info "User #{user.id} removed from league #{league.id} after grace period expired"
    end
  end

  def handle_unsubscribed_user(membership, league)
    case league.unsubscribe_policy
    when 'remove_immediately'
      # Remove user from league
      user = membership.user
      membership.destroy

      # Notify user (would use mailer in a real implementation)
      Rails.logger.info "User #{user.id} removed from league #{league.id} due to unsubscribe"
    when 'grace_period'
      # Mark user as pending removal and set grace period
      membership.update(
        subscription_status: 'grace_period',
        grace_period_ends_at: Time.current + 7.days
      )

      # Notify user about grace period (would use mailer in a real implementation)
      Rails.logger.info "User #{membership.user.id} in grace period for league #{league.id}"
    end
  end
end
