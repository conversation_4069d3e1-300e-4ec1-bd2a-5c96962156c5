class TodaysMatchesUpdateJob < ApplicationJob
  queue_as :default

  # Add a class method to test direct scheduling
  def self.schedule_direct_test(competition_code = 'PL', delay_minutes = 2)
    set(wait: delay_minutes.minutes).perform_later(competition_code)
  end

  def perform(competition_code)
    Rails.logger.info "Updating today's matches for competition #{competition_code} at #{Time.current}"
    competition = Competition.find_by(code: competition_code)
    return unless competition&.current_season

    season = competition.current_season
    today = Date.today
    # Use the appropriate service based on competition code
    if %w[PL CL].include?(competition_code)
      update_football_data_matches(competition, season, today)
    elsif competition_code == 'SV'
      update_api_football_matches(competition, season, today)
    end
    Rails.logger.info "Today's matches update for #{competition_code} completed"
  end

  private

  def update_football_data_matches(competition, season, today)
    service = FootballDataService.new
    service.update_matches_by_date_range(competition, today, today, season)
  end

  def update_api_football_matches(competition, season, today)
    service = ApiFootballService.new
    service.update_matches_by_date_range(competition.external_service_id, today, today, season)
  end
end
