class DailyMatchUpdateJob
  include Sidekiq::Worker

  # Add sidekiq options if needed
  # sidekiq_options queue: 'default', retry: true

  def perform(*args)
    Rails.logger.info '======================================='
    Rails.logger.info "Starting DailyMatchUpdateJob at #{Time.current}"
    Rails.logger.info "Arguments: #{args.inspect}"

    competition_code = args.first

    # Update matches for each competition type
    if competition_code == 'ALL'
      Rails.logger.info 'Updating all competitions: PL, CL, SV'
      update_competition('PL')
      update_competition('CL')
      update_competition('BDkk')
      update_competition('SV') # Added SV competition with rate limiting
    else
      update_competition(competition_code)
    end

    # Check stage transitions for cup competitions
    check_stage_transitions

    # Schedule more frequent updates for today's matches
    schedule_todays_match_updates

    Rails.logger.info "DailyMatchUpdateJob completed at #{Time.current}"
    Rails.logger.info '======================================='
  end

  private

  def update_competition(competition_code)
    Rails.logger.info "Starting update for competition #{competition_code} at #{Time.current}"
    competition = Competition.find_by(code: competition_code)

    unless competition&.current_season
      Rails.logger.warn "No current season found for competition #{competition_code}"
      return
    end

    Rails.logger.info "Found current season for #{competition_code}, updating matches..."
    MatchUpdaterService.update_matches(competition)
    Rails.logger.info "Completed update for competition #{competition_code}"
  end

  def check_stage_transitions
    Rails.logger.info 'Checking stage transitions for cup competitions'
    Competition.where(competition_type: 'CUP').each do |competition|
      next unless competition.current_season

      Rails.logger.info "Checking stage transition for #{competition.code}"
      competition.current_season.check_stage_transition
    end
  end

  def schedule_todays_match_updates
    Rails.logger.info "Looking for today's matches at #{Time.current}"
    todays_matches = Match.where(status: %w[TIMED SCHEDULED])
                          .where('DATE(utc_date) = ?', Date.today)
                          .order(utc_date: :asc)

    if todays_matches.empty?
      Rails.logger.info 'No matches found for today'
      return
    end

    Rails.logger.info "Found #{todays_matches.count} matches scheduled for today"

    todays_matches.group_by { |match| match.season.competition.code }.each do |competition_code, matches|
      schedule_match_updates_for_competition(competition_code, matches)
    end
  end

  def schedule_match_updates_for_competition(competition_code, matches)
    first_match = matches.min_by(&:utc_date)
    last_match = matches.max_by(&:utc_date)

    Rails.logger.info "Scheduling updates for #{competition_code}:"
    Rails.logger.info "  First match at: #{first_match.utc_date}"
    Rails.logger.info "  Last match at: #{last_match.utc_date}"

    if first_match && first_match.utc_date > Time.current
      subtract_time = if competition_code == 'SV'
                        60.minutes # More time before match for rate-limited API
                      else
                        10.minutes
                      end

      pre_match_time = first_match.utc_date - subtract_time
      Rails.logger.info "Scheduling pre-match update for #{competition_code} at #{pre_match_time.iso8601}"

      DynamicMatchUpdaterJob.set(wait_until: pre_match_time).perform_later(
        competition_code,
        first_match.matchday,
        first_match.stage
      )
    end

    # Special handling for SV competition - reduced frequency due to rate limits
    if competition_code == 'SV'
      return unless last_match

      update_time = last_match.utc_date + 2.hours
      if update_time > Time.current
        Rails.logger.info "Scheduling rate-limited update for #{competition_code} at #{update_time.iso8601}"

        DynamicMatchUpdaterJob.set(wait_until: update_time).perform_later(
          competition_code,
          last_match.matchday,
          last_match.stage
        )
      end
    else
      # For PL and CL, schedule more frequent updates
      current_time = Time.current
      end_of_day = Time.current.end_of_day

      while current_time < end_of_day
        add_time = %w[PL CL].include?(competition_code) ? 10.minutes : 30.minutes
        next_update = current_time + add_time

        Rails.logger.info "Scheduling update for #{competition_code} at #{next_update.iso8601}"

        DynamicMatchUpdaterJob.set(wait_until: next_update).perform_later(
          competition_code,
          nil, # nil means update all active matches
          nil
        )

        current_time = next_update
      end
    end
  end
end
