class CheckSeasonCompletionJob < ApplicationJob
  queue_as :default

  # This job checks if any seasons are completed and marks them as such
  # It can be run with a specific competition code or for all competitions
  def perform(competition_code = nil)
    if competition_code.present?
      check_competition(competition_code)
    else
      check_all_competitions
    end
  end

  private

  def check_all_competitions
    Rails.logger.info "Checking all competitions for completed seasons at #{Time.current}"
    Competition.all.each do |competition|
      check_competition_seasons(competition)
    end
  end

  def check_competition(code)
    Rails.logger.info "Checking competition #{code} for completed seasons at #{Time.current}"
    competition = Competition.find_by(code:)
    return unless competition

    check_competition_seasons(competition)
  end

  def check_competition_seasons(competition)
    # Check the current season
    check_season(competition.current_season) if competition.current_season.present?

    # Also check any other seasons that might be ending soon
    upcoming_end_seasons = Season.where(competition_id: competition.id)
                                 .where('end_date <= ?', 1.month.from_now)
                                 .where(completed: false)

    upcoming_end_seasons.each do |season|
      check_season(season)
    end
  end

  def check_season(season)
    return unless season

    # Check if the season is over and mark it as completed if necessary
    season.check_if_completed

    # If the season is now marked as completed, archive the leagues
    return unless season.completed?

    Rails.logger.info "Season #{season.id} for competition #{season.competition.code} is completed. Archiving leagues."
    LeagueArchiveService.new(season).archive_all_leagues
  end
end
