class BatchRoundPredictionsJob < ApplicationJob
  queue_as :default

  def perform(user_id, prediction_data, job_id)
    user = User.find(user_id)
    successful_predictions = []
    failed_predictions = []

    prediction_data.each do |round_prediction_data|
      # Create round prediction logic (similar to controller)
      round_prediction = user.round_predictions.new(
        matchday: round_prediction_data[:matchday],
        season_id: round_prediction_data[:season_id],
        competition_id: round_prediction_data[:competition_id],
        stage: round_prediction_data[:stage]
      )

      # Add match predictions
      round_prediction_data[:match_predictions].each do |match_pred_data|
        round_prediction.match_predictions.new(match_pred_data)
      end

      if round_prediction.save
        successful_predictions << round_prediction.id
      else
        failed_predictions << {
          data: round_prediction_data,
          errors: round_prediction.errors.full_messages
        }
      end
    end

    # Store results in Redis or database for retrieval
    Rails.cache.write(
      "batch_predictions:#{job_id}",
      {
        successful: successful_predictions,
        failed: failed_predictions,
        status: 'completed',
        completed_at: Time.current
      },
      expires_in: 1.hour
    )
  end
end
