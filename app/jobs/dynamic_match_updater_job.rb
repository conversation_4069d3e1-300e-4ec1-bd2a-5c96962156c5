class DynamicMatchUpdaterJob < ApplicationJob
  queue_as :default

  retry_on StandardError, wait: :exponentially_longer, attempts: 5
  discard_on ApiError

  def perform(competition_code, match_day, stage)
    Rails.logger.info "Updating matches for #{competition_code}, day #{match_day}, stage #{stage}"

    Timeout.timeout(2.minutes) do
      MatchUpdaterService.update_matches_for_matchday(competition_code, match_day, stage)
    end

    Rails.logger.info "Completed match update for #{competition_code}, day #{match_day}, stage #{stage}"
  rescue ActiveRecord::RecordInvalid => e
    raise e unless e.message.include?('External service has already been taken')

    Rails.logger.warn "Duplicate external service detected for #{competition_code}: #{e.message}"
    # You might want to discard this job or handle it differently
    raise e # Still raise to trigger retry logic, or use 'return' to silently discard
  rescue Timeout::Error
    Rails.logger.error "Timeout updating matches for #{competition_code}, day #{match_day}"
    raise
  end
end
