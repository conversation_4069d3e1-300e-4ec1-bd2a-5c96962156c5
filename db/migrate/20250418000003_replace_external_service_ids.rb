class ReplaceExternalServiceIds < ActiveRecord::Migration[7.0]
  def change
    add_column :areas, :external_service_id, :string
    add_column :competitions, :external_service_id, :string
    add_column :matches, :external_service_id, :string
    add_column :postponed_matches, :external_service_id, :string
    add_column :seasons, :external_service_id, :string
    add_column :teams, :external_service_id, :string

    add_index :areas, :external_service_id
    add_index :competitions, :external_service_id
    add_index :matches, :external_service_id
    add_index :postponed_matches, :external_service_id
    add_index :seasons, :external_service_id
    add_index :teams, :external_service_id

    reversible do |dir|
      dir.up do
        # Migrate existing IDs to the new `external_service_id` field
        execute <<-SQL.squish
          UPDATE areas
          SET external_service_id = COALESCE(football_data_id::text, api_football_id::text);

          UPDATE competitions
          SET external_service_id = COALESCE(football_data_id::text, api_football_id::text);

          UPDATE matches
          SET external_service_id = COALESCE(football_data_id::text, api_football_id::text);

          UPDATE postponed_matches
          SET external_service_id = COALESCE(football_data_id::text, api_football_id::text);

          UPDATE seasons
          SET external_service_id = COALESCE(football_data_id::text, api_football_id::text);

          UPDATE teams
          SET external_service_id = COALESCE(football_data_id::text, api_football_id::text);
        SQL
      end
    end

    # Remove old service-specific IDs
    remove_column :areas, :football_data_id
    remove_column :areas, :api_football_id
    remove_column :competitions, :football_data_id
    remove_column :competitions, :api_football_id
    remove_column :matches, :football_data_id
    remove_column :matches, :api_football_id
    remove_column :postponed_matches, :football_data_id
    remove_column :postponed_matches, :api_football_id
    remove_column :seasons, :football_data_id
    remove_column :seasons, :api_football_id
    remove_column :teams, :football_data_id
    remove_column :teams, :api_football_id
  end
end
