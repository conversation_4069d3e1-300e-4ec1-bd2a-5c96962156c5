class AddStageToRoundPredictions < ActiveRecord::Migration[7.0]
  def up
    add_column :round_predictions, :stage, :string, null: true

    # Populate stage based on competition type
    execute <<-SQL
      UPDATE round_predictions
      SET stage = CASE#{' '}
        WHEN competitions.competition_type = 'CUP' THEN 'LEAGUE_STAGE'
        ELSE 'REGULAR_SEASON'
      END
      FROM competitions
      WHERE round_predictions.competition_id = competitions.id
    SQL

    # Make stage required after population
    change_column_null :round_predictions, :stage, false
  end

  def down
    remove_column :round_predictions, :stage
  end
end
