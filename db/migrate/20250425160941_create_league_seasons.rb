class CreateLeagueSeasons < ActiveRecord::Migration[7.0]
  def change
    create_table :league_seasons do |t|
      t.references :league, null: false, foreign_key: true
      t.references :season, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.integer :points, default: 0
      t.integer :correct_predictions, default: 0
      t.integer :incorrect_predictions, default: 0
      t.integer :perfect_predictions, default: 0
      t.integer :position

      t.timestamps
    end

    # Add a unique index to ensure a user can only have one entry per league and season
    add_index :league_seasons, %i[league_id season_id user_id], unique: true
  end
end
