class CreateRoundPredictions < ActiveRecord::Migration[7.0]
  def change
    create_table :round_predictions do |t|
      t.integer :matchday, null: false
      t.references :user, null: false, foreign_key: true
      t.references :season, null: false, foreign_key: true
      t.references :competition, null: false, foreign_key: true

      t.timestamps
      t.index %i[user_id season_id matchday], unique: true
    end
  end
end
