class CreatePostponedMatches < ActiveRecord::Migration[7.0]
  def change
    create_table :postponed_matches do |t|
      t.bigint :match_id, null: false
      t.integer :external_id, null: false
      t.datetime :last_checked_at
      t.datetime :check_again_after

      t.timestamps
    end

    add_index :postponed_matches, :match_id, unique: true
    add_index :postponed_matches, :external_id, unique: true
    add_foreign_key :postponed_matches, :matches
  end
end
