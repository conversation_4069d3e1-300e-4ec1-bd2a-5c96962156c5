class CreateUsers < ActiveRecord::Migration[7.0]
  def change
    create_table :users do |t|
      t.string :email, null: false
      t.string :username, null: false
      t.string :encrypted_password, null: false
      t.boolean :admin, default: false
      t.string :reset_password_token
      t.datetime :reset_password_sent_at
      t.datetime :remember_created_at

      t.timestamps
      t.index :email, unique: true
      t.index :username, unique: true
      t.index :reset_password_token, unique: true
    end
  end
end
