class RenameExternalIdColumnsAddApiFootballId < ActiveRecord::Migration[7.0]
  def change
    rename_column :areas, :external_id, :football_data_id
    add_column :areas, :api_football_id, :integer

    rename_column :competitions, :external_id, :football_data_id
    add_column :competitions, :api_football_id, :integer

    rename_column :teams, :external_id, :football_data_id
    add_column :teams, :api_football_id, :integer

    rename_column :matches, :external_id, :football_data_id
    add_column :matches, :api_football_id, :integer

    rename_column :seasons, :external_id, :football_data_id
    add_column :seasons, :api_football_id, :integer

    rename_column :postponed_matches, :external_id, :football_data_id
    add_column :postponed_matches, :api_football_id, :integer
  end
end
