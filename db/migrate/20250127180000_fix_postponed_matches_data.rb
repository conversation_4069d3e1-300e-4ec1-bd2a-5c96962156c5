class FixPostponedMatchesData < ActiveRecord::Migration[7.0]
  def up
    # First ensure all matches have an external_id
    Match.where(external_id: nil).find_each do |match|
      match.update_column(:external_id, match.id)
    end

    # Remove any invalid postponed matches
    PostponedMatch.where(external_id: nil).destroy_all

    # Recreate postponed matches with proper external_ids
    Match.where(status: 'POSTPONED').find_each do |match|
      PostponedMatch.find_or_create_by!(
        match:,
        external_id: match.external_id
      )
    rescue ActiveRecord::RecordInvalid => e
      puts "Failed to create PostponedMatch for Match ID: #{match.id}, Error: #{e.message}"
    end
  end

  def down
    # This migration is data fixing only, no structural changes to reverse
  end
end
