class AddCheckConstraintToTables < ActiveRecord::Migration[7.0]
  def change
    # Removing NOT NULL constraint from football_data_id columns
    change_column_null :matches, :football_data_id, true
    change_column_null :areas, :football_data_id, true
    change_column_null :competitions, :football_data_id, true
    change_column_null :postponed_matches, :football_data_id, true
    change_column_null :seasons, :football_data_id, true
    change_column_null :teams, :football_data_id, true

    # Adding check constraint to areas table
    add_check_constraint :areas, '(football_data_id IS NULL OR api_football_id IS NULL)',
                         name: 'areas_football_data_id_api_football_id_check'

    # Adding check constraint to competitions table
    add_check_constraint :competitions, '(football_data_id IS NULL OR api_football_id IS NULL)',
                         name: 'competitions_football_data_id_api_football_id_check'

    # Adding check constraint to matches table
    add_check_constraint :matches, '(football_data_id IS NULL OR api_football_id IS NULL)',
                         name: 'matches_football_data_id_api_football_id_check'

    # Adding check constraint to postponed_matches table
    add_check_constraint :postponed_matches, '(football_data_id IS NULL OR api_football_id IS NULL)',
                         name: 'postponed_matches_football_data_id_api_football_id_check'

    # Adding check constraint to seasons table
    add_check_constraint :seasons, '(football_data_id IS NULL OR api_football_id IS NULL)',
                         name: 'seasons_football_data_id_api_football_id_check'

    # Adding check constraint to teams table
    add_check_constraint :teams, '(football_data_id IS NULL OR api_football_id IS NULL)',
                         name: 'teams_football_data_id_api_football_id_check'
  end
end
