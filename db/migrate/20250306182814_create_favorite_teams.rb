class CreateFavoriteTeams < ActiveRecord::Migration[7.0]
  def change
    create_table :favorite_teams do |t|
      t.references :user, null: false, foreign_key: true
      t.references :team, null: false, foreign_key: true
      t.references :competition, null: false, foreign_key: true

      t.timestamps
    end

    # Ensures a user can only have one favorite team per competition
    add_index :favorite_teams, %i[user_id competition_id], unique: true

    # Index to improve query performance when looking up a user's favorite team for a competition
    add_index :favorite_teams, %i[competition_id user_id]

    # Index to help with querying teams and their fans
    add_index :favorite_teams, %i[team_id user_id]
  end
end
