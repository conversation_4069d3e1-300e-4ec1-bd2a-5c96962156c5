class CreateCompetitions < ActiveRecord::Migration[7.0]
  def change
    create_table :competitions do |t|
      t.integer :external_id
      t.string :name
      t.string :code
      t.string :competition_type
      t.string :gender
      t.string :emblem_public_id
      t.references :area, null: false, foreign_key: true
      t.references :current_season, null: true, foreign_key: { to_table: :seasons }
      t.references :current_winner, null: true, foreign_key: { to_table: :teams }

      t.timestamps
      t.index :external_id, unique: true
    end
  end
end
