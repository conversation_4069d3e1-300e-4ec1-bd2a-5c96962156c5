class AddUserToMatchPredictions < ActiveRecord::Migration[7.0]
  def change
    add_reference :match_predictions, :user, null: true, foreign_key: true

    reversible do |dir|
      dir.up do
        # Backfill user_id for existing match predictions
        execute <<-SQL.squish
          UPDATE match_predictions
          SET user_id = (
            SELECT round_predictions.user_id
            FROM round_predictions
            WHERE round_predictions.id = match_predictions.round_prediction_id
          )
        SQL

        # Set user_id to NOT NULL after backfilling
        change_column_null :match_predictions, :user_id, false
      end
    end
  end
end
