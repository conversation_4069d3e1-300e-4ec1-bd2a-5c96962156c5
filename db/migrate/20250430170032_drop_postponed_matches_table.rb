class DropPostponedMatchesTable < ActiveRecord::Migration[7.0]
  def change
    drop_table :postponed_matches do |t|
      t.bigint :match_id, null: false
      t.datetime :last_checked_at
      t.datetime :check_again_after
      t.datetime :created_at, null: false
      t.datetime :updated_at, null: false
      t.string :source, default: 'football_data', null: false
      t.string :external_service_id
      t.bigint :competition_id, null: false
    end
  end
end
