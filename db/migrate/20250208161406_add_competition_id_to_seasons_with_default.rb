class AddCompetitionIdToSeasonsWithDefault < ActiveRecord::Migration[7.0]
  def change
    # Add the column with a default value and allow nulls initially
    add_column :seasons, :competition_id, :bigint, default: 1, null: true

    # Update existing records with a valid competition_id
    reversible do |dir|
      dir.up do
        Season.update_all(competition_id: 1) # Use a valid competition_id if possible
      end
    end

    # Change the column to NOT NULL and remove the default value
    change_column_null :seasons, :competition_id, false
    change_column_default :seasons, :competition_id, nil

    # Add foreign key and index
    add_foreign_key :seasons, :competitions
    add_index :seasons, :competition_id
  end
end
