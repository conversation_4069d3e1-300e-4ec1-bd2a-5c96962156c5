class CreateMatchPredictions < ActiveRecord::Migration[7.0]
  def change
    create_table :match_predictions do |t|
      t.integer :home_score
      t.integer :away_score
      t.integer :points
      t.references :round_prediction, null: false, foreign_key: true
      t.references :match, null: false, foreign_key: true

      t.timestamps
      t.index %i[round_prediction_id match_id], unique: true
    end
  end
end
