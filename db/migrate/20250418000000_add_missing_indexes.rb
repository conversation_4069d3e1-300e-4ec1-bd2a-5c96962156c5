class AddMissingIndexes < ActiveRecord::Migration[7.0]
  def change
    # Add indexes for commonly queried columns
    add_index :matches, :status unless index_exists?(:matches, :status)
    add_index :matches, %i[status matchday] unless index_exists?(:matches, %i[status matchday])
    add_index :matches, %i[season_id matchday status] unless index_exists?(:matches, %i[season_id matchday status])
    add_index :matches, :utc_date unless index_exists?(:matches, :utc_date)

    # Add composite indexes for common queries
    unless index_exists?(:round_predictions, %i[user_id season_id matchday])
      add_index :round_predictions, %i[user_id season_id matchday]
    end

    return if index_exists?(:match_predictions, %i[match_id round_prediction_id])

    add_index :match_predictions, %i[match_id round_prediction_id]
  end
end
