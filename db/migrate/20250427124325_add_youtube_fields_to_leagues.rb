class AddYoutubeFieldsToLeagues < ActiveRecord::Migration[7.0]
  def change
    add_column :leagues, :youtube_league, :boolean, default: false
    add_column :leagues, :youtube_channel_id, :string
    add_column :leagues, :subscriber_only, :boolean, default: false
    add_column :leagues, :min_subscriber_date, :datetime
    add_column :leagues, :subscriber_requirement_type, :string
    add_column :leagues, :unsubscribe_policy, :string, default: 'grace_period'

    add_index :leagues, :youtube_channel_id
    add_index :leagues, :subscriber_only
  end
end
