class CreateTeams < ActiveRecord::Migration[7.0]
  def change
    create_table :teams do |t|
      t.integer :external_id
      t.string :name
      t.string :short_name
      t.string :tla
      t.string :crest_public_id
      t.references :area, null: false, foreign_key: true
      t.integer :founded
      t.string :venue
      t.string :website

      t.timestamps
      t.index :external_id, unique: true
    end
  end
end
