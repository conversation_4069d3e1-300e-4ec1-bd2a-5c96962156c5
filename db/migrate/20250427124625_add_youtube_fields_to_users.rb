class AddYoutubeFieldsToUsers < ActiveRecord::Migration[7.0]
  def change
    add_column :users, :youtube_channel_id, :string
    add_column :users, :youtube_channel_name, :string
    add_column :users, :is_content_creator, :boolean, default: false
    add_column :users, :youtube_verified_at, :datetime
    add_column :users, :youtube_subscriber_count, :integer
    add_column :users, :youtube_avatar_url, :string
    add_column :users, :youtube_credentials, :text

    add_index :users, :youtube_channel_id, unique: true
    add_index :users, :is_content_creator
  end
end
