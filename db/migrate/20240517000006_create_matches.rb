class CreateMatches < ActiveRecord::Migration[7.0]
  def change
    create_table :matches do |t|
      t.integer :external_id
      t.integer :matchday
      t.datetime :utc_date
      t.string :status
      t.string :stage
      t.string :group
      t.string :referee
      t.references :season, null: false, foreign_key: true
      t.references :home_team, null: false, foreign_key: { to_table: :teams }
      t.references :away_team, null: false, foreign_key: { to_table: :teams }
      t.string :winner

      t.timestamps
      t.index :external_id, unique: true
    end
  end
end
