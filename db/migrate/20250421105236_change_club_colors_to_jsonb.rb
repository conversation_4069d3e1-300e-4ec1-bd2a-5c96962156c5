class ChangeClubColorsToJsonb < ActiveRecord::Migration[7.0]
  def up
    # First, add a temporary column to store the new jsonb data
    add_column :teams, :club_colors_jsonb, :jsonb, default: {}

    # Migrate existing data to the new format
    Team.find_each do |team|
      if team.club_colors.present?
        colors = team.club_colors.split(' / ')
        club_colors_hash = {
          home: {
            primary: colors[0],
            secondary: colors[1],
            tertiary: colors[2]
          }.compact,
          away: {}
        }
        team.update_column(:club_colors_jsonb, club_colors_hash)
      else
        team.update_column(:club_colors_jsonb, { home: {}, away: {} })
      end
    end

    # Remove the old column and rename the new one
    remove_column :teams, :club_colors
    rename_column :teams, :club_colors_jsonb, :club_colors
  end

  def down
    # First, add a temporary column to store the string data
    add_column :teams, :club_colors_string, :string

    # Migrate data back to the string format
    Team.find_each do |team|
      if team.club_colors.present? && team.club_colors['home'].present?
        home_colors = team.club_colors['home']
        colors = [
          home_colors['primary'],
          home_colors['secondary'],
          home_colors['tertiary']
        ].compact
        team.update_column(:club_colors_string, colors.join(' / '))
      end
    end

    # Remove the jsonb column and rename the string column
    remove_column :teams, :club_colors
    rename_column :teams, :club_colors_string, :club_colors
  end
end
