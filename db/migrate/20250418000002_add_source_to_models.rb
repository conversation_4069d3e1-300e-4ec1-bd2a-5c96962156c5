class AddSourceToModels < ActiveRecord::Migration[7.0]
  def change
    add_column :areas, :source, :string, null: false, default: 'football_data'
    add_column :competitions, :source, :string, null: false, default: 'football_data'
    add_column :matches, :source, :string, null: false, default: 'football_data'
    add_column :postponed_matches, :source, :string, null: false, default: 'football_data'
    add_column :seasons, :source, :string, null: false, default: 'football_data'
    add_column :teams, :source, :string, null: false, default: 'football_data'

    add_index :areas, :source
    add_index :competitions, :source
    add_index :matches, :source
    add_index :postponed_matches, :source
    add_index :seasons, :source
    add_index :teams, :source

    reversible do |dir|
      dir.up do
        # Update source for competitions
        execute <<-SQL.squish
          UPDATE competitions
          SET source = CASE
            WHEN football_data_id IS NOT NULL THEN 'football_data'
            WHEN api_football_id IS NOT NULL THEN 'api_football'
            ELSE 'ksi_soap'
          END
        SQL

        # Update source for matches
        execute <<-SQL.squish
          UPDATE matches
          SET source = CASE
            WHEN football_data_id IS NOT NULL THEN 'football_data'
            WHEN api_football_id IS NOT NULL THEN 'api_football'
            ELSE 'ksi_soap'
          END
        SQL

        # Update source for areas
        execute <<-SQL.squish
          UPDATE areas
          SET source = CASE
            WHEN football_data_id IS NOT NULL THEN 'football_data'
            WHEN api_football_id IS NOT NULL THEN 'api_football'
            ELSE 'ksi_soap'
          END
        SQL

        # Update source for postponed_matches
        execute <<-SQL.squish
          UPDATE postponed_matches
          SET source = CASE
            WHEN football_data_id IS NOT NULL THEN 'football_data'
            WHEN api_football_id IS NOT NULL THEN 'api_football'
            ELSE 'ksi_soap'
          END
        SQL

        # Update source for seasons
        execute <<-SQL.squish
          UPDATE seasons
          SET source = CASE
            WHEN football_data_id IS NOT NULL THEN 'football_data'
            WHEN api_football_id IS NOT NULL THEN 'api_football'
            ELSE 'ksi_soap'
          END
        SQL

        # Update source for teams
        execute <<-SQL.squish
          UPDATE teams
          SET source = CASE
            WHEN football_data_id IS NOT NULL THEN 'football_data'
            WHEN api_football_id IS NOT NULL THEN 'api_football'
            ELSE 'ksi_soap'
          END
        SQL
      end
    end
  end
end
