class UpdateMatchesExternalId < ActiveRecord::Migration[7.0]
  def up
    Match.find_each do |match|
      # Skip if external_id is already set
      next if match.external_id.present?

      # Set the existing id as external_id
      match.update_column(:external_id, match.id)
    end
  end

  def down
    # Optional: If you want to be able to rollback
    Match.update_all(external_id: nil)
  end
end
