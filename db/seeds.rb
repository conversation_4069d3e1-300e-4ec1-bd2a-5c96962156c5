# Only load and run seeds in development environment
if Rails.env.development?
  begin
    require 'pry'
  rescue LoadError
    # Pry not available
  end

  begin
    require 'round_robin_tournament'
  rescue LoadError
    # round_robin_tournament not available
  end

  begin
    require 'factory_bot_rails'
  rescue LoadError
    # FactoryBot not available
  end

  require 'faker'

  # Create admin user for development
  FactoryBot.create(:user, username: 'MxKlptz', email: '<EMAIL>', password: 'asdasd',
                           admin: true)

  # Create YouTube subscriber test user
  FactoryBot.create(:user,
                    username: 'youtube test',
                    email: '<EMAIL>',
                    password: 'password123',
                    youtube_credentials: {
                      access_token: 'fake_subscriber_token',
                      refresh_token: 'fake_subscriber_refresh_token',
                      expires_at: 1.hour.from_now.to_i,
                      channel_id: 'UC_test_subscriber_channel'
                    }.to_json,
                    youtube_channel_id: 'UC_test_subscriber_channel',
                    youtube_channel_name: 'Test Subscriber Channel',
                    youtube_verified_at: Time.current,
                    youtube_subscriber_count: 50,
                    youtube_avatar_url: 'https://example.com/avatar.jpg',
                    is_content_creator: false)

  def populate_database
    create_competition_with_teams
    comp = Competition.first

    create_rounds_and_matches(comp.current_season_id)
    add_scores_to_matches(comp)

    user1 = FactoryBot.create(:user)
    user2 = FactoryBot.create(:user, username: 'User 2', email: '<EMAIL>')
    FactoryBot.create(:user, username: 'Tester', email: '<EMAIL>', password: 'password')
    league = FactoryBot.create(:league, owner: user1, competition_id: comp.id, season_id: comp.current_season_id)
    league.users << user2

    user1_with_predictions(user1, comp)
    user2_with_predictions(user2, comp)

    # Return the objects created for easy access in the spec
    { competition: comp, users: [user1, user2], league: }
  end

  def create_competition_with_teams
    area = FactoryBot.create(:area)
    comp = FactoryBot.build(:competition, area:, name: 'Besta deildin')
    season = FactoryBot.create(:season, competition: comp)
    comp.current_season_id = season.id
    create_teams(season, area)
    comp.current_winner = Team.first.id
    comp.save
    add_club_logo('bestaDeildin.png', comp)
    comp.save
  end

  def add_club_logo(filename, resource)
    file_path = Rails.root.join('app', 'assets', 'logos', 'clubLogos', filename)
    file = File.open(file_path)
    resource.emblem.attach(io: file, filename:, content_type: 'image/png')
  end

  def create_teams(season, area)
    teams = %w[
      valur
      kr
      fram
      breidablik
      reynir
      vikingur
    ]

    teams.each do |trait|
      team = FactoryBot.create(:team, trait.to_sym, area_id: area.id)
      season.teams << team
    end
  end

  def create_rounds_and_matches(season_id)
    rounds = [[%w[valur kr], %w[fram breidablik], %w[reynir vikingur]],
              [%w[fram kr], %w[reynir valur], %w[vikingur breidablik]],
              [%w[reynir kr], %w[vikingur fram], %w[breidablik valur]],
              [%w[vikingur kr], %w[breidablik reynir], %w[valur fram]],
              [%w[breidablik kr], %w[valur vikingur], %w[fram reynir]]]

    reversed_rounds = rounds.map { |sub_array| sub_array.map(&:reverse) }

    combined_rounds = rounds + reversed_rounds

    create_matches(combined_rounds, season_id)
  end

  def create_matches(rounds, season_id)
    rounds.each_with_index do |round, index|
      round.each do |teams|
        home_team = Team.find_by(name: teams[0].capitalize)
        away_team = Team.find_by(name: teams[1].capitalize)
        match = FactoryBot.create(:match, home_team_id: home_team.id, away_team_id: away_team.id, matchday: index + 1,
                                          season_id:)
        match.score = Score.create
      end
    end
  end

  def add_scores_to_matches(comp)
    r1 = [{ home: 2, away: 1 }, { home: 0, away: 3 }, { home: 2, away: 2 }]

    r2 = [{ home: 1, away: 0 }, { home: 0, away: 4 }, { home: 3, away: 4 }]

    set_matches_to_finished(1, comp, r1)
    set_matches_to_finished(2, comp, r2)
    season = comp.current_season

    season.currentMatchday = 3
    season.save
  end

  def set_matches_to_finished(matchday, comp, results)
    matches = Match.where(matchday:, season_id: comp.current_season_id)
    matches.each_with_index do |match, index|
      update_match(match, results[index][:home], results[index][:away])
    end
  end

  def update_match(match, home_score, away_score)
    match.status = 'FINISHED'
    match.save
    match.score.fullTimeHome = home_score
    match.score.fullTimeAway = away_score
    match.score.save
  end

  def user1_with_predictions(user, comp)
    match_predictions_r1 = [
      { home: 2, away: 1 },
      { home: 3, away: 0 },
      { home: 2, away: 2 }
    ]

    match_predictions_r2 = [
      { home: 1, away: 3 },
      { home: 0, away: 4 },
      { home: 2, away: 2 }
    ]

    setup_user_with_predictions(1, user, comp, match_predictions_r1)
    setup_user_with_predictions(2, user, comp, match_predictions_r2)
  end

  def user2_with_predictions(user, comp)
    match_predictions_r1 = [
      { home: 2, away: 1 },
      { home: 1, away: 2 },
      { home: 3, away: 4 }
    ]

    match_predictions_r2 = [
      { home: 1, away: 0 },
      { home: 0, away: 1 },
      { home: 3, away: 4 }
    ]

    setup_user_with_predictions(1, user, comp, match_predictions_r1)
    setup_user_with_predictions(2, user, comp, match_predictions_r2)
  end

  def setup_user_with_predictions(match_day, user, comp, match_predictions)
    round_prediction = FactoryBot.create(:round_prediction,
                                         user:, competition_id: comp.id, season_id: comp.current_season_id,
                                         matchday: match_day)

    create_round_predictions_with_match_predictions(match_predictions, round_prediction)
  end

  def create_round_predictions_with_match_predictions(match_predictions, round_prediction)
    matches_round = Match.where(matchday: round_prediction.matchday)

    matches_round.each_with_index do |match, index|
      FactoryBot.create(:match_prediction, home: match_predictions[index][:home],
                                           away: match_predictions[index][:away], round_prediction:, match:)
    end
  end

  # Only run in dev environment
  populate_database
end

# This file can be empty for production environment
puts "Skipping seeds in #{Rails.env} environment" unless Rails.env.development?
