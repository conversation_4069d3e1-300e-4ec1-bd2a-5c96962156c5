# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_04_30_170032) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "areas", force: :cascade do |t|
    t.string "name"
    t.string "code"
    t.string "flag_public_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "source", default: "football_data", null: false
    t.string "external_service_id"
    t.index ["external_service_id"], name: "index_areas_on_external_service_id"
    t.index ["source"], name: "index_areas_on_source"
  end

  create_table "competitions", force: :cascade do |t|
    t.string "name"
    t.string "code"
    t.string "competition_type"
    t.string "gender"
    t.string "emblem_public_id"
    t.bigint "area_id", null: false
    t.bigint "current_season_id"
    t.bigint "current_winner_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "source", default: "football_data", null: false
    t.string "external_service_id"
    t.jsonb "colors", default: {}
    t.index ["area_id"], name: "index_competitions_on_area_id"
    t.index ["current_season_id"], name: "index_competitions_on_current_season_id"
    t.index ["current_winner_id"], name: "index_competitions_on_current_winner_id"
    t.index ["external_service_id"], name: "index_competitions_on_external_service_id"
    t.index ["source"], name: "index_competitions_on_source"
  end

  create_table "competitions_teams", force: :cascade do |t|
    t.bigint "competition_id", null: false
    t.bigint "team_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["competition_id", "team_id"], name: "index_competitions_teams_on_competition_id_and_team_id", unique: true
    t.index ["competition_id"], name: "index_competitions_teams_on_competition_id"
    t.index ["team_id"], name: "index_competitions_teams_on_team_id"
  end

  create_table "devise_api_tokens", force: :cascade do |t|
    t.string "resource_owner_type", null: false
    t.bigint "resource_owner_id", null: false
    t.string "access_token", null: false
    t.string "refresh_token"
    t.integer "expires_in", null: false
    t.datetime "revoked_at"
    t.string "previous_refresh_token"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["access_token"], name: "index_devise_api_tokens_on_access_token"
    t.index ["previous_refresh_token"], name: "index_devise_api_tokens_on_previous_refresh_token"
    t.index ["refresh_token"], name: "index_devise_api_tokens_on_refresh_token"
    t.index ["resource_owner_type", "resource_owner_id"], name: "index_devise_api_tokens_on_resource_owner"
  end

  create_table "favorite_teams", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "team_id", null: false
    t.bigint "competition_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["competition_id", "user_id"], name: "index_favorite_teams_on_competition_id_and_user_id"
    t.index ["competition_id"], name: "index_favorite_teams_on_competition_id"
    t.index ["team_id", "user_id"], name: "index_favorite_teams_on_team_id_and_user_id"
    t.index ["team_id"], name: "index_favorite_teams_on_team_id"
    t.index ["user_id", "competition_id"], name: "index_favorite_teams_on_user_id_and_competition_id", unique: true
    t.index ["user_id"], name: "index_favorite_teams_on_user_id"
  end

  create_table "league_join_requests", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "league_id", null: false
    t.integer "status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["league_id"], name: "index_league_join_requests_on_league_id"
    t.index ["user_id", "league_id"], name: "index_league_join_requests_on_user_id_and_league_id", unique: true
    t.index ["user_id"], name: "index_league_join_requests_on_user_id"
  end

  create_table "league_seasons", force: :cascade do |t|
    t.bigint "league_id", null: false
    t.bigint "season_id", null: false
    t.bigint "user_id", null: false
    t.integer "points", default: 0
    t.integer "correct_predictions", default: 0
    t.integer "incorrect_predictions", default: 0
    t.integer "perfect_predictions", default: 0
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["league_id", "season_id", "user_id"], name: "index_league_seasons_on_league_id_and_season_id_and_user_id", unique: true
    t.index ["league_id"], name: "index_league_seasons_on_league_id"
    t.index ["season_id"], name: "index_league_seasons_on_season_id"
    t.index ["user_id"], name: "index_league_seasons_on_user_id"
  end

  create_table "leagues", force: :cascade do |t|
    t.string "name", null: false
    t.boolean "open", default: true
    t.bigint "owner_id", null: false
    t.bigint "competition_id", null: false
    t.bigint "season_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "starting_matchday"
    t.boolean "archived", default: false, null: false
    t.datetime "archived_at"
    t.boolean "youtube_league", default: false
    t.string "youtube_channel_id"
    t.boolean "subscriber_only", default: false
    t.datetime "min_subscriber_date"
    t.string "subscriber_requirement_type"
    t.string "unsubscribe_policy", default: "grace_period"
    t.index ["archived"], name: "index_leagues_on_archived"
    t.index ["competition_id"], name: "index_leagues_on_competition_id"
    t.index ["owner_id"], name: "index_leagues_on_owner_id"
    t.index ["season_id"], name: "index_leagues_on_season_id"
    t.index ["subscriber_only"], name: "index_leagues_on_subscriber_only"
    t.index ["youtube_channel_id"], name: "index_leagues_on_youtube_channel_id"
  end

  create_table "match_predictions", force: :cascade do |t|
    t.integer "home_score"
    t.integer "away_score"
    t.integer "points"
    t.bigint "round_prediction_id", null: false
    t.bigint "match_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id", null: false
    t.index ["match_id", "round_prediction_id"], name: "index_match_predictions_on_match_id_and_round_prediction_id"
    t.index ["match_id"], name: "index_match_predictions_on_match_id"
    t.index ["round_prediction_id", "match_id"], name: "index_match_predictions_on_round_prediction_id_and_match_id", unique: true
    t.index ["round_prediction_id"], name: "index_match_predictions_on_round_prediction_id"
    t.index ["user_id"], name: "index_match_predictions_on_user_id"
  end

  create_table "matches", force: :cascade do |t|
    t.integer "matchday"
    t.datetime "utc_date"
    t.string "status"
    t.string "stage"
    t.string "group"
    t.string "referee"
    t.bigint "season_id", null: false
    t.bigint "home_team_id"
    t.bigint "away_team_id"
    t.string "winner"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "original_status"
    t.string "venue"
    t.string "source", default: "football_data", null: false
    t.string "external_service_id"
    t.index ["away_team_id"], name: "index_matches_on_away_team_id"
    t.index ["external_service_id"], name: "index_matches_on_external_service_id"
    t.index ["home_team_id"], name: "index_matches_on_home_team_id"
    t.index ["season_id", "matchday", "status"], name: "index_matches_on_season_id_and_matchday_and_status"
    t.index ["season_id"], name: "index_matches_on_season_id"
    t.index ["source"], name: "index_matches_on_source"
    t.index ["status", "matchday"], name: "index_matches_on_status_and_matchday"
    t.index ["status"], name: "index_matches_on_status"
    t.index ["utc_date"], name: "index_matches_on_utc_date"
  end

  create_table "memberships", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "league_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "subscription_status"
    t.datetime "subscription_verified_at"
    t.datetime "grace_period_ends_at"
    t.index ["grace_period_ends_at"], name: "index_memberships_on_grace_period_ends_at"
    t.index ["league_id"], name: "index_memberships_on_league_id"
    t.index ["subscription_status"], name: "index_memberships_on_subscription_status"
    t.index ["user_id", "league_id"], name: "index_memberships_on_user_id_and_league_id", unique: true
    t.index ["user_id"], name: "index_memberships_on_user_id"
  end

  create_table "round_predictions", force: :cascade do |t|
    t.integer "matchday", null: false
    t.bigint "user_id", null: false
    t.bigint "season_id", null: false
    t.bigint "competition_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "stage", null: false
    t.index ["competition_id"], name: "index_round_predictions_on_competition_id"
    t.index ["season_id"], name: "index_round_predictions_on_season_id"
    t.index ["user_id", "season_id", "matchday"], name: "index_round_predictions_on_user_id_and_season_id_and_matchday", unique: true
    t.index ["user_id"], name: "index_round_predictions_on_user_id"
  end

  create_table "scores", force: :cascade do |t|
    t.string "duration"
    t.integer "fullTimeAway"
    t.integer "halfTimeAway"
    t.integer "fullTimeHome"
    t.integer "halfTimeHome"
    t.string "winner"
    t.bigint "match_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["match_id"], name: "index_scores_on_match_id"
  end

  create_table "seasons", force: :cascade do |t|
    t.date "start_date"
    t.date "end_date"
    t.integer "current_matchday"
    t.integer "winner_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "current_stage", default: "REGULAR_SEASON"
    t.bigint "competition_id", null: false
    t.integer "overall_matchday"
    t.string "source", default: "football_data", null: false
    t.string "external_service_id"
    t.boolean "completed", default: false, null: false
    t.index ["competition_id"], name: "index_seasons_on_competition_id"
    t.index ["external_service_id"], name: "index_seasons_on_external_service_id"
    t.index ["source"], name: "index_seasons_on_source"
  end

  create_table "seasons_teams", id: false, force: :cascade do |t|
    t.bigint "season_id", null: false
    t.bigint "team_id", null: false
    t.index ["season_id", "team_id"], name: "index_seasons_teams_on_season_id_and_team_id", unique: true
  end

  create_table "teams", force: :cascade do |t|
    t.string "name"
    t.string "short_name"
    t.string "tla"
    t.string "crest_public_id"
    t.bigint "area_id", null: false
    t.integer "founded"
    t.string "venue"
    t.string "website"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "source", default: "football_data", null: false
    t.string "external_service_id"
    t.jsonb "club_colors", default: {}
    t.index ["area_id"], name: "index_teams_on_area_id"
    t.index ["external_service_id"], name: "index_teams_on_external_service_id"
    t.index ["source"], name: "index_teams_on_source"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", null: false
    t.string "username", null: false
    t.string "encrypted_password", null: false
    t.boolean "admin", default: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "confirmation_token"
    t.datetime "confirmed_at"
    t.datetime "confirmation_sent_at"
    t.string "unconfirmed_email"
    t.string "youtube_channel_id"
    t.string "youtube_channel_name"
    t.boolean "is_content_creator", default: false
    t.datetime "youtube_verified_at"
    t.integer "youtube_subscriber_count"
    t.string "youtube_avatar_url"
    t.text "youtube_credentials"
    t.datetime "last_active_at"
    t.index ["confirmation_token"], name: "index_users_on_confirmation_token", unique: true
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["is_content_creator"], name: "index_users_on_is_content_creator"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["username"], name: "index_users_on_username", unique: true
    t.index ["youtube_channel_id"], name: "index_users_on_youtube_channel_id", unique: true
  end

  add_foreign_key "competitions", "areas"
  add_foreign_key "competitions", "seasons", column: "current_season_id", on_delete: :nullify
  add_foreign_key "competitions", "teams", column: "current_winner_id"
  add_foreign_key "competitions_teams", "competitions"
  add_foreign_key "competitions_teams", "teams"
  add_foreign_key "favorite_teams", "competitions"
  add_foreign_key "favorite_teams", "teams"
  add_foreign_key "favorite_teams", "users"
  add_foreign_key "league_join_requests", "leagues"
  add_foreign_key "league_join_requests", "users"
  add_foreign_key "league_seasons", "leagues"
  add_foreign_key "league_seasons", "seasons"
  add_foreign_key "league_seasons", "users"
  add_foreign_key "leagues", "competitions"
  add_foreign_key "leagues", "seasons"
  add_foreign_key "leagues", "users", column: "owner_id"
  add_foreign_key "match_predictions", "matches"
  add_foreign_key "match_predictions", "round_predictions"
  add_foreign_key "match_predictions", "users"
  add_foreign_key "matches", "seasons"
  add_foreign_key "matches", "teams", column: "away_team_id"
  add_foreign_key "matches", "teams", column: "home_team_id"
  add_foreign_key "memberships", "leagues"
  add_foreign_key "memberships", "users"
  add_foreign_key "round_predictions", "competitions"
  add_foreign_key "round_predictions", "seasons"
  add_foreign_key "round_predictions", "users"
  add_foreign_key "seasons", "competitions"
  add_foreign_key "teams", "areas"
end
