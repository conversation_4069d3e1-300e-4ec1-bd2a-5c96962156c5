# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore the default SQLite database.
/db/*.sqlite3
/db/*.sqlite3-*

/vendor/*

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
tmp
!/log/.keep
!/tmp/.keep
tmp/.keep
tmp/pids/.keep
tmp/storage/.keep

# Ignore pidfiles, but keep the directory.
/tmp/pids/*
!/tmp/pids/
!/tmp/pids/.keep

# Ignore uploaded files in development.
/storage/*
!/storage/.keep
/tmp/storage/*
!/tmp/storage/
!/tmp/storage/.keep
/public/uploads

# Ignore master key for decrypting credentials and more.
/config/master.key

# Ignore env files
.env

# Ignore this crap
related_stuff.txt
combined.txt
/new_venv
.aider*
backup_db.sql

# Ignore bundled gems
/vendor/bundle

# Ignore all temporary files
/tmp/*
# But keep the directory structure
!/tmp/.keep
!/tmp/*/
/tmp/*/.*
!/tmp/*/.keep
# Specific exceptions for subdirectories
!/tmp/pids/
!/tmp/storage/
# Logs
*.log


