network:
  version: 2
  ethernets:
    eth0:
      addresses:
        - ***************/20
        - *************/32
      gateway4: *************
      nameservers:
        addresses:
          - ***********
          - ***********
      routes:
        - to: 0.0.0.0/0
          via: *************
      set-name: eth0
    eth1:
      addresses:
        - **********/20
      nameservers:
        addresses:
          - ***********
          - ***********
      set-name: eth1