# spec/factories/leagues.rb
FactoryBot.define do
  factory :league do
    sequence(:name) { |n| "League #{n}" }
    open { true }
    association :owner, factory: :user

    # Use transient attributes to handle competition and season
    transient do
      competition { create(:competition) }
      season { create(:season, competition:) }
    end

    # Set competition_id and season_id from transient attributes
    competition_id { competition.id }
    season_id { season.id }

    starting_matchday { 1 }
    archived { false }
    archived_at { nil }

    trait :archived do
      archived { true }
      archived_at { Time.current }
    end

    trait :active do
      archived { false }
      archived_at { nil }
    end

    factory :archived_league do
      archived { true }
      archived_at { Time.current }
    end
  end
end
