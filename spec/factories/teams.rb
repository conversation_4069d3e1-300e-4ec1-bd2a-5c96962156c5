# spec/factories/teams.rb
FactoryBot.define do
  factory :team do
    sequence(:name) { |n| "Team #{n}" }
    sequence(:short_name) { |n| "T#{n}" }
    sequence(:external_service_id) { |n| n } # Add default external_service_id
    source { 'football_data' }
    association :area
    crest_public_id { 'some_crest_id' }
    venue { 'Some Venue' }
    tla { 'TLA' }
    club_colors { { home: { primary: '#FF0000', secondary: '#FFFFFF' }, away: {} } }

    trait :valur do
      name { 'Valur' }
      short_name { 'VAL' }
      tla { 'Val' }
      venue { 'Valsvöllur' }
      crest_public_id { 'teams/valur' }
      club_colors do
        { home: { primary: '#FF0000', secondary: '#FFFFFF' }, away: { primary: '#FFFFFF', secondary: '#FF0000' } }
      end
    end

    trait :kr do
      name { 'Kr' }
      short_name { 'KR' }
      tla { 'KR' }
      venue { 'KR-völlur' }
      crest_public_id { 'teams/kr' }
      club_colors do
        { home: { primary: '#000000', secondary: '#FFFFFF' }, away: { primary: '#FFFFFF', secondary: '#000000' } }
      end
    end

    trait :breidablik do
      name { 'Breidablik' }
      short_name { 'Breidablik' }
      tla { 'Bre' }
      venue { 'Kópavogsvöllur' }
      crest_public_id { 'teams/breidablik' }
    end

    trait :reynir do
      name { 'Reynir' }
      short_name { 'Reynir' }
      tla { 'Rey' }
      venue { 'Reynisvöllur' }
      crest_public_id { 'teams/reynir' }
    end

    trait :vikingur do
      name { 'Vikingur' }
      short_name { 'Vikingur' }
      tla { 'Vik' }
      venue { 'Vikingsvöllur' }
      crest_public_id { 'teams/vikingur' }
    end

    trait :fram do
      name { 'Fram' }
      short_name { 'Fram' }
      tla { 'Fra' }
      venue { 'Framvöllur' }
      crest_public_id { 'teams/fram' }
    end
  end
end
