# spec/factories/seasons.rb
FactoryBot.define do
  factory :season do
    sequence(:external_service_id) { |n| n } # Add default external_service_id
    source { 'football_data' }
    start_date { Date.today }
    end_date { Date.today + 1.year }
    current_matchday { 1 }
    association :competition

    trait :ksi_soap do
      source { 'ksi_soap' }
    end

    trait :api_football do
      source { 'api_football' }
    end
  end
end
