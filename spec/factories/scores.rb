# spec/factories/scores.rb
FactoryBot.define do
  factory :score do
    association :match

    # By default, create a score for a home team win
    fullTimeHome { 2 }
    fullTimeAway { 1 }
    halfTimeHome { 1 }
    halfTimeAway { 0 }
    duration { 'REGULAR' }
    winner { 'HOME_TEAM' } # This will be auto-calculated based on scores

    trait :draw do
      fullTimeHome { 1 }
      fullTimeAway { 1 }
      halfTimeHome { 0 }
      halfTimeAway { 0 }
      winner { 'DRAW' }
    end

    trait :away_win do
      fullTimeHome { 1 }
      fullTimeAway { 2 }
      halfTimeHome { 1 }
      halfTimeAway { 1 }
      winner { 'AWAY_TEAM' }
    end
  end
end
