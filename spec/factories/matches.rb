# spec/factories/matches.rb
FactoryBot.define do
  factory :match do
    sequence(:external_service_id) { |n| n } # Add default external_service_id
    source { 'football_data' }
    matchday { 1 }
    status { 'SCHEDULED' }
    referee { '<PERSON>' }
    utc_date { 1.day.from_now }
    stage { 'REGULAR_SEASON' }
    venue { 'Stadium' }
    association :season
    association :home_team, factory: :team
    association :away_team, factory: :team

    trait :ksi_soap do
      source { 'ksi_soap' }
    end

    trait :api_football do
      source { 'api_football' }
    end
  end
end
