# spec/factories/areas.rb
FactoryBot.define do
  factory :area do
    sequence(:name) { |n| "Area #{n}" }
    sequence(:code) { |n| "A#{n}" }
    sequence(:external_service_id) { |n| n } # Add default external_service_id
    source { 'football_data' }
    flag_public_id { 'areas/flag' }

    trait :ksi_soap do
      source { 'ksi_soap' }
    end

    trait :api_football do
      source { 'api_football' }
    end
  end
end
