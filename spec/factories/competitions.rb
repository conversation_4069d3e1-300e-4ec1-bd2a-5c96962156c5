# spec/factories/competitions.rb
FactoryBot.define do
  factory :competition do
    sequence(:name) { |n| "Competition #{n}" }
    sequence(:code) { |n| "C#{n}" }
    sequence(:external_service_id) { |n| n } # Add default external_service_id
    source { 'football_data' }
    association :area
    competition_type { 'LEAGUE' }
    gender { 'male' }
    emblem_public_id { 'competitions/emblem' }

    trait :ksi_soap do
      source { 'ksi_soap' }
    end

    trait :api_football do
      source { 'api_football' }
    end
  end
end
