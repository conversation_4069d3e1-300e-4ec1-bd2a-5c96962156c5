FactoryBot.define do
  factory :membership do
    association :user
    association :league
    
    trait :with_subscription do
      subscription_status { 'active' }
      subscription_verified_at { Time.current }
    end
    
    trait :with_grace_period do
      subscription_status { 'grace_period' }
      subscription_verified_at { 1.month.ago }
      grace_period_ends_at { 2.weeks.from_now }
    end
  end
end
