require 'rails_helper'

RSpec.describe Api::V1::MatchSerializer do
  let(:area) { create(:area) }
  let(:competition) { create(:competition, area: area) }
  let(:season) { create(:season, competition: competition) }
  let(:home_team) { create(:team, :valur, area: area) }
  let(:away_team) { create(:team, :kr, area: area) }
  let(:match) { create(:match, season: season, home_team: home_team, away_team: away_team) }
  let(:serializer) { described_class.new(match) }
  let(:serialization) { ActiveModelSerializers::Adapter.create(serializer).as_json }

  before do
    season.teams << [home_team, away_team]
  end

  describe '#team_response' do
    it 'returns home colors for home team' do
      home_team.club_colors = {
        home: { primary: '#FF0000', secondary: '#0000FF' },
        away: { primary: '#FFFFFF', secondary: '#000000' }
      }
      home_team.save

      home_team_response = serialization[:home_team]
      expect(home_team_response[:club_colors][:primary]).to eq('#FF0000')
      expect(home_team_response[:club_colors][:secondary]).to eq('#0000FF')
    end

    it 'returns away colors for away team by default' do
      away_team.club_colors = {
        home: { primary: '#FF0000', secondary: '#0000FF' },
        away: { primary: '#FFFFFF', secondary: '#000000' }
      }
      away_team.save

      away_team_response = serialization[:away_team]
      expect(away_team_response[:club_colors][:primary]).to eq('#FFFFFF')
      expect(away_team_response[:club_colors][:secondary]).to eq('#000000')
    end
  end

  describe '#determine_away_team_colors_type' do
    it 'returns home colors for away team when primary colors are similar' do
      # Set home team primary color to red
      home_team.club_colors = {
        home: { primary: '#FF0000', secondary: '#0000FF' }
      }
      home_team.save

      # Set away team away primary color to similar red
      away_team.club_colors = {
        home: { primary: '#00FF00', secondary: '#0000FF' },
        away: { primary: '#FF0000', secondary: '#000000' }
      }
      away_team.save

      # The serializer should detect similar colors and use home colors for away team
      away_team_response = serialization[:away_team]
      expect(away_team_response[:club_colors][:primary]).to eq('#00FF00')
      expect(away_team_response[:club_colors][:secondary]).to eq('#0000FF')
    end

    it 'returns away colors for away team when primary colors are different' do
      # Set home team primary color to red
      home_team.club_colors = {
        home: { primary: '#FF0000', secondary: '#0000FF' }
      }
      home_team.save

      # Set away team away primary color to blue (different from home team)
      away_team.club_colors = {
        home: { primary: '#00FF00', secondary: '#0000FF' },
        away: { primary: '#0000FF', secondary: '#000000' }
      }
      away_team.save

      # The serializer should use away colors for away team
      away_team_response = serialization[:away_team]
      expect(away_team_response[:club_colors][:primary]).to eq('#0000FF')
      expect(away_team_response[:club_colors][:secondary]).to eq('#000000')
    end
  end
end
