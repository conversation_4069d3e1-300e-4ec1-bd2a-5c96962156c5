require 'rails_helper'

RSpec.describe 'League Standings', type: :request do
  let(:user) { create(:user) }
  let(:devise_api_token) { create(:devise_api_token, resource_owner: user) }
  let(:headers) { authentication_headers_for(user, devise_api_token) }

  let(:area) { create(:area) }
  let(:competition) { create(:competition, area:) }
  let(:season) { create(:season, competition:) }
  let(:league) { create(:league, owner: user, competition:, season:) }

  before do
    # Create some additional users and add them to the league
    @user2 = create(:user, username: "user2_#{Time.now.to_i}")
    @user3 = create(:user, username: "user3_#{Time.now.to_i}")

    league.memberships.create(user: @user2)
    league.memberships.create(user: @user3)

    # Create league_seasons records with different points
    create(:league_season, league:, season:, user:, points: 15)
    create(:league_season, league:, season:, user: @user2, points: 25)
    create(:league_season, league:, season:, user: @user3, points: 20)

    # Calculate positions
    LeagueSeason.calculate_positions_for_league_season(league.id, season.id)
  end

  describe 'GET /api/v1/leagues/:id/standings' do
    it 'returns the current standings for a league' do
      get("/api/v1/leagues/#{league.id}/standings", headers:)

      expect(response).to have_http_status(:success)

      json = JSON.parse(response.body)
      expect(json['league']['id']).to eq(league.id)
      expect(json['league']['name']).to eq(league.name)
      expect(json['league']['standings'].size).to eq(3)

      # Check that standings are sorted by points in descending order
      standings = json['league']['standings']
      expect(standings[0]['username']).to eq(@user2.username)
      expect(standings[0]['points']).to eq(25)
      expect(standings[0]['position']).to eq(1)

      expect(standings[1]['username']).to eq(@user3.username)
      expect(standings[1]['points']).to eq(20)
      expect(standings[1]['position']).to eq(2)

      expect(standings[2]['username']).to eq(user.username)
      expect(standings[2]['points']).to eq(15)
      expect(standings[2]['position']).to eq(3)

      # Check for top messages
      expect(standings[0]['top_message']).to include('leading')
      expect(standings[0]['top_message']).to include('🏆')

      expect(standings[1]['top_message']).to include('second place')
      expect(standings[1]['top_message']).to include('🥈')

      expect(standings[2]['top_message']).to include('third place')
      expect(standings[2]['top_message']).to include('🥉')
    end

    context 'with a historical season' do
      let(:old_season) { create(:season, competition:, start_date: 1.year.ago, end_date: 6.months.ago) }

      before do
        # Create historical league_seasons
        create(:league_season, league:, season: old_season, user:, points: 30)
        create(:league_season, league:, season: old_season, user: @user2, points: 20)
        create(:league_season, league:, season: old_season, user: @user3, points: 10)

        # Calculate positions
        LeagueSeason.calculate_positions_for_league_season(league.id, old_season.id)
      end

      it 'returns historical standings when season_id is provided' do
        get("/api/v1/leagues/#{league.id}/standings", params: { season_id: old_season.id }, headers:)

        expect(response).to have_http_status(:success)

        json = JSON.parse(response.body)
        expect(json['league']['id']).to eq(league.id)
        expect(json['league']['name']).to eq(league.name)
        expect(json['league']['season']['id']).to eq(old_season.id)
        expect(json['league']['standings'].size).to eq(3)

        # Check that historical standings are correct
        standings = json['league']['standings']
        expect(standings[0]['username']).to eq(user.username)
        expect(standings[0]['points']).to eq(30)
        expect(standings[0]['position']).to eq(1)

        expect(standings[1]['username']).to eq(@user2.username)
        expect(standings[1]['points']).to eq(20)
        expect(standings[1]['position']).to eq(2)

        expect(standings[2]['username']).to eq(@user3.username)
        expect(standings[2]['points']).to eq(10)
        expect(standings[2]['position']).to eq(3)
      end
    end
  end
end
