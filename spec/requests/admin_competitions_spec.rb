require 'rails_helper'

RSpec.describe 'Admin Competitions API', type: :request do
  let(:user) { create(:user, username: 'test', email: '<EMAIL>', password: 'password') }
  let(:admin_user) { create(:user, username: 'admin', email: '<EMAIL>', password: 'password', admin: true) }
  let(:devise_api_token) { create(:devise_api_token, resource_owner: user) }
  let(:admin_token) { create(:devise_api_token, resource_owner: admin_user) }

  before do
    populate_database
    @competition = Competition.first
    @season = @competition.current_season
    @team = @season.teams.first
  end

  describe 'GET /api/v1/admin/competitions' do
    context 'when authenticated as admin' do
      # Skipping due to actual network calls are made
      xit 'returns a list of all competitions with admin data' do
        get api_v1_admin_competitions_path,
            headers: authentication_headers_for(admin_user, admin_token)

        expect(response).to have_http_status(:success)
        expect(parsed_body.data).to be_an(Array)
        expect(parsed_body.data[0].id).to eq @competition.id
        expect(parsed_body.message).to eq ['Competitions found']
      end
    end

    context 'when authenticated as regular user' do
      # Skipping due to actual network calls are made
      xit 'denies access' do
        get api_v1_admin_competitions_path,
            headers: authentication_headers_for(user, devise_api_token)

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'when not authenticated' do
      # Skipping due to actual network calls are made
      xit 'denies access' do
        get api_v1_admin_competitions_path

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'GET /api/v1/admin/competitions/:id' do
    context 'when authenticated as admin' do
      # Skipping due to actual network calls are made
      xit 'returns a specific competition with admin data' do
        get api_v1_admin_competition_path(@competition),
            headers: authentication_headers_for(admin_user, admin_token)

        expect(response).to have_http_status(:success)
        expect(parsed_body.data.id).to eq @competition.id
        expect(parsed_body.data.name).to eq @competition.name
        expect(parsed_body.data.code).to eq @competition.code
        expect(parsed_body.message).to eq ['Competition found']
      end

      # Skipping due to actual network calls are made
      xit 'returns warning for not setup competition' do
        # Use a code that we know doesn't exist yet but is supported by the controller
        get api_v1_admin_competition_path('SV'),
            headers: authentication_headers_for(admin_user, admin_token)

        expect(response).to have_http_status(:success)
        expect(parsed_body.message).to eq 'Competition is not yet setup'
        expect(parsed_body.type).to eq 'warning'
      end

      # Skipping due to actual network calls are made
      xit 'returns 404 for invalid competition ID' do
        get api_v1_admin_competition_path('INVALID'),
            headers: authentication_headers_for(admin_user, admin_token)

        expect(response).to have_http_status(:not_found)
      end
    end

    context 'when authenticated as regular user' do
      # Skipping due to actual network calls are made
      xit 'denies access' do
        get api_v1_admin_competition_path(@competition),
            headers: authentication_headers_for(user, devise_api_token)

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'POST /api/v1/admin/competitions/initialize_or_update' do
    context 'when authenticated as admin' do
      before do
        # Mock the service methods to avoid actual API calls
        allow_any_instance_of(FootballDataService).to receive(:import_competition)
          .and_return(@competition)
        allow_any_instance_of(ApiFootballService).to receive(:import_competition)
          .and_return(@competition)
      end

      # Skipping due to actual network calls are made
      xit 'initializes PL competition successfully' do
        post initialize_or_update_api_v1_admin_competitions_path,
             params: { code: 'PL' }.to_json,
             headers: authentication_headers_for(admin_user, admin_token)
        expect(response).to have_http_status(:success)
        expect(parsed_body.message).to eq ['Competition initialized or updated successfully']
      end

      # Skipping due to actual network calls are made
      xit 'initializes CL competition successfully' do
        post initialize_or_update_api_v1_admin_competitions_path,
             params: { code: 'CL' }.to_json,
             headers: authentication_headers_for(admin_user, admin_token)
        expect(response).to have_http_status(:success)
        expect(parsed_body.message).to eq ['Competition initialized or updated successfully']
      end

      # Skipping due to actual network calls are made
      xit 'initializes SV competition successfully' do
        post initialize_or_update_api_v1_admin_competitions_path,
             params: { code: 'SV' }.to_json,
             headers: authentication_headers_for(admin_user, admin_token)
        expect(response).to have_http_status(:success)
        expect(parsed_body.message).to eq ['Competition initialized or updated successfully']
      end

      # Skipping due to actual network calls are made
      xit 'returns error for invalid competition code' do
        post initialize_or_update_api_v1_admin_competitions_path,
             params: { code: 'INVALID' }.to_json,
             headers: authentication_headers_for(admin_user, admin_token)
        expect(response).to have_http_status(:bad_request)
        expect(parsed_body.errors).to include('Unsupported competition code')
      end

      # Skipping due to actual network calls are made
      xit 'returns error when code is missing' do
        post initialize_or_update_api_v1_admin_competitions_path,
             headers: authentication_headers_for(admin_user, admin_token)

        expect(response).to have_http_status(:bad_request)
        expect(parsed_body.errors).to include('Competition code is required')
      end

      # Skipping due to actual network calls are made
      xit 'handles API errors gracefully' do
        allow_any_instance_of(FootballDataService).to receive(:import_competition)
          .and_raise(FootballDataService::ApiError.new('API unavailable'))

        post initialize_or_update_api_v1_admin_competitions_path,
             params: { code: 'PL' }.to_json,
             headers: authentication_headers_for(admin_user, admin_token)

        expect(response).to have_http_status(:unprocessable_entity)
        expect(parsed_body.errors.first).to include('API Error')
      end
    end

    context 'when authenticated as regular user' do
      # Skipping due to actual network calls are made
      xit 'denies access' do
        post initialize_or_update_api_v1_admin_competitions_path,
             params: { code: 'PL' }.to_json,
             headers: authentication_headers_for(user, devise_api_token)

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'POST /api/v1/admin/competitions/:id/update_teams' do
    context 'when authenticated as admin' do
      before do
        # Mock the service methods
        allow_any_instance_of(FootballDataService).to receive(:import_teams_for_competition)
          .and_return(true)
        allow_any_instance_of(ApiFootballService).to receive(:import_teams_for_competition)
          .and_return(true)
      end

      # Skipping due to actual network calls are made
      xit 'updates teams for a competition' do
        post update_teams_api_v1_admin_competition_path(@competition),
             params: {}.to_json,
             headers: authentication_headers_for(admin_user, admin_token)

        expect(response).to have_http_status(:success)
        expect(parsed_body.message).to eq ['Teams updated successfully']
      end

      # Skipping due to actual network calls are made
      xit 'handles service errors gracefully' do
        allow_any_instance_of(FootballDataService).to receive(:import_teams_for_competition)
          .and_raise(StandardError.new('Service unavailable'))

        post update_teams_api_v1_admin_competition_path(@competition),
             headers: authentication_headers_for(admin_user, admin_token)

        expect(response).to have_http_status(:unprocessable_entity)
        expect(parsed_body.errors).to include('Service unavailable')
      end
    end

    context 'when authenticated as regular user' do
      # Skipping due to actual network calls are made
      xit 'denies access' do
        post update_teams_api_v1_admin_competition_path(@competition),
             headers: authentication_headers_for(user, devise_api_token)

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'POST /api/v1/admin/competitions/:id/update_matches' do
    context 'when authenticated as admin' do
      before do
        # Mock the service methods
        allow_any_instance_of(FootballDataService).to receive(:update_matches)
          .and_return(true)
        allow_any_instance_of(ApiFootballService).to receive(:update_matches)
          .and_return(true)
      end

      # Skipping due to actual network calls are made
      xit 'updates matches for a competition' do
        post update_matches_api_v1_admin_competition_path(@competition),
             params: {}.to_json,
             headers: authentication_headers_for(admin_user, admin_token)

        expect(response).to have_http_status(:success)
        expect(parsed_body.message).to eq ['Matches updated successfully']
      end

      # Skipping due to actual network calls are made
      xit 'handles service errors gracefully' do
        allow_any_instance_of(FootballDataService).to receive(:update_matches)
          .and_raise(StandardError.new('Service unavailable'))

        post update_matches_api_v1_admin_competition_path(@competition),
             headers: authentication_headers_for(admin_user, admin_token)

        expect(response).to have_http_status(:unprocessable_entity)
        expect(parsed_body.errors).to include('Service unavailable')
      end
    end

    context 'when authenticated as regular user' do
      # Skipping due to actual network calls are made
      xit 'denies access' do
        post update_matches_api_v1_admin_competition_path(@competition),
             headers: authentication_headers_for(user, devise_api_token)

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'GET /api/v1/admin/competitions/:id/current_season_teams' do
    context 'when authenticated as admin' do
      # Skipping due to actual network calls are made
      xit 'returns all teams for current season' do
        get current_season_teams_api_v1_admin_competition_path(@competition),
            headers: authentication_headers_for(admin_user, admin_token)

        expect(response).to have_http_status(:success)
        expect(parsed_body.data).to be_an(Array)
        expect(parsed_body.data.length).to be > 0
        expect(parsed_body.message).to eq ['Teams found']
      end

      # Skipping due to actual network calls are made
      xit 'returns 404 when competition has no current season' do
        # Create a mock competition without a current season
        competition_without_season = instance_double(Competition,
                                                     id: 999,
                                                     current_season: nil,
                                                     code: 'NO_SEASON')

        allow(Competition).to receive(:find_by)
          .with(id: '999')
          .and_return(competition_without_season)

        get current_season_teams_api_v1_admin_competition_path(999),
            headers: authentication_headers_for(admin_user, admin_token)

        expect(response).to have_http_status(:not_found)
        expect(parsed_body.errors).to include('Current season not found')
      end
    end

    context 'when authenticated as regular user' do
      # Skipping due to actual network calls are made
      xit 'denies access' do
        get current_season_teams_api_v1_admin_competition_path(@competition),
            headers: authentication_headers_for(user, devise_api_token)

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'POST /api/v1/admin/competitions/:id/set_winner' do
    context 'when authenticated as admin' do
      # Skipping due to actual network calls are made
      xit 'sets the winner for a competition' do
        # Create a mock or spy on Competition#save to ensure it's called properly
        allow_any_instance_of(Competition).to receive(:save).and_return(true)

        post set_winner_api_v1_admin_competition_path(@competition),
             params: { team_id: @team.id }.to_json,
             headers: authentication_headers_for(admin_user, admin_token)

        expect(response).to have_http_status(:success)
        expect(parsed_body.message).to eq ['Competition winner updated successfully']
      end

      # Skipping due to actual network calls are made
      xit 'returns 404 when team does not exist' do
        post set_winner_api_v1_admin_competition_path(@competition),
             params: { team_id: 9999 }.to_json,
             headers: authentication_headers_for(admin_user, admin_token)

        expect(response).to have_http_status(:not_found)
        expect(parsed_body.errors).to include('Team not found')
      end

      # Skipping due to actual network calls are made
      xit 'returns 400 when team is not part of the current season' do
        # Mock a team instead of creating one to avoid validation issues
        other_team = instance_double(Team, id: 9876)
        allow(Team).to receive(:find_by).with(id: 9876).and_return(other_team)

        # Make sure the team is not part of the current season
        allow(@competition.current_season).to receive(:teams).and_return([])

        post set_winner_api_v1_admin_competition_path(@competition),
             params: { team_id: 9876 }.to_json,
             headers: authentication_headers_for(admin_user, admin_token)

        expect(response).to have_http_status(:bad_request)
        expect(parsed_body.errors).to include('Team is not part of the current season')
      end
    end

    context 'when authenticated as regular user' do
      # Skipping due to actual network calls are made
      xit 'denies access' do
        post set_winner_api_v1_admin_competition_path(@competition),
             params: { team_id: @team.id }.to_json,
             headers: authentication_headers_for(user, devise_api_token)

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
