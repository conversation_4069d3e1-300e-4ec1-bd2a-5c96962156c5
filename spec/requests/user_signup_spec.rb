require 'rails_helper'

RSpec.describe 'User Signup', type: :request do
  let(:valid_params) do
    {
      user: {
        email: '<EMAIL>',
        password: 'password123',
        username: 'testuser'
      }
    }
  end

  describe 'POST /users/tokens/sign_up' do
    xit 'creates a user and queues confirmation email job' do
      expect {
        post '/users/tokens/sign_up', params: valid_params, as: :json
      }.to change(User, :count).by(1)
        .and have_enqueued_job(ConfirmationEmailJob)

      expect(response).to have_http_status(:created)
      expect(json_response['status']).to eq('success')
      expect(json_response['message']).to include('check your email')
      expect(json_response['token']).to be_present
    end

    context 'with invalid parameters' do
      let(:invalid_params) do
        {
          user: {
            email: 'invalid-email',
            password: 'short',
            username: ''
          }
        }
      end

      it 'returns validation errors' do
        post '/users/tokens/sign_up', params: invalid_params, as: :json

        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['status']).to eq('error')
        expect(json_response['errors']).to include(/Email is invalid/)
      end
    end
  end
end