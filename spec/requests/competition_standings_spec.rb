require 'rails_helper'

RSpec.describe 'Competition Standings', type: :request do
  let(:user) { create(:user, username: "user1_#{Time.now.to_i}") }
  let(:devise_api_token) { create(:devise_api_token, resource_owner: user) }
  let(:headers) { authentication_headers_for(user, devise_api_token) }

  let(:area) { create(:area) }
  let(:competition) { create(:competition, area:) }
  let(:season) { create(:season, competition:) }

  describe 'GET /api/v1/competitions/:id/standings' do
    it 'returns the standings for a competition' do
      # Set the current season for the competition
      competition.update(current_season: season)

      # Mock the calculate_competition_standings method to return predictable data
      allow_any_instance_of(Api::V1::CompetitionsController).to receive(:calculate_competition_standings).and_return([
                                                                                                                       { username: 'user2', points: 25, correct: 5, incorrect: 2, perfect: 5, position: 1,
                                                                                                                         top_message: '🏆 user2 is leading the competition! Congratulations!' },
                                                                                                                       { username: 'user3', points: 20, correct: 4, incorrect: 3, perfect: 4, position: 2,
                                                                                                                         top_message: '🥈 user3 is in second place. Keep pushing!' },
                                                                                                                       { username: user.username, points: 15, correct: 3, incorrect: 4, perfect: 3, position: 3,
                                                                                                                         top_message: '🥉 user1 is in third place. Great job!' }
                                                                                                                     ])

      get("/api/v1/competitions/#{competition.id}/standings", headers:)

      expect(response).to have_http_status(:success)

      json = JSON.parse(response.body)
      expect(json['competition']['id']).to eq(competition.id)
      expect(json['competition']['name']).to eq(competition.name)
      expect(json['competition']['standings'].size).to eq(3)

      # Check that standings are sorted by points in descending order
      standings = json['competition']['standings']
      expect(standings[0]['username']).to eq('user2')
      expect(standings[0]['points']).to eq(25)
      expect(standings[0]['position']).to eq(1)

      expect(standings[1]['username']).to eq('user3')
      expect(standings[1]['points']).to eq(20)
      expect(standings[1]['position']).to eq(2)

      expect(standings[2]['username']).to eq(user.username)
      expect(standings[2]['points']).to eq(15)
      expect(standings[2]['position']).to eq(3)

      # Check for top messages
      expect(standings[0]['top_message']).to include('leading')
      expect(standings[0]['top_message']).to include('🏆')

      expect(standings[1]['top_message']).to include('second place')
      expect(standings[1]['top_message']).to include('🥈')

      expect(standings[2]['top_message']).to include('third place')
      expect(standings[2]['top_message']).to include('🥉')
    end

    context 'with a historical season' do
      let(:old_season) { create(:season, competition:, start_date: 1.year.ago, end_date: 6.months.ago) }

      it 'returns historical standings when season_id is provided' do
        # Set the current season for the competition
        competition.update(current_season: season)

        # Make sure the old season is associated with the competition
        old_season.update(competition:)

        # Mock the calculate_competition_standings method to return predictable data
        allow_any_instance_of(Api::V1::CompetitionsController).to receive(:calculate_competition_standings).and_return([
                                                                                                                         { username: user.username, points: 30, correct: 6, incorrect: 1, perfect: 6, position: 1,
                                                                                                                           top_message: '🏆 user1 is leading the competition! Congratulations!' },
                                                                                                                         { username: 'user2', points: 20, correct: 4, incorrect: 3, perfect: 4, position: 2,
                                                                                                                           top_message: '🥈 user2 is in second place. Keep pushing!' },
                                                                                                                         { username: 'user3', points: 10, correct: 2, incorrect: 5, perfect: 2, position: 3,
                                                                                                                           top_message: '🥉 user3 is in third place. Great job!' }
                                                                                                                       ])

        get("/api/v1/competitions/#{competition.id}/standings", params: { season_id: old_season.id }, headers:)

        expect(response).to have_http_status(:success)

        json = JSON.parse(response.body)
        expect(json['competition']['id']).to eq(competition.id)
        expect(json['competition']['name']).to eq(competition.name)
        expect(json['competition']['season']['id']).to eq(old_season.id)
        expect(json['competition']['standings'].size).to eq(3)

        # Check that historical standings are correct
        standings = json['competition']['standings']
        expect(standings[0]['username']).to eq(user.username)
        expect(standings[0]['points']).to eq(30)
        expect(standings[0]['position']).to eq(1)

        expect(standings[1]['username']).to eq('user2')
        expect(standings[1]['points']).to eq(20)
        expect(standings[1]['position']).to eq(2)

        expect(standings[2]['username']).to eq('user3')
        expect(standings[2]['points']).to eq(10)
        expect(standings[2]['position']).to eq(3)
      end
    end
  end
end
