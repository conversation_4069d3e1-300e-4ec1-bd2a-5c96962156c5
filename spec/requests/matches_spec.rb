require 'rails_helper'

RSpec.describe 'Matches API', type: :request do
  let(:user) { create(:user, username: 'test', email: '<EMAIL>', password: 'password') }
  let(:admin_user) { create(:user, username: 'admin', email: '<EMAIL>', password: 'password', admin: true) }
  let(:devise_api_token) { create(:devise_api_token, resource_owner: user) }
  let(:admin_token) { create(:devise_api_token, resource_owner: admin_user) }

  before do
    populate_database
    @season_id = Season.first.id
    @competition_id = Competition.first.id
    @competition_code = Competition.first.code
  end

  describe 'GET /api/v1/matches' do
    context 'when authenticated' do
      it 'returns matches for the specified matchday and season' do
        get api_v1_matches_path,
            headers: authentication_headers_for(user, devise_api_token),
            params: { matchday: 2, seasonId: @season_id }

        expect(response).to have_http_status(:success)
        expect(parsed_body.matches.length).to eq 3
        expect(parsed_body.matches[0].matchday).to eq 2
        expect(parsed_body.matches[0].status).to eq 'SCHEDULED'
        expect(parsed_body.matches[0].venue).to be_an_instance_of(String)
        expect(parsed_body.matches[0].home_team.name).to eq 'Fram'
        expect(parsed_body.matches[0].away_team.name).to eq 'Valur'
        expect(parsed_body.matches[0].home_team.form.string).to be_a(String)
        expect(parsed_body.matches[0].away_team.form.string).to be_a(String)
      end

      it 'returns empty array when no matches exist for the matchday' do
        get api_v1_matches_path,
            headers: authentication_headers_for(user, devise_api_token),
            params: { matchday: 99, seasonId: @season_id }

        expect(response).to have_http_status(:not_found)
        expect(parsed_body.matches).to eq []
        expect(parsed_body.status).to eq 404
        expect(parsed_body.message).to eq ['No matches found']
      end

      it 'includes stage parameter in the request when provided' do
        get api_v1_matches_path,
            headers: authentication_headers_for(user, devise_api_token),
            params: { matchday: 2, seasonId: @season_id, stage: 'REGULAR_SEASON' }

        expect(response).to have_http_status(:success)
        expect(parsed_body.matches.length).to be >= 0
      end
    end

    context 'when not authenticated' do
      it 'returns unauthorized status' do
        get api_v1_matches_path, params: { matchday: 2, seasonId: @season_id }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'GET /api/v1/matches/:id' do
    let(:match) { Match.where(matchday: 2).first }

    it 'returns a specific match' do
      get api_v1_match_path(match), headers: authentication_headers_for(user, devise_api_token)

      expect(response).to have_http_status(:success)
      expect(parsed_body.data.id).to eq match.id
      expect(parsed_body.data.matchday).to eq match.matchday
      expect(parsed_body.message).to eq ['Match found']
    end

    it 'returns 404 when match does not exist' do
      get api_v1_match_path(999_999), headers: authentication_headers_for(user, devise_api_token)

      expect(response).to have_http_status(:not_found)
    end
  end
end
