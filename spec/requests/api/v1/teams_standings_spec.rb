require 'rails_helper'

RSpec.describe 'Team Standings API', type: :request do
  let(:user) { create(:user) }
  let(:devise_api_token) { create(:devise_api_token, resource_owner: user) }
  let(:headers) { authentication_headers_for(user, devise_api_token) }
  let(:area) { create(:area) }
  let(:competition) { create(:competition, area:) }
  let(:season) { create(:season, competition:) }
  let(:team) { create(:team, :valur, area:) }

  before do
    # Add the team to the season
    season.teams << team

    # Create some fans for the team
    3.times do |i|
      fan = create(:user, email: "fan#{i}@example.com", username: "fan#{i}")
      create(:favorite_team, user: fan, team:, competition:)

      # Create some round predictions for each fan
      rp = create(:round_prediction, user: fan, competition_id: competition.id, season_id: season.id, matchday: 1)

      # Create match predictions with different points (only 0, 1, 3 are valid)
      points_value = i == 2 ? 3 : i # Convert 2 to 3 for valid points

      # Create a unique opponent team for each fan
      opponent = create(:team,
                        name: "Opponent #{i}",
                        short_name: "OP#{i}",
                        tla: "OP#{i}",
                        area:)

      match = create(:match, season:, home_team: team, away_team: opponent)
      create(:match_prediction, round_prediction: rp, match:, points: points_value)
    end
  end

  describe 'GET /api/v1/teams/:id/standings' do
    context 'when not authenticated' do
      it 'returns unauthorized status' do
        get "/api/v1/teams/#{team.id}/standings"
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'when authenticated' do
      it 'returns team fans standings' do
        get("/api/v1/teams/#{team.id}/standings", headers:)

        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)

        expect(json['team']['id']).to eq(team.id)
        expect(json['standings']).to be_an(Array)
        expect(json['standings'].size).to eq(3) # 3 fans

        # Check that standings are sorted by points
        expect(json['standings'].first['position']).to eq(1)
        expect(json['standings'].last['position']).to eq(3)

        # Check that top messages are present for top 3
        expect(json['standings'].first['top_message']).to include('leading among fans')
      end

      it 'filters by competition_id when provided' do
        # Create another competition with the same team as favorite
        another_competition = create(:competition, area:, name: 'Another Competition')
        another_season = create(:season, competition: another_competition)
        another_season.teams << team

        # Create a fan with predictions in both competitions
        fan = create(:user, email: '<EMAIL>', username: 'multi_comp_fan')
        create(:favorite_team, user: fan, team:, competition: another_competition)

        # Create predictions for the new competition
        rp = create(:round_prediction, user: fan, competition_id: another_competition.id, season_id: another_season.id,
                                       matchday: 1)
        # Create a unique opponent team
        opponent = create(:team,
                          name: 'Another Comp Opponent',
                          short_name: 'ACO',
                          tla: 'ACO',
                          area:)

        match = create(:match, season: another_season, home_team: team, away_team: opponent)
        create(:match_prediction, round_prediction: rp, match:, points: 3) # Perfect prediction

        # Request standings filtered by the new competition
        get("/api/v1/teams/#{team.id}/standings?competition_id=#{another_competition.id}", headers:)

        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)

        # Should only include the fan with predictions in the specified competition
        expect(json['competition']['id']).to eq(another_competition.id)
        expect(json['standings'].size).to eq(1)
        expect(json['standings'].first['username']).to eq('multi_comp_fan')
      end

      it 'filters by season_id when provided' do
        # Create another season for the same competition
        new_season = create(:season, competition:, start_date: 1.year.from_now, end_date: 2.years.from_now)
        new_season.teams << team

        # Create a fan with predictions in both seasons
        fan = create(:user, email: '<EMAIL>', username: 'multi_season_fan')
        create(:favorite_team, user: fan, team:, competition:)

        # Create predictions for the new season
        rp = create(:round_prediction, user: fan, competition_id: competition.id, season_id: new_season.id, matchday: 1)
        # Create a unique opponent team
        opponent = create(:team,
                          name: 'New Season Opponent',
                          short_name: 'NSO',
                          tla: 'NSO',
                          area:)

        match = create(:match, season: new_season, home_team: team, away_team: opponent)
        create(:match_prediction, round_prediction: rp, match:, points: 3) # Perfect prediction

        # Request standings filtered by the new season
        get("/api/v1/teams/#{team.id}/standings?season_id=#{new_season.id}", headers:)

        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)

        # Should only include the fan with predictions in the specified season
        expect(json['season']['id']).to eq(new_season.id)
        expect(json['standings'].size).to eq(1)
        expect(json['standings'].first['username']).to eq('multi_season_fan')
      end

      it 'returns empty standings when team has no fans' do
        # Create a team with no fans
        teamless = create(:team, :kr, area:)

        get("/api/v1/teams/#{teamless.id}/standings", headers:)

        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)

        expect(json['team']['id']).to eq(teamless.id)
        expect(json['standings']).to eq([])
        expect(json['message']).to include('No fans found for this team')
      end

      it 'returns 404 when team does not exist' do
        get('/api/v1/teams/999999/standings', headers:)

        expect(response).to have_http_status(:not_found)
        json = JSON.parse(response.body)

        expect(json['errors']).to include('Team not found')
      end

      it 'ensures each user only appears once in the standings' do
        # Since we can't create duplicate favorite teams due to validation,
        # let's test that our distinct call works by mocking the fans method
        # to return duplicate users
        fan = User.find_by(email: '<EMAIL>')

        # Allow the test to access private methods
        allow_any_instance_of(Api::V1::TeamsController).to receive(:calculate_team_fans_standings).and_wrap_original do |original, fans, *args|
          # Duplicate the first fan to simulate the issue
          duplicated_fans = fans.to_a + [fan]
          original.call(User.where(id: duplicated_fans.map(&:id)), *args)
        end

        get("/api/v1/teams/#{team.id}/standings", headers:)

        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)

        # Extract usernames from standings
        usernames = json['standings'].map { |standing| standing['username'] }

        # Check that each username only appears once
        expect(usernames.uniq.length).to eq(usernames.length)

        # Specifically check that fan0 only appears once
        expect(usernames.count('fan0')).to eq(1)
      end
    end
  end
end
