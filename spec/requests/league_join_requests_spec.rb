require 'rails_helper'

RSpec.describe 'LeagueJoinRequests', type: :request do
  let(:user) { create(:user) }
  let(:owner) { create(:user) }
  let(:league) { create(:league, owner:, open: false) }
  let(:token) { create(:devise_api_token, resource_owner: user) }
  let(:owner_token) { create(:devise_api_token, resource_owner: owner) }

  before do
    # Mock YouTube service to avoid actual API calls
    allow_any_instance_of(BragRightsYouTubeService).to receive(:verify_subscription?).and_return(true)
  end

  describe 'POST /api/v1/leagues/:league_id/join_requests' do
    context 'when user is authenticated' do
      before do
        # Set authentication headers
        @auth_headers = { 'Authorization' => "Bearer #{token.access_token}" }
      end

      it 'creates a join request' do
        expect do
          post "/api/v1/leagues/#{league.id}/join_requests", headers: @auth_headers
        end.to change(LeagueJoinRequest, :count).by(1)

        expect(response).to have_http_status(:created)
        expect(JSON.parse(response.body)['message']).to include('Join request submitted successfully')
      end

      context 'when user is already a member' do
        before do
          create(:membership, user:, league:)
        end

        it 'returns an error' do
          post "/api/v1/leagues/#{league.id}/join_requests", headers: @auth_headers

          expect(response).to have_http_status(:unprocessable_entity)
          expect(JSON.parse(response.body)['error']).to include('You are already a member of this league')
        end
      end

      context 'when user has reached maximum leagues' do
        before do
          # Create 5 leagues and memberships for the user
          5.times do |i|
            league = create(:league, name: "Test League #{i}", owner: create(:user))
            create(:membership, user:, league:)
          end
        end

        it 'returns an error' do
          post "/api/v1/leagues/#{league.id}/join_requests", headers: @auth_headers

          expect(response).to have_http_status(:unprocessable_entity)
          expect(JSON.parse(response.body)['errors']).to eq('MAXIMUM_LEAGUES_REACHED')
        end
      end

      context 'when league is subscriber-only' do
        let(:subscriber_league) do
          create(:league, owner:, open: false, subscriber_only: true, youtube_channel_id: 'test_channel')
        end

        context 'when user is subscribed' do
          before do
            allow_any_instance_of(League).to receive(:user_meets_subscription_requirements?).and_return(true)
          end

          it 'creates a join request' do
            expect do
              post "/api/v1/leagues/#{subscriber_league.id}/join_requests", headers: @auth_headers
            end.to change(LeagueJoinRequest, :count).by(1)

            expect(response).to have_http_status(:created)
          end
        end

        context 'when user is not subscribed' do
          before do
            allow_any_instance_of(League).to receive(:user_meets_subscription_requirements?).and_return(false)
          end

          it 'returns an error' do
            post "/api/v1/leagues/#{subscriber_league.id}/join_requests", headers: @auth_headers

            expect(response).to have_http_status(:forbidden)
            expect(JSON.parse(response.body)['error']).to include('This league is only available to YouTube subscribers')
          end
        end
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized' do
        post "/api/v1/leagues/#{league.id}/join_requests"

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'GET /api/v1/leagues/:league_id/join_requests' do
    before do
      # Create some join requests
      3.times do
        create(:league_join_request, league:, user: create(:user))
      end

      # Set authentication headers for league owner
      @owner_headers = { 'Authorization' => "Bearer #{owner_token.access_token}" }
    end

    context 'when user is the league owner' do
      it 'returns all join requests for the league' do
        get "/api/v1/leagues/#{league.id}/join_requests", headers: @owner_headers

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['data'].size).to eq(3)
      end
    end

    context 'when user is not the league owner' do
      it 'returns join requests' do
        get "/api/v1/leagues/#{league.id}/join_requests", headers: { 'Authorization' => "Bearer #{token.access_token}" }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['data'].size).to eq(3)
      end
    end
  end

  describe 'GET /api/v1/join_requests/user_requests' do
    before do
      # Create some join requests for the user
      2.times do |i|
        league = create(:league, name: "League #{i}", owner: create(:user), open: false)
        create(:league_join_request, league:, user:)
      end

      # Set authentication headers
      @auth_headers = { 'Authorization' => "Bearer #{token.access_token}" }
    end

    it 'returns all join requests for the authenticated user' do
      get '/api/v1/join_requests/user_requests', headers: @auth_headers

      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['data'].size).to eq(2)
    end
  end

  describe 'PUT /api/v1/leagues/:league_id/join_requests/:id' do
    let!(:join_request) { create(:league_join_request, league:, user:) }

    context 'when user is the league owner' do
      before do
        # Set authentication headers for league owner
        @owner_headers = { 'Authorization' => "Bearer #{owner_token.access_token}" }
      end

      context 'when accepting a request' do
        it 'accepts the request and creates a membership' do
          expect do
            put "/api/v1/leagues/#{league.id}/join_requests/#{join_request.id}",
                params: { status: 'accepted' },
                headers: @owner_headers
          end.to change(Membership, :count).by(1)

          expect(response).to have_http_status(:ok)
          expect(JSON.parse(response.body)['message']).to include('Request accepted')

          # Check that the request status was updated
          join_request.reload
          expect(join_request.status).to eq('accepted')
        end
      end

      context 'when rejecting a request' do
        it 'rejects the request without creating a membership' do
          expect do
            put "/api/v1/leagues/#{league.id}/join_requests/#{join_request.id}",
                params: { status: 'rejected' },
                headers: @owner_headers
          end.not_to change(Membership, :count)

          expect(response).to have_http_status(:ok)
          expect(JSON.parse(response.body)['message']).to include('Request rejected')

          # Check that the request status was updated
          join_request.reload
          expect(join_request.status).to eq('rejected')
        end
      end

      context 'with invalid status' do
        it 'returns an error' do
          put "/api/v1/leagues/#{league.id}/join_requests/#{join_request.id}",
              params: { status: 'invalid' },
              headers: @owner_headers

          expect(response).to have_http_status(:unprocessable_entity)
        end
      end
    end

    context 'when user is not the league owner' do
      it 'returns unauthorized' do
        put "/api/v1/leagues/#{league.id}/join_requests/#{join_request.id}",
            params: { status: 'accepted' },
            headers: { 'Authorization' => "Bearer #{token.access_token}" }

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
