require 'rails_helper'

RSpec.describe 'LeagueMemberships', type: :request do
  let(:user) { create(:user) }
  let(:owner) { create(:user) }
  let(:open_league) { create(:league, owner:, open: true) }
  let(:private_league) { create(:league, owner:, open: false) }
  let(:token) { create(:devise_api_token, resource_owner: user) }

  before do
    # Mock YouTube service to avoid actual API calls
    allow_any_instance_of(BragRightsYouTubeService).to receive(:verify_subscription?).and_return(true)
  end

  describe 'POST /api/v1/leagues/:league_id/memberships' do
    context 'when user is authenticated' do
      before do
        # Set authentication headers
        @auth_headers = { 'Authorization' => "Bearer #{token.access_token}" }
      end

      context 'with an open league' do
        it 'creates a membership' do
          # Check if the user is already a member of the league
          expect(open_league.users).not_to include(user)

          post "/api/v1/leagues/#{open_league.id}/memberships", headers: @auth_headers

          # After the request, the user should be a member
          open_league.reload
          expect(open_league.users).to include(user)
          expect(response).to have_http_status(:created)
          expect(JSON.parse(response.body)['message']).to include('You have joined the league')
        end

        context 'when user is already a member' do
          before do
            create(:membership, user:, league: open_league)
          end

          it 'returns an error' do
            post "/api/v1/leagues/#{open_league.id}/memberships", headers: @auth_headers

            expect(response).to have_http_status(:unprocessable_entity)
            expect(JSON.parse(response.body)['error']).to include('You are already a member of this league')
          end
        end

        context 'when user has reached maximum leagues' do
          before do
            # Create 5 leagues and memberships for the user
            5.times do |i|
              league = create(:league, name: "Test League #{i}", owner: create(:user))
              create(:membership, user:, league:)
            end
          end

          it 'returns an error' do
            post "/api/v1/leagues/#{open_league.id}/memberships", headers: @auth_headers

            expect(response).to have_http_status(:unprocessable_entity)
            expect(JSON.parse(response.body)['errors']).to eq('MAXIMUM_LEAGUES_REACHED')
          end
        end
      end

      context 'with a private league' do
        it 'returns an error' do
          post "/api/v1/leagues/#{private_league.id}/memberships", headers: @auth_headers

          expect(response).to have_http_status(:unprocessable_entity)
          expect(JSON.parse(response.body)['error']).to include('This is a private league')
        end
      end

      context 'with a subscriber-only league' do
        let(:subscriber_league) do
          create(:league, owner:, open: true, subscriber_only: true, youtube_channel_id: 'test_channel')
        end

        context 'when user is subscribed' do
          before do
            allow_any_instance_of(League).to receive(:user_meets_subscription_requirements?).and_return(true)
          end

          it 'creates a membership with subscription status' do
            # Check if the user is already a member of the league
            expect(subscriber_league.users).not_to include(user)

            post "/api/v1/leagues/#{subscriber_league.id}/memberships", headers: @auth_headers

            # After the request, the user should be a member
            subscriber_league.reload
            expect(subscriber_league.users).to include(user)
            expect(response).to have_http_status(:created)

            # Check that the membership has subscription data
            membership = Membership.find_by(user:, league: subscriber_league)
            expect(membership.subscription_status).to eq('active')
            expect(membership.subscription_verified_at).to be_present
          end
        end

        context 'when user is not subscribed' do
          before do
            allow_any_instance_of(League).to receive(:user_meets_subscription_requirements?).and_return(false)
          end

          it 'returns an error' do
            post "/api/v1/leagues/#{subscriber_league.id}/memberships", headers: @auth_headers

            expect(response).to have_http_status(:forbidden)
            expect(JSON.parse(response.body)['error']).to include('This league is only available to YouTube subscribers')
          end
        end
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized' do
        post "/api/v1/leagues/#{open_league.id}/memberships"

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'DELETE /api/v1/leagues/:league_id/memberships/:id' do
    let!(:membership) { create(:membership, user:, league: open_league) }

    before do
      # Set authentication headers
      @auth_headers = { 'Authorization' => "Bearer #{token.access_token}" }
    end

    context 'when user is a member of the league' do
      it 'removes the membership' do
        expect do
          delete "/api/v1/leagues/#{open_league.id}/memberships/#{membership.id}", headers: @auth_headers
        end.to change(Membership, :count).by(-1)

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['message']).to include('You have left the league')
      end
    end

    context 'when user is not a member of the league' do
      it 'returns an error' do
        # Create a different league
        other_league = create(:league, name: 'Other League', owner: create(:user))
        other_membership = create(:membership, user: create(:user), league: other_league)

        delete "/api/v1/leagues/#{other_league.id}/memberships/#{other_membership.id}", headers: @auth_headers

        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['error']).to include('You are not a member of this league')
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized' do
        delete "/api/v1/leagues/#{open_league.id}/memberships/#{membership.id}"

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
