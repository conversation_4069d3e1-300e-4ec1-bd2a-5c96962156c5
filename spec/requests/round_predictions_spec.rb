require 'rails_helper'

RSpec.describe 'RoundPredictions API', type: :request do
  let(:user) { create(:user, username: 'test', email: '<EMAIL>', password: 'password') }
  let(:other_user) { create(:user, username: 'other', email: '<EMAIL>', password: 'password') }
  let(:devise_api_token) { create(:devise_api_token, resource_owner: user) }
  let(:other_token) { create(:devise_api_token, resource_owner: other_user) }

  before do
    populate_database
    @season_id = Season.first.id
    @competition_id = Competition.first.id

    @round_prediction = create(:round_prediction, user:, matchday: 4, season_id: @season_id,
                                                  competition_id: @competition_id, stage: 'REGULAR_SEASON')
    match1 = Match.where(matchday: 4).first
    @round_prediction.match_predictions.create(match: match1, away_score: 1, home_score: 1)
    match2 = Match.where(matchday: 4)[1]
    @round_prediction.match_predictions.create(match: match2, away_score: 1, home_score: 1)
    match3 = Match.where(matchday: 4)[2]
    @round_prediction.match_predictions.create(match: match3, away_score: 1, home_score: 1)

    round_prediction2 = create(:round_prediction, user:, matchday: 5, season_id: @season_id,
                                                  competition_id: @competition_id, stage: 'REGULAR_SEASON')
    match4 = Match.where(matchday: 5).first
    round_prediction2.match_predictions.create(match: match4, away_score: 1, home_score: 1)
    match5 = Match.where(matchday: 5)[1]
    round_prediction2.match_predictions.create(match: match5, away_score: 1, home_score: 1)
    match6 = Match.where(matchday: 5)[2]
    round_prediction2.match_predictions.create(match: match6, away_score: 1, home_score: 1)
  end

  describe 'GET /api/v1/round_predictions' do
    it 'returns all round predictions for the authenticated user' do
      get api_v1_round_predictions_path,
          headers: authentication_headers_for(user, devise_api_token),
          params: {
            season_id: @season_id,
            competition_id: @competition_id
          }

      expect(response).to have_http_status(:success)
      expect(parsed_body.round_predictions.length).to eq 2
      expect(parsed_body.round_predictions[0].id).to be @round_prediction.id
      expect(parsed_body.round_predictions[0].matchday).to be 4
      expect(parsed_body.round_predictions[0].season_id).to be @season_id
      expect(parsed_body.round_predictions[0].competition_id).to be @competition_id
      expect(parsed_body.message).to eq ['Round predictions found']
    end

    it 'returns round predictions for specific matchdays when provided' do
      get api_v1_round_predictions_path,
          headers: authentication_headers_for(user, devise_api_token),
          params: {
            season_id: @season_id,
            competition_id: @competition_id,
            matchdays: '4'
          }

      expect(response).to have_http_status(:success)
      expect(parsed_body.round_predictions.length).to eq 1
      expect(parsed_body.round_predictions[0].matchday).to be 4
    end

    it 'requires authentication' do
      get api_v1_round_predictions_path,
          params: {
            season_id: @season_id,
            competition_id: @competition_id
          }

      expect(response).to have_http_status(:unauthorized)
    end
  end

  describe 'GET /api/v1/round_predictions/:id' do
    it 'returns a specific round prediction' do
      get api_v1_round_prediction_path(@round_prediction),
          headers: authentication_headers_for(user, devise_api_token)

      expect(response).to have_http_status(:success)
      expect(parsed_body.round_predictions.id).to be @round_prediction.id
      expect(parsed_body.round_predictions.matchday).to be 4
      expect(parsed_body.round_predictions.season_id).to be @season_id
      expect(parsed_body.round_predictions.competition_id).to be @competition_id
      expect(parsed_body.message).to eq ['Round prediction found']
    end

    it 'cannot access another user\'s round prediction' do
      # Create a prediction for another user
      other_prediction = create(:round_prediction, user: other_user, matchday: 6,
                                                   season_id: @season_id, competition_id: @competition_id,
                                                   stage: 'REGULAR_SEASON')

      # Try to access it with the first user's token
      get api_v1_round_prediction_path(other_prediction),
          headers: authentication_headers_for(user, devise_api_token)

      # It should fail because the round prediction belongs to another user
      expect(response).to have_http_status(:not_found)
    end
  end

  describe 'POST /api/v1/round_predictions' do
    let(:valid_params) do
      params(2)
    end

    it 'creates a new round prediction' do
      expect do
        post api_v1_round_predictions_path,
             headers: authentication_headers_for(user, devise_api_token),
             params: valid_params,
             as: :json
      end.to change(RoundPrediction, :count).by(1)
                                            .and change(MatchPrediction, :count).by(3)

      expect(response).to have_http_status(:success)
      expect(parsed_body.round_predictions.id).not_to be_nil
      expect(parsed_body.round_predictions.matchday).to be 2
      expect(parsed_body.round_predictions.season_id).to be @season_id
      expect(parsed_body.round_predictions.competition_id).to be @competition_id
    end

    it 'updates existing round prediction instead of creating duplicate' do
      # First create a prediction for matchday 6
      post api_v1_round_predictions_path,
           headers: authentication_headers_for(user, devise_api_token),
           params: valid_params,
           as: :json

      # Get the ID of the created prediction
      first_id = parsed_body.round_predictions.id

      # Now try to create another one for the same matchday
      expect do
        post api_v1_round_predictions_path,
             headers: authentication_headers_for(user, devise_api_token),
             params: valid_params,
             as: :json
      end.to_not change(RoundPrediction, :count)

      # The ID should be the same, indicating update not create
      expect(parsed_body.round_predictions.id).to eq first_id
    end

    it 'requires authentication' do
      expect do
        post api_v1_round_predictions_path, params: valid_params, as: :json
      end.to_not change(RoundPrediction, :count)

      expect(response).to have_http_status(:unauthorized)
    end
  end

  # Helper methods
  def match_prediction_expected(match_prediction, expected_content)
    expect(match_prediction.id).to eq expected_content[:id]
    expect(match_prediction.away_score).to eq expected_content[:away]
    expect(match_prediction.home_score).to eq expected_content[:home]
  end

  def params(match_day)
    matches = Match.where(matchday: match_day)
    Rails.logger.debug "Matches for matchday #{match_day}: #{matches.inspect}"

    { round_predictions: {
      matchday: match_day,
      season_id: @season_id,
      competition_id: @competition_id,
      stage: 'REGULAR_SEASON',
      match_predictions: [
        { match_id: matches[0].id, home_score: 2, away_score: 1 },
        { match_id: matches[1].id, home_score: 1, away_score: 1 },
        { match_id: matches[2].id, home_score: 1, away_score: 2 }
      ]
    } }
  end
end
