require 'rails_helper'

RSpec.describe 'YouTube Controller', type: :request do
  let(:user) { create(:user) }
  let(:devise_api_token) { create(:devise_api_token, resource_owner: user) }
  
  describe 'GET /api/v1/youtube/check_subscriber_league_eligibility' do
    context 'when user is not authenticated' do
      it 'returns unauthorized status' do
        get '/api/v1/youtube/check_subscriber_league_eligibility'
        expect(response).to have_http_status(:unauthorized)
      end
    end
    
    context 'when user is authenticated but not eligible' do
      before do
        allow_any_instance_of(User).to receive(:youtube_connected?).and_return(false)
        allow(YoutubeEligibilityService).to receive(:can_create_subscriber_league?).and_return(false)
        allow(YoutubeEligibilityService).to receive(:subscriber_league_ineligibility_reason)
          .and_return('You need to connect your YouTube account')
        allow(YoutubeEligibilityService).to receive(:min_subscribers_required).and_return(100)
      end
      
      it 'returns eligibility status with reason' do
        get '/api/v1/youtube/check_subscriber_league_eligibility', 
            headers: authentication_headers_for(user, devise_api_token)
            
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['eligible']).to be false
        expect(json_response['message']).to include('connect your YouTube account')
        expect(json_response['min_subscribers_required']).to eq(100)
      end
    end
    
    context 'when user is authenticated and eligible' do
      before do
        allow_any_instance_of(User).to receive(:youtube_connected?).and_return(true)
        allow_any_instance_of(User).to receive(:is_content_creator?).and_return(true)
        allow_any_instance_of(User).to receive(:verified_youtube_creator?).and_return(true)
        allow_any_instance_of(User).to receive(:youtube_subscriber_count).and_return(200)
        allow(YoutubeEligibilityService).to receive(:can_create_subscriber_league?).and_return(true)
        allow(YoutubeEligibilityService).to receive(:min_subscribers_required).and_return(100)
      end
      
      it 'returns eligible status' do
        get '/api/v1/youtube/check_subscriber_league_eligibility', 
            headers: authentication_headers_for(user, devise_api_token)
            
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['eligible']).to be true
        expect(json_response['message']).to include('eligible')
        expect(json_response['subscriber_count']).to eq(200)
        expect(json_response['min_subscribers_required']).to eq(100)
      end
    end
  end
end
