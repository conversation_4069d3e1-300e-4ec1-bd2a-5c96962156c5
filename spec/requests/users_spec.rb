require 'rails_helper'

RSpec.describe 'Api::V1::Users', type: :request do
  let(:user) { create(:user, password: 'password123') }
  let(:token) { create(:devise_api_token, resource_owner: user) }
  let(:headers) do
    authentication_headers_for(user, token).merge({
                                                    'Content-Type' => 'application/json'
                                                  })
  end

  describe 'GET /api/v1/users/:id' do
    context 'when authenticated' do
      it 'returns the user details' do
        get("/api/v1/users/#{user.id}", headers:)

        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response['status']).to eq(200)
        expect(json_response['type']).to eq('success')
        expect(json_response['message']).to eq(['User found'])
        expect(json_response['data']).to be_present
      end

      it 'returns 404 for non-existent user' do
        get('/api/v1/users/0', headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    context 'when not authenticated' do
      it 'returns unauthorized status' do
        get "/api/v1/users/#{user.id}"

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'PATCH /api/v1/users/:id' do
    context 'when authenticated' do
      context 'updating username' do
        it 'successfully updates the username' do
          patch("/api/v1/users/#{user.id}",
                params: { username: 'newusername' }.to_json,
                headers:)

          expect(response).to have_http_status(:ok)
          json_response = JSON.parse(response.body)
          expect(json_response['message']).to eq('Username updated successfully')
          expect(user.reload.username).to eq('newusername')
        end

        it 'returns error for invalid username' do
          patch("/api/v1/users/#{user.id}",
                params: { username: '' }.to_json,
                headers:)

          expect(response).to have_http_status(:unprocessable_entity)
          expect(JSON.parse(response.body)).to have_key('errors')
        end
      end

      context 'updating password' do
        it 'successfully updates the password with valid old password' do
          patch("/api/v1/users/#{user.id}",
                params: {
                  old_password: 'password123',
                  new_password: 'newpassword123'
                }.to_json,
                headers:)

          expect(response).to have_http_status(:ok)
          json_response = JSON.parse(response.body)
          expect(json_response['message']).to eq('Password updated successfully')
        end

        it 'returns unauthorized for invalid old password' do
          patch("/api/v1/users/#{user.id}",
                params: {
                  old_password: 'wrongpassword',
                  new_password: 'newpassword123'
                }.to_json,
                headers:)

          expect(response).to have_http_status(:unauthorized)
          json_response = JSON.parse(response.body)
          expect(json_response['error']).to eq('Invalid old password')
        end
      end

      it 'returns bad request when no update parameters provided' do
        patch("/api/v1/users/#{user.id}",
              params: {}.to_json,
              headers:)

        expect(response).to have_http_status(:bad_request)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('No update parameters provided')
      end
    end

    context 'when not authenticated' do
      it 'returns unauthorized status' do
        patch "/api/v1/users/#{user.id}",
              params: { username: 'newusername' }.to_json

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
