# spec/requests/tokens_controller_spec.rb
require 'rails_helper'

RSpec.describe TokensController, type: :request do
  describe 'POST /users/tokens/sign_up' do
    let(:valid_attributes) do
      {
        user: {
          email: '<EMAIL>',
          password: 'password123',
          password_confirmation: 'password123',
          username: 'testuser'
        }
      }
    end

    context 'with valid parameters' do
      it 'creates a new user and returns success response' do
        expect {
          post '/users/tokens/sign_up', params: valid_attributes, as: :json
        }.to change(User, :count).by(1)

        expect(response).to have_http_status(:created)
        
        json_response = JSON.parse(response.body)
        expect(json_response['status']).to eq('success')
        expect(json_response['data']['email']).to eq('<EMAIL>')
        expect(json_response['data']['username']).to eq('testuser')
        expect(json_response['token']).to be_present
        expect(json_response['message']).to include('check your email')
      end

      it 'sends a confirmation email' do
        expect {
          post '/users/tokens/sign_up', params: valid_attributes, as: :json
        }.to change { ActionMailer::Base.deliveries.count }.by(1)
        
        email = ActionMailer::Base.deliveries.last
        expect(email.to).to include('<EMAIL>')
        expect(email.subject).to include('Confirm your Brag Rights account')
      end
    end

    context 'with invalid parameters' do
      it 'returns error when email is already taken' do
        # Create a user with the same email first
        User.create!(
          email: '<EMAIL>',
          password: 'password123',
          password_confirmation: 'password123',
          username: 'existinguser'
        )

        expect {
          post '/users/tokens/sign_up', params: valid_attributes, as: :json
        }.not_to change(User, :count)

        expect(response).to have_http_status(:unprocessable_entity)
        
        json_response = JSON.parse(response.body)
        expect(json_response['status']).to eq('error')
        expect(json_response['message']).to eq('Validation failed')
        expect(json_response['errors']).to include('Email has already been taken')
      end

      it 'returns error when username is already taken' do
        # Create a user with the same username first
        User.create!(
          email: '<EMAIL>',
          password: 'password123',
          password_confirmation: 'password123',
          username: 'testuser'
        )

        expect {
          post '/users/tokens/sign_up', params: valid_attributes, as: :json
        }.not_to change(User, :count)

        expect(response).to have_http_status(:unprocessable_entity)
        
        json_response = JSON.parse(response.body)
        expect(json_response['status']).to eq('error')
        expect(json_response['errors']).to include('Username has already been taken')
      end

      it 'returns error when password is too short' do
        short_password_attributes = {
          user: {
            email: '<EMAIL>',
            password: 'pass',
            password_confirmation: 'pass',
            username: 'testuser'
          }
        }

        post '/users/tokens/sign_up', params: short_password_attributes, as: :json

        expect(response).to have_http_status(:unprocessable_entity)
        
        json_response = JSON.parse(response.body)
        expect(json_response['status']).to eq('error')
        expect(json_response['errors']).to include(/Password is too short/)
      end
    end
  end
end