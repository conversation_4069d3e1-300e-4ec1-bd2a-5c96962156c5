require 'rails_helper'

RSpec.describe 'Competitions API', type: :request do
  let(:user) { create(:user, username: 'test', email: '<EMAIL>', password: 'password') }
  let(:devise_api_token) { create(:devise_api_token, resource_owner: user) }

  describe 'GET /api/v1/competitions' do
    before do
      # @competitions = [
      #   {
      #     code: 'PL',
      #     emblem: 'http://www.premierleague.com/assets/img/elements/pl-logo.png',
      #     name: 'Premier League',
      #     cid: '2021'
      #   }, {
      #     code: 'BD',
      #     emblem: 'http://www.ksi.com/assets/img/elements/pl-logo.png',
      #     name: '<PERSON><PERSON> deildin',
      #     cid: '2137'
      #   }
      # ]
      # @competitions.each do |competition|
      #   FactoryBot.create(:competition, competition)
      # end
      populate_database

      get api_v1_competitions_path, headers: authentication_headers_for(user, devise_api_token)
    end

    it 'returns all competitions' do
      expect(json['competitions'].size).to eq(1)
    end

    it 'returns status code 200' do
      expect(response).to have_http_status(:success)
    end

    it 'returns competition data in expected format' do
      expect(json['competitions'][0].keys).to include('code', 'emblem_public_id', 'name')
    end
  end
end
