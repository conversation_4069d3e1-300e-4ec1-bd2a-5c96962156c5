require 'rails_helper'

RSpec.describe 'PredictableMatches API', type: :request do
  let(:user) { create(:user, username: 'test', email: '<EMAIL>', password: 'password') }
  let(:admin_user) { create(:user, username: 'admin', email: '<EMAIL>', password: 'password', admin: true) }
  let(:devise_api_token) { create(:devise_api_token, resource_owner: user) }
  let(:admin_token) { create(:devise_api_token, resource_owner: admin_user) }

  before do
    populate_database
    @competition = Competition.first
    @competition_code = @competition.code
  end

  describe 'GET /api/v1/predictable_matches' do
    context 'when authenticated' do
      it 'returns a list of matches that can be predicted' do
        # Mock the service to return matches
        allow(PredictableMatchesService).to receive(:fetch_matches)
          .with(competition_code: @competition_code, season_id: nil)
          .and_return(Match.limit(3).to_a)

        get api_v1_predictable_matches_path,
            params: { competitionCode: @competition_code },
            headers: authentication_headers_for(user, devise_api_token)

        expect(response).to have_http_status(:success)
        expect(parsed_body.matches).not_to be_empty
        expect(parsed_body.message).to eq ['Predictable matches found']
      end

      it 'returns empty array when no predictable matches exist' do
        allow(PredictableMatchesService).to receive(:fetch_matches)
          .with(competition_code: @competition_code, season_id: nil)
          .and_return([])

        get api_v1_predictable_matches_path,
            params: { competitionCode: @competition_code },
            headers: authentication_headers_for(user, devise_api_token)

        expect(response).to have_http_status(:success)
        expect(parsed_body.matches).to be_empty
      end

      it 'fetches matches for a specific season when seasonId is provided' do
        season_id = 123
        # Mock the service to return matches for the specific season
        allow(PredictableMatchesService).to receive(:fetch_matches)
          .with(competition_code: @competition_code, season_id: season_id.to_s)
          .and_return(Match.limit(2).to_a)

        get api_v1_predictable_matches_path,
            params: { competitionCode: @competition_code, seasonId: season_id },
            headers: authentication_headers_for(user, devise_api_token)

        expect(response).to have_http_status(:success)
        expect(parsed_body.matches).not_to be_empty
        expect(parsed_body.season_id).to eq(season_id.to_s)
      end

      it 'returns season_over response with the correct season_id when season is over' do
        season_id = 456
        # Create a result array with the season_over flag
        result = []
        result.instance_variable_set(:@season_over, true)

        allow(PredictableMatchesService).to receive(:fetch_matches)
          .with(competition_code: @competition_code, season_id: season_id.to_s)
          .and_return(result)

        get api_v1_predictable_matches_path,
            params: { competitionCode: @competition_code, seasonId: season_id },
            headers: authentication_headers_for(user, devise_api_token)

        expect(response).to have_http_status(:success)
        expect(parsed_body.matches).to be_empty
        expect(parsed_body.season_over).to be true
        expect(parsed_body.season_id).to eq(season_id.to_s)
        expect(parsed_body.message.first).to include('Season is over')
      end
    end

    context 'when not authenticated' do
      it 'returns unauthorized error' do
        get api_v1_predictable_matches_path,
            params: { competitionCode: @competition_code }

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'GET /api/v1/predictable_matches/refresh' do
    context 'when authenticated as admin' do
      before do
        allow(TodaysMatchesUpdateJob).to receive(:perform_later).and_return(true)
      end

      it 'schedules a job to refresh matches' do
        get refresh_api_v1_predictable_matches_path,
            params: { competitionCode: @competition_code },
            headers: authentication_headers_for(admin_user, admin_token)

        expect(response).to have_http_status(:success)
        expect(TodaysMatchesUpdateJob).to have_received(:perform_later).with(@competition_code)
        expect(parsed_body.message).to eq ['Match refresh scheduled successfully']
      end
    end

    context 'when authenticated as regular user' do
      it 'returns forbidden error' do
        allow_any_instance_of(Api::V1::PredictableMatchesController).to receive(:current_user).and_return(user)

        get refresh_api_v1_predictable_matches_path,
            params: { competitionCode: @competition_code },
            headers: authentication_headers_for(user, devise_api_token)

        expect(response).to have_http_status(:forbidden)
        expect(parsed_body.message).to include('Not authorized to refresh matches manually')
      end
    end
  end
end
