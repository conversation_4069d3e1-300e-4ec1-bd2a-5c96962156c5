require 'rails_helper'

RSpec.describe 'FavoriteTeams API', type: :request do
  let(:user) { create(:user, username: 'test', email: '<EMAIL>', password: 'password') }
  let(:other_user) { create(:user, username: 'other', email: '<EMAIL>', password: 'password') }
  let(:devise_api_token) { create(:devise_api_token, resource_owner: user) }
  let(:other_user_token) { create(:devise_api_token, resource_owner: other_user) }

  before do
    populate_database
    @competition = Competition.first
    @team = @competition.current_season.teams.first
  end

  describe 'POST /api/v1/favorite_teams' do
    it 'creates a favorite team for a user' do
      post api_v1_favorite_teams_path,
           headers: authentication_headers_for(user, devise_api_token),
           params: { team_id: @team.id, competition_id: @competition.id }, as: :json

      expect(response).to have_http_status(:created)
      expect(user.favorite_teams.count).to eq(1)
    end

    it 'updates existing favorite team for a competition' do
      # Create initial favorite team
      favorite = FavoriteTeam.create(user:, team: @team, competition: @competition)

      # Get another team from the same competition
      another_team = @competition.current_season.teams.second

      # Update the favorite team
      post api_v1_favorite_teams_path,
           headers: authentication_headers_for(user, devise_api_token),
           params: { team_id: another_team.id, competition_id: @competition.id }, as: :json

      expect(response).to have_http_status(:created)
      expect(user.favorite_teams.count).to eq(1)
      expect(user.favorite_teams.first.team_id).to eq(another_team.id)
    end

    it 'requires authentication' do
      post api_v1_favorite_teams_path,
           params: { team_id: @team.id, competition_id: @competition.id }

      expect(response).to have_http_status(:unauthorized)
    end
  end

  describe 'GET /api/v1/favorite_teams' do
    before do
      # Create favorite teams for both test users
      FavoriteTeam.create(user:, team: @team, competition: @competition)

      # Create another competition and favorite for first user
      @competition2 = Competition.second || create(:competition, code: 'TEST2')
      @team2 = @competition2.current_season&.teams&.first || create(:team, tla: 'TEST2', name: 'Test Team 2',
                                                                           short_name: 'TT2', venue: 'Test Venue 2',
                                                                           crest_public_id: 'test2')
      if @competition2.current_season.nil?
        @season = create(:season, competition: @competition2)
        @competition2.update(current_season: @season)
        @season.teams << @team2
      end

      FavoriteTeam.create(user:, team: @team2, competition: @competition2)
    end

    it 'returns all favorite teams for the authenticated user' do
      get api_v1_favorite_teams_path,
          headers: authentication_headers_for(user, devise_api_token)

      expect(response).to have_http_status(:success)
      expect(json.size).to eq(2)
      expect(json.map { |f| f['team']['id'] }).to include(@team.id, @team2.id)
    end

    it 'filters by competition ID when provided' do
      get favorite_team_api_v1_competition_path(@competition.id),
          headers: authentication_headers_for(user, devise_api_token)

      expect(response).to have_http_status(:success)
      # should only be one favorite team
      expect(json['team']['id']).to eq(@team.id)
    end

    it 'returns empty array when user has no favorites' do
      get api_v1_favorite_teams_path,
          headers: authentication_headers_for(other_user, other_user_token)

      expect(response).to have_http_status(:success)
      expect(json).to be_empty
    end

    it 'requires authentication' do
      get api_v1_favorite_teams_path

      expect(response).to have_http_status(:unauthorized)
    end
  end

  describe 'GET /api/v1/competitions/:competition_id/favorite_team' do
    before do
      FavoriteTeam.create(user:, team: @team, competition: @competition)
    end

    it 'returns the favorite team for a specific competition' do
      get "/api/v1/competitions/#{@competition.id}/favorite_team",
          headers: authentication_headers_for(user, devise_api_token)

      expect(response).to have_http_status(:success)
      expect(json['team']['id']).to eq(@team.id)
      expect(json['competition']['id']).to eq(@competition.id)
    end

    it 'returns nil when no favorite exists for the competition' do
      # Use a competition that the user doesn't have a favorite for
      @competition2 = create(:competition, code: 'TEST3')

      get "/api/v1/competitions/#{@competition2.id}/favorite_team",
          headers: authentication_headers_for(user, devise_api_token)

      expect(response).to have_http_status(:success)
      expect(json['favorite_team']).to be_nil
    end

    it 'requires authentication' do
      get "/api/v1/competitions/#{@competition.id}/favorite_team"

      expect(response).to have_http_status(:unauthorized)
    end
  end

  describe 'DELETE /api/v1/favorite_teams/:id' do
    before do
      @favorite = FavoriteTeam.create(user:, team: @team, competition: @competition)
    end

    it 'removes a favorite team' do
      expect do
        delete api_v1_favorite_team_path(@favorite),
               headers: authentication_headers_for(user, devise_api_token)
      end.to change(FavoriteTeam, :count).by(-1)

      expect(response).to have_http_status(:success)
    end

    it 'cannot remove another user\'s favorite team' do
      # Create a favorite for another user
      other_favorite = FavoriteTeam.create(user: other_user, team: @team, competition: @competition)

      expect do
        delete api_v1_favorite_team_path(other_favorite),
               headers: authentication_headers_for(user, devise_api_token)
      end.not_to change(FavoriteTeam, :count)

      expect(response).to have_http_status(:unauthorized)
    end

    it 'returns 404 when favorite does not exist' do
      delete api_v1_favorite_team_path(9999),
             headers: authentication_headers_for(user, devise_api_token)

      expect(response).to have_http_status(:not_found)
    end

    it 'requires authentication' do
      delete api_v1_favorite_team_path(@favorite)

      expect(response).to have_http_status(:unauthorized)
    end
  end
end
