require 'rails_helper'

RSpec.describe 'Season Transition Leagues', type: :request do
  let(:area) { create(:area) }
  let(:competition) { create(:competition, area:) }
  let(:old_season) { create(:season, competition:, start_date: 1.year.ago, end_date: 1.day.ago) }
  let(:new_season) { create(:season, competition:, start_date: Date.today, end_date: 1.year.from_now) }
  let(:admin_user) { create(:user, admin: true) }
  let(:admin_devise_api_token) { create(:devise_api_token, resource_owner: admin_user) }
  let(:regular_user) { create(:user) }
  let(:regular_devise_api_token) { create(:devise_api_token, resource_owner: regular_user) }

  before do
    competition.update!(current_season_id: new_season.id)
    create_list(:league, 3, competition_id: competition.id, season_id: old_season.id)
  end

  describe 'POST /api/v1/seasons/:id/transition_leagues' do
    context 'when user is an admin' do
      it 'transitions leagues to the new season and archives old season leagues' do
        # Mock the LeagueArchiveService
        archive_service = instance_double(LeagueArchiveService)
        expect(LeagueArchiveService).to receive(:new).with(old_season).and_return(archive_service)
        expect(archive_service).to receive(:archive_all_leagues).and_return(3)

        post transition_leagues_api_v1_season_path(new_season),
             headers: authentication_headers_for(admin_user, admin_devise_api_token)

        expect(response).to have_http_status(:success)
        expect(json_response['message'].first).to include('leagues transitioned to new season successfully')
        expect(json_response['message'].first).to include('Old season leagues have been archived')
        expect(League.where(season_id: new_season.id).count).to eq(3)
        expect(League.where(season_id: old_season.id).count).to eq(0)
      end

      it 'returns a message when leagues are already using the current season' do
        # Update leagues to use the new season
        League.update_all(season_id: new_season.id)

        post transition_leagues_api_v1_season_path(new_season),
             headers: authentication_headers_for(admin_user, admin_devise_api_token)

        expect(response).to have_http_status(:success)
        expect(json_response['message'].first).to eq('Leagues are already using the current season')
      end

      it 'returns an error when the season is not the current season' do
        post transition_leagues_api_v1_season_path(old_season),
             headers: authentication_headers_for(admin_user, admin_devise_api_token)

        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['errors'].first).to eq('This is not the current season for this competition')
      end
    end

    context 'when user is not an admin' do
      it 'returns unauthorized' do
        post transition_leagues_api_v1_season_path(new_season),
             headers: authentication_headers_for(regular_user, regular_devise_api_token)

        expect(response).to have_http_status(:unauthorized)
        expect(json_response['errors'].first).to eq('Only admin can perform this action')
      end
    end
  end
end
