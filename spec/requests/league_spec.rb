require 'rails_helper'

RSpec.describe 'Leagues', type: :request do
  let(:user) { create(:user, username: 'test', email: '<EMAIL>', password: 'password') }
  let(:devise_api_token) { create(:devise_api_token, resource_owner: user) }
  before do
    populate_database
    @season_id = Season.first.id
    @competition_id = Competition.first.id
  end

  describe 'POST /create' do
    it 'returns http success' do
      post api_v1_leagues_path, headers: authentication_headers_for(user, devise_api_token),
                                params: { league: { name: 'test',
                                                    competition_id: @competition_id,
                                                    open: true,
                                                    season_id: @season_id } }, as: :json

      expect(response).to have_http_status(:success)
    end
  end
end
