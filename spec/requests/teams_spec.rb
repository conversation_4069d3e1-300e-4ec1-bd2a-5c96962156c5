require 'rails_helper'

RSpec.describe 'Api::V1::Teams', type: :request do
  let(:area) { create(:area) }
  let(:team) { create(:team, :valur, area:) }
  let(:valid_team_params) do
    {
      team: {
        name: 'New Team',
        short_name: 'NT',
        tla: 'NTM',
        venue: 'New Stadium',
        crest_public_id: 'teams/new-team',
        external_service_id: 1, # Add external_service_id
        source: 'football_data', # Add source
        area_id: area.id # Include area_id
      }
    }
  end

  describe 'GET /api/v1/teams' do
    before do
      3.times do |n|
        create(:team, :valur, area:,
                              name: "Team #{n}",
                              short_name: "T#{n}")
      end
    end

    it 'returns a list of teams' do
      get '/api/v1/teams'

      expect(response).to have_http_status(:success)
      expect(json_response.length).to eq(3)
    end
  end

  describe 'GET /api/v1/teams/:id' do
    context 'when the team exists' do
      it 'returns the requested team' do
        get "/api/v1/teams/#{team.id}"

        expect(response).to have_http_status(:success)
        expect(json_response['name']).to eq(team.name)
        expect(json_response['short_name']).to eq(team.short_name)
        expect(json_response['venue']).to eq(team.venue)
      end
    end

    context 'when the team does not exist' do
      it 'returns a not found error' do
        get '/api/v1/teams/0'

        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe 'POST /api/v1/teams' do
    context 'with valid parameters' do
      it 'creates a new team' do
        expect do
          post '/api/v1/teams', params: valid_team_params
        end.to change(Team, :count).by(1)

        expect(response).to have_http_status(:created)
        expect(json_response['name']).to eq('New Team')
        expect(json_response['short_name']).to eq('NT')
        expect(json_response['venue']).to eq('New Stadium')
      end
    end

    context 'with invalid parameters' do
      it 'returns unprocessable entity status' do
        post '/api/v1/teams', params: {
          team: { name: '', area_id: area.id } # Include area_id
        }

        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['errors']).to include("Name can't be blank")
      end
    end

    context 'when area does not exist' do
      it 'returns not found status' do
        post '/api/v1/teams', params: {
          team: valid_team_params[:team].merge(area_id: 0) # Invalid area_id
        }

        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
