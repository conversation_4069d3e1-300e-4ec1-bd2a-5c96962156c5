require 'rails_helper'

RSpec.describe Match, type: :model do
  describe 'Validations' do
    let(:area) { create(:area) }
    let(:competition) do
      Competition.create!(
        name: 'Test Competition',
        competition_type: 'LEAGUE',
        area:,
        code: 'TC',
        emblem_public_id: 'test_emblem',
        gender: 'male',
        external_service_id: 1, # Add external_service_id
        source: 'football_data' # Add source
      )
    end

    it 'is valid with valid attributes' do
      area = Area.create!(name: 'Test Area', code: 'TA')
      competition = Competition.create!(name: 'Test Competition', competition_type: 'LEAGUE', area:, code: 'TC',
                                        emblem_public_id: 'test_emblem', gender: 'male',
                                        external_service_id: 1, # Add external_service_id
                                        source: 'football_data')
      season = Season.create!(start_date: Date.today, end_date: Date.today + 1.year, competition:,
                              current_matchday: 1, external_service_id: 1, # Add external_service_id
                              source: 'football_data')

      home_team = Team.create!(name: 'Home Team', area:, short_name: 'HT', tla: 'HTA', venue: 'Home Venue',
                               crest_public_id: 'home_crest',
                               external_service_id: 1, # Add external_service_id
                               source: 'football_data') # Add source
      away_team = Team.create!(name: 'Away Team', area:, short_name: 'AT', tla: 'ATA', venue: 'Away Venue',
                               crest_public_id: 'away_crest',
                               external_service_id: 2, # Add external_service_id
                               source: 'football_data') # Add source
      match = Match.new(season:, home_team:, away_team:, status: 'SCHEDULED',
                        external_service_id: 1, # Add external_service_id
                        source: 'football_data') # Add source
      expect(match).to be_valid
    end

    it 'is invalid without a season' do
      match = Match.new(home_team: Team.new, away_team: Team.new, status: 'SCHEDULED',
                        external_service_id: 1, # Add external_service_id
                        source: 'football_data') # Add source
      expect(match).not_to be_valid
    end

    it 'is invalid without home and away teams for certain statuses' do
      area = Area.create!(name: 'Test Area', code: 'TA')
      competition = Competition.create!(name: 'Test Competition', competition_type: 'LEAGUE', area:, code: 'TC',
                                        emblem_public_id: 'test_emblem', gender: 'male',
                                        external_service_id: 1, # Add external_service_id
                                        source: 'football_data') # Add source
      season = Season.create!(start_date: Date.today, end_date: Date.today + 1.year,
                              competition:, current_matchday: 1,
                              external_service_id: 1, # Add external_service_id
                              source: 'football_data') # Add source
      match = Match.new(season:, status: 'LIVE')
      expect(match).not_to be_valid
    end
  end

  describe 'Associations' do
    it { is_expected.to belong_to(:season) }
    it { is_expected.to have_one(:score) }
    it { is_expected.to have_many(:match_predictions) }
    it { is_expected.to belong_to(:home_team).class_name('Team').optional }
    it { is_expected.to belong_to(:away_team).class_name('Team').optional }
  end

  describe 'Instance Methods' do
    describe '#finished?' do
      it 'returns true if the match status is FINISHED or POSTPONED' do
        match = Match.new(status: 'FINISHED')
        expect(match.finished?).to be true

        match.status = 'POSTPONED'
        expect(match.finished?).to be true
      end

      it 'returns false for other statuses' do
        match = Match.new(status: 'SCHEDULED')
        expect(match.finished?).to be false
      end
    end
  end
end
