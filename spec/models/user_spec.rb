require 'rails_helper'

RSpec.describe User, type: :model do
  let(:user) { create(:user) }
  let(:competition) { create(:competition) }
  let(:team) { create(:team, :valur) }

  describe 'competition-specific point calculations' do
    before do
      @user = User.create!(email: '<EMAIL>', username: 'testuser', password: 'password')

      # Create two competitions
      @area = Area.create!(name: 'Test Area', code: 'TA')
      @competition1 = Competition.create!(
        name: 'Competition 1',
        competition_type: 'LEAGUE',
        area: @area,
        code: 'C1',
        emblem_public_id: 'test_emblem1',
        gender: 'male',
        external_service_id: 1,
        source: 'football_data'
      )

      @competition2 = Competition.create!(
        name: 'Competition 2',
        competition_type: 'LEAGUE',
        area: @area,
        code: 'C2',
        emblem_public_id: 'test_emblem2',
        gender: 'male',
        external_service_id: 2,
        source: 'football_data'
      )

      # Create seasons for each competition
      @season1 = Season.create!(
        start_date: Date.today,
        end_date: Date.today + 1.year,
        competition: @competition1,
        external_service_id: 1,
        current_matchday: 1
      )

      @season2 = Season.create!(
        start_date: Date.today,
        end_date: Date.today + 1.year,
        competition: @competition2,
        external_service_id: 2,
        current_matchday: 1
      )

      # Create round predictions for each competition
      @rp1 = RoundPrediction.create!(
        user: @user,
        matchday: 1,
        season_id: @season1.id,
        competition_id: @competition1.id,
        stage: 'REGULAR_SEASON'
      )

      @rp2 = RoundPrediction.create!(
        user: @user,
        matchday: 1,
        season_id: @season2.id,
        competition_id: @competition2.id,
        stage: 'REGULAR_SEASON'
      )

      # Create teams
      @team1 = create(:team, name: 'Team 1', external_service_id: 1, area: @area)
      @team2 = create(:team, name: 'Team 2', external_service_id: 2, area: @area)
      @team3 = create(:team, name: 'Team 3', external_service_id: 3, area: @area)
      @team4 = create(:team, name: 'Team 4', external_service_id: 4, area: @area)

      # Create matches for competition 1
      @match1 = create(:match,
                       home_team: @team1,
                       away_team: @team2,
                       season: @season1,
                       matchday: 1,
                       status: 'FINISHED',
                       stage: 'REGULAR_SEASON',
                       utc_date: Time.now)

      @match2 = create(:match,
                       home_team: @team3,
                       away_team: @team4,
                       season: @season1,
                       matchday: 1,
                       status: 'FINISHED',
                       stage: 'REGULAR_SEASON',
                       utc_date: Time.now)

      # Create matches for competition 2
      @match3 = create(:match,
                       home_team: @team1,
                       away_team: @team2,
                       season: @season2,
                       matchday: 1,
                       status: 'FINISHED',
                       stage: 'REGULAR_SEASON',
                       utc_date: Time.now)

      @match4 = create(:match,
                       home_team: @team3,
                       away_team: @team4,
                       season: @season2,
                       matchday: 1,
                       status: 'FINISHED',
                       stage: 'REGULAR_SEASON',
                       utc_date: Time.now)

      # Create scores for the matches
      @score1 = Score.create!(match: @match1, fullTimeHome: 2, fullTimeAway: 1)
      @score2 = Score.create!(match: @match2, fullTimeHome: 0, fullTimeAway: 0)
      @score3 = Score.create!(match: @match3, fullTimeHome: 3, fullTimeAway: 0)
      @score4 = Score.create!(match: @match4, fullTimeHome: 1, fullTimeAway: 2)

      # Create match predictions for competition 1
      @mp1 = @rp1.match_predictions.create!(
        match: @match1,
        home_score: 2,
        away_score: 1,
        user: @user
      )

      @mp2 = @rp1.match_predictions.create!(
        match: @match2,
        home_score: 1,
        away_score: 0,
        user: @user
      )

      # Create match predictions for competition 2
      @mp3 = @rp2.match_predictions.create!(
        match: @match3,
        home_score: 2,
        away_score: 0,
        user: @user
      )

      @mp4 = @rp2.match_predictions.create!(
        match: @match4,
        home_score: 0,
        away_score: 2,
        user: @user
      )

      # Calculate points for all match predictions
      @mp1.calculate_points
      @mp2.calculate_points
      @mp3.calculate_points
      @mp4.calculate_points
    end

    it 'calculates points for a specific competition and season' do
      # Get the actual values from the test data
      comp1_points = @user.points_for_competition_season(@competition1.id, @season1.id)
      comp2_points = @user.points_for_competition_season(@competition2.id, @season2.id)
      total_points = @user.points

      # Verify that the competition-specific points add up to the total
      expect(comp1_points + comp2_points).to eq(total_points)

      # Verify that the points are calculated correctly for each competition
      expect(comp1_points).to eq(3) # Perfect prediction for match1
      expect(comp2_points).to eq(2) # Correct predictions for match3 and match4
    end

    it 'calculates correct predictions for a specific competition and season' do
      comp1_correct = @user.correct_predictions_for_competition_season(@competition1.id, @season1.id)
      comp2_correct = @user.correct_predictions_for_competition_season(@competition2.id, @season2.id)
      total_correct = @user.correct_predictions

      # Verify that the competition-specific correct predictions add up to the total
      expect(comp1_correct + comp2_correct).to eq(total_correct)

      # Verify that the correct predictions are calculated correctly for each competition
      expect(comp1_correct).to eq(0)
      expect(comp2_correct).to eq(2)
    end

    it 'calculates incorrect predictions for a specific competition and season' do
      comp1_incorrect = @user.incorrect_predictions_for_competition_season(@competition1.id, @season1.id)
      comp2_incorrect = @user.incorrect_predictions_for_competition_season(@competition2.id, @season2.id)
      total_incorrect = @user.incorrect_predictions

      # Verify that the competition-specific incorrect predictions add up to the total
      expect(comp1_incorrect + comp2_incorrect).to eq(total_incorrect)

      # Verify that the incorrect predictions are calculated correctly for each competition
      expect(comp1_incorrect).to eq(1)
      expect(comp2_incorrect).to eq(0)
    end

    it 'calculates perfect predictions for a specific competition and season' do
      comp1_perfect = @user.perfect_predictions_for_competition_season(@competition1.id, @season1.id)
      comp2_perfect = @user.perfect_predictions_for_competition_season(@competition2.id, @season2.id)
      total_perfect = @user.perfect_predictions

      # Verify that the competition-specific perfect predictions add up to the total
      expect(comp1_perfect + comp2_perfect).to eq(total_perfect)

      # Verify that the perfect predictions are calculated correctly for each competition
      expect(comp1_perfect).to eq(1)
      expect(comp2_perfect).to eq(0)
    end
  end

  describe 'favorite teams methods' do
    describe '#favorite_team_for_competition' do
      it 'returns nil when no favorite team exists for the competition' do
        expect(user.favorite_team_for_competition(competition.id)).to be_nil
      end

      it 'returns the favorite team for the competition' do
        FavoriteTeam.create(user:, team:, competition:)
        expect(user.favorite_team_for_competition(competition.id)).to eq(team)
      end
    end

    describe '#set_favorite_team' do
      it 'sets a new favorite team for a competition' do
        expect do
          user.set_favorite_team(competition.id, team.id)
        end.to change(FavoriteTeam, :count).by(1)

        favorite = user.favorite_teams.first
        expect(favorite.team).to eq(team)
        expect(favorite.competition).to eq(competition)
      end

      it 'updates existing favorite team for a competition' do
        FavoriteTeam.create(user:, team:, competition:)
        new_team = create(:team, :kr)

        expect do
          user.set_favorite_team(competition.id, new_team.id)
        end.not_to change(FavoriteTeam, :count)

        favorite = user.favorite_teams.first
        expect(favorite.team).to eq(new_team)
      end
    end

    describe '#remove_favorite_team' do
      it 'removes the favorite team for a competition' do
        FavoriteTeam.create(user:, team:, competition:)

        expect do
          user.remove_favorite_team(competition.id)
        end.to change(FavoriteTeam, :count).by(-1)

        expect(user.favorite_team_for_competition(competition.id)).to be_nil
      end

      it 'does nothing when no favorite team exists' do
        expect do
          user.remove_favorite_team(competition.id)
        end.not_to change(FavoriteTeam, :count)
      end
    end
  end
end
