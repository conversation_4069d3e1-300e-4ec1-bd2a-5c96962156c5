require 'rails_helper'

RSpec.describe Area, type: :model do
  describe 'validations' do
    it 'is valid with valid attributes' do
      area = Area.new(name: 'Test Area', code: 'TA')
      expect(area).to be_valid
    end

    it 'requires a name' do
      area = Area.new(code: 'TA')
      expect(area).not_to be_valid
      expect(area.errors[:name]).to include("can't be blank")
    end

    it 'requires a code' do
      area = Area.new(name: 'Test Area')
      expect(area).not_to be_valid
      expect(area.errors[:code]).to include("can't be blank")
    end

    it 'enforces unique names' do
      Area.create!(name: 'Test Area', code: 'TA')
      duplicate = Area.new(name: 'Test Area', code: 'TA2')
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:name]).to include('has already been taken')
    end

    it 'enforces unique codes' do
      Area.create!(name: 'Test Area', code: 'TA')
      duplicate = Area.new(name: 'Test Area 2', code: 'TA')
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:code]).to include('has already been taken')
    end
  end

  describe 'associations' do
    it { should have_many(:competitions).dependent(:destroy) }
    it { should have_many(:teams).dependent(:destroy) }
  end
end
