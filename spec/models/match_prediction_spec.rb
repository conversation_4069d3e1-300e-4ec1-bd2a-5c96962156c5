# spec/models/match_prediction_spec.rb
require 'rails_helper'

# rubocop:disable Metrics/BlockLength
RSpec.describe MatchPrediction, type: :model do
  describe '#calculate_points' do # rubocop:disable Metrics/BlockLength
    before do
      area = Area.create!(name: 'Test Area', code: 'TA')
      competition = Competition.create!(name: 'Test Competition', competition_type: 'LEAGUE', area:, code: 'TC',
                                        emblem_public_id: 'test_emblem', gender: 'male',
                                        external_service_id: 1, source: 'football_data')
      @user = User.create!(email: '<EMAIL>', username: 'user', password: 'password')
      season = Season.create!(start_date: Date.today, end_date: Date.today + 1.year, competition:,
                              current_matchday: 1,
                              external_service_id: 1)
      home_team = Team.create!(name: 'Home Team', area:, short_name: 'HT', tla: 'HTA', venue: 'Home Venue',
                               crest_public_id: 'home_crest',
                               external_service_id: 1)
      away_team = Team.create!(name: 'Away Team', area:, short_name: 'AT', tla: 'ATA', venue: 'Away Venue',
                               crest_public_id: 'away_crest',
                               external_service_id: 2)
      @match = Match.create!(season:, status: 'SCHEDULED', matchday: 4, home_team:,
                             away_team:,
                             external_service_id: 1)
    end

    it '#calculate_points awards correct points for perfect prediction' do
      round_prediction = RoundPrediction.create!(user: @user, matchday: 4, season_id: @match.season.id,
                                                 competition_id: @match.season.competition.id, stage: 'REGULAR_SEASON')
      match_prediction = round_prediction.match_predictions.create(match: @match, away_score: 1, home_score: 1)
      @match.update!(status: 'FINISHED', score: Score.create!(match: @match, fullTimeAway: 1, fullTimeHome: 1),
                     external_service_id: 1)

      expect(match_prediction.points).to be_nil

      match_prediction.calculate_points

      expect(match_prediction.points).to eq(3)
    end

    # Update test to use different scores that still represent a correct result
    it 'awards correct points for correct result' do
      round_prediction = RoundPrediction.create!(user: @user, matchday: 4, season_id: @match.season.id,
                                                 competition_id: @match.season.competition.id, stage: 'REGULAR_SEASON')
      match_prediction = round_prediction.match_predictions.create(match: @match, away_score: 1, home_score: 2)
      @match.update!(status: 'FINISHED', score: Score.create!(match: @match, fullTimeAway: 0, fullTimeHome: 3))

      expect(match_prediction.points).to be_nil

      match_prediction.calculate_points

      expect(match_prediction.points).to eq(1)
    end

    it 'does not change points for incorrect prediction' do
      round_prediction = RoundPrediction.create!(user: @user, matchday: 4, season_id: @match.season.id,
                                                 competition_id: @match.season.competition.id, stage: 'REGULAR_SEASON')
      match_prediction = round_prediction.match_predictions.create(match: @match, away_score: 1, home_score: 1)
      @match.update!(status: 'FINISHED', score: Score.create!(match: @match, fullTimeAway: 2, fullTimeHome: 1))

      expect(match_prediction.points).to be_nil

      match_prediction.calculate_points

      expect(match_prediction.points).to eq(0)
    end

    # it 'does not change points for match with status other than FINISHED' do
    #   score = nil
    #   match_prediction = create(:match_prediction, match:, user:)

    #   expect { match_prediction.calculate_points }.not_to(change { user.reload.points })
    # end
  end
end
# rubocop:enable Metrics/BlockLength
