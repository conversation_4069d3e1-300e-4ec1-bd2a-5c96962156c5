require 'rails_helper'

RSpec.describe FavoriteTeam, type: :model do
  let(:user) { create(:user) }
  let(:competition) { create(:competition) }
  let(:team) { create(:team, :valur) }

  describe 'validations' do
    it 'allows a user to have one favorite team per competition' do
      favorite = FavoriteTeam.create(user:, team:, competition:)
      expect(favorite).to be_valid
    end

    it 'prevents a user from having multiple favorite teams in the same competition' do
      FavoriteTeam.create(user:, team:, competition:)

      another_team = create(:team, :kr)
      duplicate = FavoriteTeam.new(user:, team: another_team, competition:)

      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:user_id]).to include('can only have one favorite team per competition')
    end
  end

  describe 'associations' do
    it 'belongs to a user' do
      expect(FavoriteTeam.reflect_on_association(:user).macro).to eq(:belongs_to)
    end

    it 'belongs to a team' do
      expect(FavoriteTeam.reflect_on_association(:team).macro).to eq(:belongs_to)
    end

    it 'belongs to a competition' do
      expect(FavoriteTeam.reflect_on_association(:competition).macro).to eq(:belongs_to)
    end
  end
end
