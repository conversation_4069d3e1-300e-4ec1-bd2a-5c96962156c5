require 'rails_helper'

RSpec.describe Team, type: :model do
  let(:area) { create(:area) }

  describe 'validations' do
    it 'is valid with valid attributes' do
      team = build(:team, :valur, area:)
      expect(team).to be_valid
    end

    it 'requires a name' do
      team = build(:team, :valur, area:, name: nil)
      expect(team).not_to be_valid
      expect(team.errors[:name]).to include("can't be blank")
    end

    it 'requires a short_name' do
      team = build(:team, :valur, area:, short_name: nil)
      expect(team).not_to be_valid
      expect(team.errors[:short_name]).to include("can't be blank")
    end

    it 'requires a tla' do
      team = build(:team, :valur, area:, tla: nil)
      expect(team).not_to be_valid
      expect(team.errors[:tla]).to include("can't be blank")
    end

    it 'requires a venue' do
      team = build(:team, :valur, area:, venue: nil)
      expect(team).not_to be_valid
      expect(team.errors[:venue]).to include("can't be blank")
    end

    it 'requires a crest_public_id' do
      team = build(:team, :valur, area:, crest_public_id: nil)
      expect(team).not_to be_valid
      expect(team.errors[:crest_public_id]).to include("can't be blank")
    end

    it 'enforces unique names' do
      create(:team, :valur, area:)
      duplicate = build(:team, :valur, area:)
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:name]).to include('has already been taken')
    end

    it 'enforces unique short_names' do
      create(:team, :valur, area:)
      duplicate = build(:team, :valur, area:)
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:short_name]).to include('has already been taken')
    end
  end

  describe 'associations' do
    it { should belong_to(:area) }
    it { should have_and_belong_to_many(:seasons) }
    it { should have_many(:home_matches).class_name('Match').with_foreign_key('home_team_id') }
    it { should have_many(:away_matches).class_name('Match').with_foreign_key('away_team_id') }
    it { should have_many(:favorite_teams) }
    it { should have_many(:fans).through(:favorite_teams).source(:user) }
  end

  describe '#form' do
    let(:team) { create(:team, :valur, area:) }
    let(:competition) { create(:competition, area:) }
    let(:season) { create(:season, competition:) }
    let(:opponent) { create(:team, :kr, area:) }

    before do
      season.teams << [team, opponent]
    end

    context 'when team has no matches' do
      it 'returns empty form string and matches' do
        result = team.form(season.id)
        expect(result[:string]).to eq('')
        expect(result[:matches]).to be_empty
      end
    end

    context 'when team has matches' do
      before do
        # Create 3 finished matches with different outcomes and dates
        # Oldest match - Win as home team
        match1 = create(:match, season:, home_team: team, away_team: opponent,
                                status: 'FINISHED', utc_date: 10.days.ago)
        create(:score, match: match1, fullTimeHome: 2, fullTimeAway: 1, winner: 'HOME_TEAM')

        # Middle match - Loss as away team
        match2 = create(:match, season:, home_team: opponent, away_team: team,
                                status: 'FINISHED', utc_date: 5.days.ago)
        create(:score, match: match2, fullTimeHome: 2, fullTimeAway: 0, winner: 'HOME_TEAM')

        # Most recent match - Draw as home team
        match3 = create(:match, season:, home_team: team, away_team: opponent,
                                status: 'FINISHED', utc_date: 2.days.ago)
        create(:score, match: match3, fullTimeHome: 1, fullTimeAway: 1, winner: 'DRAW')
      end
      it 'returns correct form string' do
        result = team.form(season.id)
        expect(result[:string]).to eq('WLD')
      end

      it 'returns matches with correct structure' do
        result = team.form(season.id)
        expect(result[:matches].length).to eq(3)
        expect(result[:matches].first).to include(
          :homeTeam,
          :awayTeam,
          score: {
            home: kind_of(Integer),
            away: kind_of(Integer)
          }
        )
      end

      it 'only includes FINISHED matches' do
        # Add an upcoming match
        create(:match, season:, home_team: team, away_team: opponent, status: 'SCHEDULED')

        result = team.form(season.id)
        expect(result[:matches].length).to eq(3)
      end

      it 'limits to 5 most recent matches' do
        # Add 3 more finished matches
        3.times do
          match = create(:match, season:, home_team: team, away_team: opponent, status: 'FINISHED')
          create(:score, match:, fullTimeHome: 1, fullTimeAway: 0)
        end

        result = team.form(season.id)
        expect(result[:matches].length).to eq(5)
      end
    end
  end

  describe 'club_colors' do
    let(:team) { create(:team, :valur, area:) }

    it 'initializes with empty club_colors structure' do
      expect(team.club_colors).to be_a(Hash)
      expect(team.club_colors).to have_key('home')
      expect(team.club_colors).to have_key('away')
    end

    it 'parses club colors from string format' do
      colors_string = 'Red / Blue / Yellow'
      result = team.parse_club_colors_from_string(colors_string)

      expect(result).to be_a(Hash)
      expect(result[:home][:primary]).to eq('#FF0000')
      expect(result[:home][:secondary]).to eq('#0000FF')
      expect(result[:home][:tertiary]).to eq('#FFFF00')
    end

    it 'handles partial color strings' do
      colors_string = 'Red / Blue'
      result = team.parse_club_colors_from_string(colors_string)

      expect(result[:home][:primary]).to eq('#FF0000')
      expect(result[:home][:secondary]).to eq('#0000FF')
      expect(result[:home][:tertiary]).to be_nil
    end

    it 'converts color names to hex values' do
      expect(team.color_name_to_hex('Red')).to eq('#FF0000')
      expect(team.color_name_to_hex('Blue')).to eq('#0000FF')
      expect(team.color_name_to_hex('Green')).to eq('#008000')
    end

    it 'returns the original value if color name is not recognized' do
      expect(team.color_name_to_hex('NotAColor')).to eq('NotAColor')
    end

    it 'returns home colors when requested' do
      team.club_colors = {
        home: { primary: '#FF0000', secondary: '#0000FF' },
        away: { primary: '#FFFFFF', secondary: '#000000' }
      }

      result = team.get_club_colors('home')
      expect(result[:primary]).to eq('#FF0000')
      expect(result[:secondary]).to eq('#0000FF')
    end

    it 'returns away colors when requested' do
      team.club_colors = {
        home: { primary: '#FF0000', secondary: '#0000FF' },
        away: { primary: '#FFFFFF', secondary: '#000000' }
      }

      result = team.get_club_colors('away')
      expect(result[:primary]).to eq('#FFFFFF')
      expect(result[:secondary]).to eq('#000000')
    end

    it 'returns home colors when away colors are empty' do
      team.club_colors = {
        home: { primary: '#FF0000', secondary: '#0000FF' },
        away: {}
      }

      result = team.get_club_colors('away')
      expect(result[:primary]).to eq('#FF0000')
      expect(result[:secondary]).to eq('#0000FF')
    end

    it 'returns home colors when no type is specified' do
      team.club_colors = {
        home: { primary: '#FF0000', secondary: '#0000FF' },
        away: { primary: '#FFFFFF', secondary: '#000000' }
      }

      result = team.get_club_colors
      expect(result[:primary]).to eq('#FF0000')
      expect(result[:secondary]).to eq('#0000FF')
    end

    describe '#hex_to_rgb' do
      it 'converts hex color to RGB' do
        expect(team.hex_to_rgb('#FF0000')).to eq({ r: 255, g: 0, b: 0 })
        expect(team.hex_to_rgb('#00FF00')).to eq({ r: 0, g: 255, b: 0 })
        expect(team.hex_to_rgb('#0000FF')).to eq({ r: 0, g: 0, b: 255 })
        expect(team.hex_to_rgb('#FFFFFF')).to eq({ r: 255, g: 255, b: 255 })
      end

      it 'handles hex without # prefix' do
        expect(team.hex_to_rgb('FF0000')).to eq({ r: 255, g: 0, b: 0 })
      end

      it 'returns nil for invalid hex' do
        expect(team.hex_to_rgb('not-a-hex')).to be_nil
        expect(team.hex_to_rgb('12345')).to be_nil
      end
    end

    describe '#colors_similar?' do
      it 'returns true for identical colors' do
        expect(team.colors_similar?('#FF0000', '#FF0000')).to be true
      end

      it 'returns true for similar colors' do
        # Red and slightly darker red
        expect(team.colors_similar?('#FF0000', '#E00000')).to be true
      end

      it 'returns false for different colors' do
        expect(team.colors_similar?('#FF0000', '#0000FF')).to be false
      end

      it 'returns false if either color is blank' do
        expect(team.colors_similar?('#FF0000', nil)).to be false
        expect(team.colors_similar?(nil, '#FF0000')).to be false
        expect(team.colors_similar?(nil, nil)).to be false
      end

      it 'returns false if either color is invalid' do
        expect(team.colors_similar?('#FF0000', 'not-a-color')).to be false
        expect(team.colors_similar?('not-a-color', '#FF0000')).to be false
      end
    end
  end
end
