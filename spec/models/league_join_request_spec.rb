require 'rails_helper'

RSpec.describe LeagueJoinRequest, type: :model do
  describe 'associations' do
    it { should belong_to(:user) }
    it { should belong_to(:league) }
  end

  describe 'validations' do
    let(:user) { create(:user) }
    let(:league) { create(:league, owner: user) }

    it 'is valid with valid attributes' do
      request = LeagueJoinRequest.new(user:, league:)
      expect(request).to be_valid
    end

    it 'validates uniqueness of user_id scoped to league_id' do
      LeagueJoinRequest.create!(user:, league:)
      duplicate_request = LeagueJoinRequest.new(user:, league:)

      expect(duplicate_request).not_to be_valid
      expect(duplicate_request.errors[:user_id]).to include('has already requested to join this league')
    end

    context 'when user has reached league limit' do
      let(:user_at_limit) { create(:user) }
      let(:new_league) { create(:league, owner: create(:user)) }

      before do
        # Create 5 leagues and memberships for the user
        5.times do |i|
          league = create(:league, name: "Test League #{i}", owner: create(:user))
          create(:membership, user: user_at_limit, league:)
        end
      end

      it 'prevents creating a join request' do
        request = LeagueJoinRequest.new(user: user_at_limit, league: new_league)

        expect(request).not_to be_valid
        expect(request.errors[:user]).to include('is already a member of 5 leagues')
      end
    end
  end

  describe 'enum status' do
    let(:user) { create(:user) }
    let(:league) { create(:league, owner: create(:user)) }

    it 'defaults to pending' do
      request = LeagueJoinRequest.new(user:, league:)
      expect(request.status).to eq('pending')
    end

    it 'can be set to accepted' do
      request = create(:league_join_request, user:, league:)
      request.accepted!
      expect(request.status).to eq('accepted')
    end

    it 'can be set to rejected' do
      request = create(:league_join_request, user:, league:)
      request.rejected!
      expect(request.status).to eq('rejected')
    end
  end
end
