require 'rails_helper'

RSpec.describe Season, type: :model do
  let(:area) { create(:area, name: 'Test Area', code: 'TA') }

  describe 'associations' do
    it { should belong_to(:competition) }
    it { should have_and_belong_to_many(:teams) }
    it { should have_many(:matches).dependent(:destroy) }
  end

  describe 'validations' do
    it 'is valid with valid attributes' do
      competition = create(:competition, area:)
      season = Season.new(
        start_date: Date.today,
        end_date: Date.today + 1.year,
        current_matchday: 1,
        competition:,
        source: 'football_data',
        external_service_id: 1 # Add external_service_id
      )
      expect(season).to be_valid
    end

    %i[current_matchday start_date end_date].each do |field|
      it "requires #{field}" do
        season = Season.new
        expect(season).not_to be_valid
        expect(season.errors[field]).to include("can't be blank")
      end
    end
  end

  describe 'callbacks' do
    it 'sets default stage to REGULAR_SEASON for league competition' do
      competition = create(:competition, area:, competition_type: 'LEAGUE')
      season = create(:season, competition:)
      expect(season.current_stage).to eq('REGULAR_SEASON')
    end

    it 'sets default stage to LEAGUE_STAGE for cup competition' do
      competition = create(:competition, area:, competition_type: 'CUP')
      season = create(:season, competition:)
      expect(season.current_stage).to eq('LEAGUE_STAGE')
    end
  end

  describe '#match_days' do
    let(:competition) { create(:competition, area:) }
    let(:season) { create(:season, competition:) }

    context 'for league competition' do
      before { competition.update!(competition_type: 'LEAGUE') }

      it 'returns 0 when no teams are assigned' do
        expect(season.match_days).to eq(0)
      end

      it 'calculates based on number of teams' do
        team_names = ['Team A', 'Team B', 'Team C', 'Team D', 'Team E']
        team_names.each_with_index do |name, index|
          create(:team,
                 name:,
                 short_name: "T#{index + 1}",
                 tla: "T#{index + 1}A",
                 area:,
                 venue: "#{name} Stadium",
                 crest_public_id: "#{name.downcase.gsub(' ', '_')}_crest")
        end
        season.teams = Team.all
        expect(season.match_days).to eq(20) # 5 teams * (5-1) = 20 match days
      end
    end

    context 'for cup competition' do
      before { competition.update!(competition_type: 'CUP') }

      it 'returns fixed number of match days' do
        expect(season.match_days).to eq(Season::CL_LEAGUE_STAGE_MATCHES + Season::CL_KNOCKOUT_STAGES_MATCHES)
      end
    end
  end

  describe '#update_matchday_if_complete' do
    let(:competition) { create(:competition, area:) }
    let(:season) { create(:season, competition:, current_matchday: 1) }
    let(:home_team) { create(:team, :valur, area:) }
    let(:away_team) { create(:team, :kr, area:) }

    context 'when all matches are complete' do
      before do
        match = create(:match,
                       season:,
                       matchday: 1,
                       home_team:,
                       away_team:,
                       status: 'FINISHED')
        create(:score, match:, fullTimeHome: 2, fullTimeAway: 1)
      end

      it 'increments current_matchday for league competition' do
        competition.update!(competition_type: 'LEAGUE')
        expect { season.update_matchday_if_complete }.to change { season.reload.current_matchday }.from(1).to(2)
      end
    end

    context 'when not all matches are complete' do
      before do
        create(:match,
               season:,
               matchday: 1,
               home_team:,
               away_team:,
               status: 'SCHEDULED')
      end

      it 'does not increment current_matchday' do
        expect { season.update_matchday_if_complete }.not_to(change { season.reload.current_matchday })
      end
    end
  end

  describe '#overall_matchday' do
    let(:competition) { create(:competition, area:) }
    let(:season) { create(:season, competition:, current_matchday: 5) }

    it 'returns current_matchday for league competition' do
      competition.update!(competition_type: 'LEAGUE')
      expect(season.overall_matchday).to eq(5)
    end
  end

  describe '#all_season_matches_completed?' do
    let(:competition) { create(:competition, area:) }
    let(:season) { create(:season, competition:) }
    let(:home_team) { create(:team, :valur, area:) }
    let(:away_team) { create(:team, :kr, area:) }

    it 'returns true when all matches are completed' do
      create(:match, season:, status: 'FINISHED')
      create(:match, season:, status: 'POSTPONED')
      create(:match, season:, status: 'CANCELLED')

      expect(season.all_season_matches_completed?).to be true
    end

    it 'returns false when any match is not completed' do
      create(:match, season:, status: 'FINISHED')
      create(:match, season:, status: 'SCHEDULED')

      expect(season.all_season_matches_completed?).to be false
    end
  end

  describe '#season_over?' do
    let(:competition) { create(:competition, area:) }

    it 'returns true when season is marked as completed' do
      season = create(:season, competition:, completed: true)

      expect(season.season_over?).to be true
    end

    it 'returns true when end date is passed and all matches are completed' do
      season = create(:season, competition:, start_date: 2.years.ago, end_date: 1.day.ago)
      allow(season).to receive(:all_season_matches_completed?).and_return(true)

      expect(season.season_over?).to be true
    end

    it 'returns false when end date is passed but not all matches are completed' do
      season = create(:season, competition:, start_date: 2.years.ago, end_date: 1.day.ago)
      allow(season).to receive(:all_season_matches_completed?).and_return(false)

      expect(season.season_over?).to be false
    end

    it 'returns false when end date is in the future' do
      season = create(:season, competition:, start_date: Date.today - 1.month, end_date: 1.day.from_now)

      expect(season.season_over?).to be false
    end
  end

  describe '#mark_as_completed!' do
    let(:competition) { create(:competition, area:) }
    let(:season) { create(:season, competition:, completed: false) }

    it 'marks the season as completed' do
      expect { season.mark_as_completed! }.to change { season.reload.completed }.from(false).to(true)
    end

    it 'does nothing if the season is already completed' do
      season.update!(completed: true)

      expect { season.mark_as_completed! }.not_to(change { season.reload.completed })
    end
  end

  describe '#check_if_completed' do
    let(:competition) { create(:competition, area:) }
    let(:season) { create(:season, competition:, completed: false) }

    it 'marks the season as completed if it is over and not already completed' do
      allow(season).to receive(:season_over?).and_return(true)

      expect { season.check_if_completed }.to change { season.reload.completed }.from(false).to(true)
    end

    it 'does nothing if the season is not over' do
      allow(season).to receive(:season_over?).and_return(false)

      expect { season.check_if_completed }.not_to(change { season.reload.completed })
    end

    it 'does nothing if the season is already completed' do
      season.update!(completed: true)

      expect { season.check_if_completed }.not_to(change { season.reload.completed })
    end
  end
end
