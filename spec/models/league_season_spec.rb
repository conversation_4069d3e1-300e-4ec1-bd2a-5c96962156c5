require 'rails_helper'

RSpec.describe LeagueSeason, type: :model do
  describe 'associations' do
    it { should belong_to(:league) }
    it { should belong_to(:season) }
    it { should belong_to(:user) }
  end

  describe 'validations' do
    it 'validates uniqueness of user_id scoped to league_id and season_id' do
      area = create(:area)
      competition = create(:competition, area:)
      season = create(:season, competition:)
      league = create(:league, competition_id: competition.id, season_id: season.id)
      user = create(:user)

      # Create the first record
      create(:league_season, league:, season:, user:)

      # Try to create a duplicate record
      duplicate = build(:league_season, league:, season:, user:)
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:user_id]).to include('already has a record for this league and season')
    end
  end

  describe 'default values' do
    it 'sets default values for new records' do
      league_season = LeagueSeason.new

      expect(league_season.points).to eq(0)
      expect(league_season.correct_predictions).to eq(0)
      expect(league_season.incorrect_predictions).to eq(0)
      expect(league_season.perfect_predictions).to eq(0)
    end
  end

  describe '.calculate_positions_for_league_season' do
    it 'calculates positions based on points' do
      area = create(:area)
      competition = create(:competition, area:)
      season = create(:season, competition:)
      league = create(:league, competition_id: competition.id, season_id: season.id)

      # Create users with different points
      user1 = create(:user, username: "user1_#{Time.now.to_i}")
      user2 = create(:user, username: "user2_#{Time.now.to_i}")
      user3 = create(:user, username: "user3_#{Time.now.to_i}")

      # Create league_seasons with different points
      ls1 = create(:league_season, league:, season:, user: user1, points: 10)
      ls2 = create(:league_season, league:, season:, user: user2, points: 20)
      ls3 = create(:league_season, league:, season:, user: user3, points: 15)

      # Calculate positions
      LeagueSeason.calculate_positions_for_league_season(league.id, season.id)

      # Reload records
      ls1.reload
      ls2.reload
      ls3.reload

      # Check positions
      expect(ls2.position).to eq(1) # 20 points
      expect(ls3.position).to eq(2) # 15 points
      expect(ls1.position).to eq(3) # 10 points
    end
  end
end
