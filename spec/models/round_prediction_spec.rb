# spec/models/round_prediction_spec.rb
require 'rails_helper'

RSpec.describe RoundPrediction, type: :model do # rubocop:disable Metrics/BlockLength
  before do
    area = Area.create!(name: 'Test Area', code: 'TA')
    @competition = Competition.create!(name: 'Test Competition', competition_type: 'LEAGUE', area:, code: 'TC',
                                       emblem_public_id: 'test_emblem', gender: 'male', external_service_id: 1,
                                       source: 'football_data')
    @season = Season.create!(start_date: Date.today, end_date: Date.today + 1.year, competition: @competition,
                             current_matchday: 1,
                             external_service_id: 1,
                             source: 'football_data')
    @user = User.create!(email: '<EMAIL>', username: 'user', password: 'password')
  end

  describe 'validations' do # rubocop:disable Metrics/BlockLength
    it 'allows different users to create predictions for the same matchday and season' do
      prediction1 = RoundPrediction.create!(user: @user, season_id: @season.id, competition_id: @competition.id,
                                            matchday: 1, stage: 'REGULAR_SEASON')
      prediction2 = RoundPrediction.create!(
        user: User.create!(email: '<EMAIL>', username: 'user2',
                           password: 'password'), season_id: @season.id, competition_id: @competition.id,
        matchday: 1, stage: 'REGULAR_SEASON'
      )
      expect(prediction1).to be_valid
      expect(prediction2).to be_valid
    end

    it 'prevents the same user from creating multiple predictions for the same matchday and season' do
      RoundPrediction.create!(user: @user, matchday: 1, season_id: @season.id, competition_id: @competition.id,
                              stage: 'REGULAR_SEASON')
      duplicate_prediction = RoundPrediction.new(user: @user, matchday: 1, season_id: @season.id,
                                                 competition_id: @competition.id, stage: 'REGULAR_SEASON')

      expect(duplicate_prediction).to be_invalid
      expect(duplicate_prediction.errors[:matchday]).to include('can only have one prediction per matchday and stage for each user')
    end

    it 'allows the same user to create predictions for different matchdays in the same season' do
      prediction1 = RoundPrediction.create!(user: @user, matchday: 1, season_id: @season.id,
                                            competition_id: @competition.id, stage: 'REGULAR_SEASON')
      prediction2 = RoundPrediction.new(user: @user, matchday: 2, season_id: @season.id,
                                        competition_id: @competition.id, stage: 'REGULAR_SEASON')

      expect(prediction1).to be_valid
      expect(prediction2).to be_valid
    end

    it 'allows the same user to create predictions for the same matchday in different seasons' do
      season2 = Season.create!(start_date: Date.today, end_date: Date.today + 1.year, competition: @competition,
                               current_matchday: 1,
                               external_service_id: 2,
                               source: 'football_data')
      prediction1 = RoundPrediction.create(user: @user, matchday: 1, season_id: @season.id,
                                           competition_id: @competition.id, stage: 'REGULAR_SEASON')
      prediction2 = RoundPrediction.new(user: @user, matchday: 1, season_id: season2.id,
                                        competition_id: @competition.id, stage: 'REGULAR_SEASON')

      expect(prediction1).to be_valid
      expect(prediction2).to be_valid
    end
  end
end
