require 'rails_helper'

RSpec.describe Competition, type: :model do
  let(:area) { create(:area) }
  let(:competition) { build(:competition, area:) }

  describe 'validations' do
    it 'is valid with valid attributes' do
      expect(competition).to be_valid
    end

    it 'requires a name' do
      competition.name = nil
      expect(competition).not_to be_valid
      expect(competition.errors[:name]).to include("can't be blank")
    end

    it 'requires a code' do
      competition.code = nil
      expect(competition).not_to be_valid
      expect(competition.errors[:code]).to include("can't be blank")
    end

    it 'requires a competition_type' do
      competition.competition_type = nil
      expect(competition).not_to be_valid
      expect(competition.errors[:competition_type]).to include("can't be blank")
    end

    it 'requires a gender' do
      competition.gender = nil
      expect(competition).not_to be_valid
      expect(competition.errors[:gender]).to include("can't be blank")
    end

    it 'requires an emblem_public_id' do
      competition.emblem_public_id = nil
      expect(competition).not_to be_valid
      expect(competition.errors[:emblem_public_id]).to include("can't be blank")
    end

    it 'validates gender is either male or female' do
      competition.gender = 'other'
      expect(competition).not_to be_valid
      expect(competition.errors[:gender]).to include('other is not a valid gender, use either male or female')
    end

    it 'validates competition_type is either LEAGUE or CUP' do
      competition.competition_type = 'OTHER'
      expect(competition).not_to be_valid
      expect(competition.errors[:competition_type]).to include('OTHER is not a valid type, use either LEAGUE or CUP')
    end
  end

  describe 'associations' do
    it { should belong_to(:area) }
    it { should have_many(:seasons) }
    it { should have_many(:matches).through(:seasons) }
    it { should have_many(:favorite_teams) }
    it { should belong_to(:current_season).class_name('Season').optional }
    it { should belong_to(:current_winner).class_name('Team').optional }
  end

  describe 'colors' do
    it 'initializes with empty colors structure' do
      competition.save
      expect(competition.colors).to be_a(Hash)
    end

    it 'converts color names to hex values' do
      expect(competition.color_name_to_hex('Red')).to eq('#FF0000')
      expect(competition.color_name_to_hex('Blue')).to eq('#0000FF')
      expect(competition.color_name_to_hex('Green')).to eq('#008000')
    end

    it 'handles case-insensitive color names' do
      expect(competition.color_name_to_hex('red')).to eq('#FF0000')
      expect(competition.color_name_to_hex('BLUE')).to eq('#0000FF')
    end

    it 'handles color names without spaces' do
      expect(competition.color_name_to_hex('NavyBlue')).to eq('#000080')
      expect(competition.color_name_to_hex('DarkGreen')).to eq('#006400')
    end

    it 'returns the original value if color name is not recognized' do
      expect(competition.color_name_to_hex('NotAColor')).to eq('NotAColor')
    end

    it 'returns colors when requested' do
      competition.colors = { primary: '#FF0000', secondary: '#0000FF', tertiary: '#00FF00' }
      competition.save

      result = competition.get_colors
      expect(result[:primary]).to eq('#FF0000')
      expect(result[:secondary]).to eq('#0000FF')
      expect(result[:tertiary]).to eq('#00FF00')
    end

    it 'returns empty hash when colors are not set' do
      competition.colors = nil
      competition.save

      result = competition.get_colors
      expect(result).to eq({})
    end
  end
end
