require 'rails_helper'

RSpec.describe League, type: :model do
  describe 'archive functionality' do
    let(:area) { create(:area, name: 'Test Area', code: 'TA') }
    let(:owner) { create(:user, email: '<EMAIL>', username: 'owner') }
    let(:competition) do
      create(:competition,
             name: 'Test Competition',
             competition_type: 'LEAGUE',
             area:,
             code: 'TC',
             emblem_public_id: 'test_emblem')
    end
    let(:season) do
      create(:season,
             start_date: Date.today,
             end_date: Date.today + 1.year,
             competition:,
             current_matchday: 1)
    end
    let(:league) do
      create(:league,
             name: 'Test League',
             open: true,
             owner:,
             competition_id: competition.id,
             season_id: season.id)
    end

    describe 'scopes' do
      it 'active scope returns only active leagues' do
        active_league = league
        archived_league = create(:league,
                                 name: 'Archived League',
                                 open: true,
                                 owner:,
                                 competition_id: competition.id,
                                 season_id: season.id,
                                 archived: true,
                                 archived_at: Time.current)

        expect(League.active).to include(active_league)
        expect(League.active).not_to include(archived_league)
      end

      it 'archived scope returns only archived leagues' do
        active_league = league
        archived_league = create(:league,
                                 name: 'Archived League',
                                 open: true,
                                 owner:,
                                 competition_id: competition.id,
                                 season_id: season.id,
                                 archived: true,
                                 archived_at: Time.current)

        expect(League.archived).to include(archived_league)
        expect(League.archived).not_to include(active_league)
      end

      it 'for_season scope returns leagues for a specific season' do
        league_for_season = league
        other_season = create(:season,
                              start_date: Date.today + 1.year,
                              end_date: Date.today + 2.years,
                              competition:,
                              current_matchday: 1)
        league_for_other_season = create(:league,
                                         name: 'Other Season League',
                                         open: true,
                                         owner:,
                                         competition_id: competition.id,
                                         season_id: other_season.id)

        expect(League.for_season(season.id)).to include(league_for_season)
        expect(League.for_season(season.id)).not_to include(league_for_other_season)
      end
    end

    describe '#archive!' do
      it 'archives a league' do
        expect(league.archived).to be_falsey
        expect(league.archived_at).to be_nil

        league.archive!

        expect(league.archived).to be_truthy
        expect(league.archived_at).to be_present
      end
    end

    describe '#unarchive!' do
      it 'unarchives a league' do
        league.update!(archived: true, archived_at: Time.current)

        league.unarchive!

        expect(league.archived).to be_falsey
        expect(league.archived_at).to be_nil
      end
    end

    describe '#archived?' do
      it 'returns true if league is archived' do
        league.update!(archived: true)
        expect(league.archived?).to be_truthy
      end

      it 'returns false if league is not archived' do
        expect(league.archived?).to be_falsey
      end
    end
  end
end
