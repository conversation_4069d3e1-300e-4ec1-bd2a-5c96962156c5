# spec/models/league_spec.rb
require 'rails_helper'
require 'pry'

RSpec.describe League, type: :model do # rubocop:disable Metrics/BlockLength
  describe '#get_top_position_message' do
    let(:league) { League.new }

    it 'returns appropriate message for first place' do
      message = league.get_top_position_message(1, 'testuser')
      expect(message).to include('testuser')
      expect(message).to include('leading')
      expect(message).to include('🏆')
    end

    it 'returns appropriate message for second place' do
      message = league.get_top_position_message(2, 'testuser')
      expect(message).to include('testuser')
      expect(message).to include('second place')
      expect(message).to include('🥈')
    end

    it 'returns appropriate message for third place' do
      message = league.get_top_position_message(3, 'testuser')
      expect(message).to include('testuser')
      expect(message).to include('third place')
      expect(message).to include('🥉')
    end

    it 'returns nil for positions beyond third place' do
      message = league.get_top_position_message(4, 'testuser')
      expect(message).to be_nil
    end
  end

  describe '#calculate_standings' do # rubocop:disable Metrics/BlockLength
    it 'includes top_message for top 3 positions' do
      area = Area.create!(name: 'Test Area', code: 'TA')
      owner = User.create!(email: '<EMAIL>', username: 'owner', password: 'password')
      competition = Competition.create!(name: 'Test Competition', competition_type: 'LEAGUE', area:, code: 'TC',
                                        emblem_public_id: 'test_emblem', gender:
                                        'male', external_service_id: 1, source: 'football_data')
      season = Season.create!(start_date: Date.today, end_date: Date.today + 1.year, competition:,
                              external_service_id: 1,
                              current_matchday: 1)
      league = League.create!(name: 'Test League', owner:, competition_id: competition.id, season_id: season.id)

      # Create 5 users
      users = []
      5.times do |i|
        users << User.create!(email: "user#{i}@example.com", username: "user#{i}", password: 'password')
        league.users << users[i]
      end

      # Create round predictions with different points for each user
      users.each_with_index do |user, i|
        # Create a round prediction for this user
        rp = RoundPrediction.create!(
          user:,
          matchday: 1,
          stage: 'REGULAR_SEASON',
          season_id: season.id,
          competition_id: competition.id
        )

        # Mock the total_points method to return different values for each user
        allow(user).to receive(:points).and_return(50 - (i * 10))
        allow(user).to receive(:correct_predictions).and_return(5 - i)
        allow(user).to receive(:incorrect_predictions).and_return(i)
        allow(user).to receive(:perfect_predictions).and_return(3 - i)
      end

      standings = league.calculate_standings

      # Verify that top messages are included for positions 1-3
      top_positions = standings.select { |s| s[:position] <= 3 }
      top_positions.each do |standing|
        expect(standing[:top_message]).to be_present
        expect(standing[:top_message]).to include(standing[:username])

        case standing[:position]
        when 1
          expect(standing[:top_message]).to include('leading')
        when 2
          expect(standing[:top_message]).to include('second place')
        when 3
          expect(standing[:top_message]).to include('third place')
        end
      end

      # Verify positions beyond top 3 have nil messages
      beyond_top_positions = standings.select { |s| s[:position] > 3 }
      beyond_top_positions.each do |standing|
        expect(standing[:top_message]).to be_nil
      end
    end

    it '#calculate_standings returns standings sorted by points in descending order' do
      area = Area.create!(name: 'Test Area', code: 'TA')
      owner = User.create!(email: '<EMAIL>', username: 'owner', password: 'password')
      competition = Competition.create!(name: 'Test Competition', competition_type: 'LEAGUE', area:, code: 'TC',
                                        emblem_public_id: 'test_emblem', gender:
                                        'male', external_service_id: 1, source: 'football_data')
      season = Season.create!(start_date: Date.today, end_date: Date.today + 1.year, competition:,
                              external_service_id: 1,
                              current_matchday: 1)
      league = League.create!(name: 'Test League', open: true, owner:,
                              competition_id: competition.id,
                              season_id: season.id)
      user1 = User.create!(email: '<EMAIL>', username: 'user1', password: 'password')
      user2 = User.create!(email: '<EMAIL>', username: 'user2', password: 'password')
      Membership.create!(user: user1, league:)
      Membership.create!(user: user2, league:)

      round_prediction1 = RoundPrediction.create!(user: user1, season_id: season.id, matchday: 1,
                                                  competition_id: competition.id, stage: 'REGULAR_SEASON')
      round_prediction2 = RoundPrediction.create!(user: user2, season_id: season.id, matchday: 1,
                                                  competition_id: competition.id, stage: 'REGULAR_SEASON')

      home_team = Team.create!(name: 'Home Team', area:, short_name: 'HT', tla: 'HTA', venue: 'Home Venue',
                               external_service_id: 1, # Unique external_service_id
                               crest_public_id: 'home_crest')
      away_team = Team.create!(name: 'Away Team', area:, short_name: 'AT', tla: 'ATA', venue: 'Away Venue',
                               external_service_id: 2, # Unique external_service_id
                               crest_public_id: 'away_crest')
      match = Match.create!(season:, status: 'FINISHED', matchday: 1, home_team:, away_team:, external_service_id: 1)
      Score.create!(match:, fullTimeHome: 2, fullTimeAway: 1)

      MatchPrediction.create!(round_prediction: round_prediction1, match:, home_score: 2, away_score: 1,
                              points: 3)
      MatchPrediction.create!(round_prediction: round_prediction2, match:, home_score: 1, away_score: 1,
                              points: 1)

      standings = league.calculate_standings.reject { |s| s[:username] == owner.username }

      expect(standings.map { |s| s[:username] }).to eq([user1.username, user2.username])
    end
  end
  describe 'YouTube integration' do
    let(:area) { create(:area) }
    let(:owner) { create(:user) }
    let(:competition) { create(:competition, area:) }
    let(:season) { create(:season, competition:) }

    describe '#subscriber_only_league?' do
      it 'returns true when league is subscriber only and has a YouTube channel ID' do
        league = create(:league,
                        owner:,
                        competition:,
                        season:,
                        subscriber_only: true,
                        youtube_league: true,
                        youtube_channel_id: 'UC123')

        expect(league.subscriber_only_league?).to be_truthy
      end

      it 'returns false when league is not subscriber only' do
        league = create(:league,
                        owner:,
                        competition:,
                        season:,
                        subscriber_only: false,
                        youtube_league: true,
                        youtube_channel_id: 'UC123')

        expect(league.subscriber_only_league?).to be_falsey
      end

      it 'returns false when league has no YouTube channel ID' do
        league = create(:league,
                        owner:,
                        competition:,
                        season:,
                        subscriber_only: true,
                        youtube_league: true,
                        youtube_channel_id: nil)

        expect(league.subscriber_only_league?).to be_falsey
      end
    end

    describe '#user_meets_subscription_requirements?' do
      let(:youtube_league) do
        create(:league,
               owner:,
               competition:,
               season:,
               youtube_league: true,
               subscriber_only: true,
               youtube_channel_id: 'UC123')
      end
      let(:youtube_user) { create(:user) }
      let(:youtube_service) { instance_double(BragRightsYouTubeService) }

      before do
        allow(youtube_user).to receive(:youtube_connected?).and_return(true)
        allow(BragRightsYouTubeService).to receive(:new).with(youtube_user).and_return(youtube_service)
      end

      context 'when league is not subscriber only' do
        it 'returns true without checking subscription' do
          non_subscriber_league = create(:league,
                                         owner:,
                                         competition:,
                                         season:,
                                         subscriber_only: false)

          expect(youtube_service).not_to receive(:verify_subscription?)
          expect(non_subscriber_league.user_meets_subscription_requirements?(youtube_user)).to be_truthy
        end
      end

      context 'when user has no YouTube connection' do
        it 'returns false without checking subscription' do
          allow(youtube_user).to receive(:youtube_connected?).and_return(false)

          expect(youtube_service).not_to receive(:verify_subscription?)
          expect(youtube_league.user_meets_subscription_requirements?(youtube_user)).to be_falsey
        end
      end

      context 'when user is subscribed to the channel' do
        it 'returns true' do
          allow(youtube_service).to receive(:verify_subscription?).with('UC123').and_return(true)

          expect(youtube_league.user_meets_subscription_requirements?(youtube_user)).to be_truthy
        end
      end

      context 'when user is not subscribed to the channel' do
        it 'returns false' do
          allow(youtube_service).to receive(:verify_subscription?).with('UC123').and_return(false)

          expect(youtube_league.user_meets_subscription_requirements?(youtube_user)).to be_falsey
        end
      end

      context 'when league has additional subscription requirements' do
        before do
          youtube_league.update(subscriber_requirement_type: 'before_date', min_subscriber_date: 1.month.ago)
          allow(youtube_service).to receive(:verify_subscription?).with('UC123').and_return(true)
        end

        it 'returns true if user is subscribed' do
          expect(youtube_league.user_meets_subscription_requirements?(youtube_user)).to be_truthy
        end
      end
    end
  end
end
