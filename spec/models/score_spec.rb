require 'rails_helper'

RSpec.describe Score, type: :model do
  let(:area) { create(:area, name: 'Test Area', code: 'TA') }
  let(:competition) do
    create(:competition,
           name: 'Test Competition',
           competition_type: 'LEAGUE',
           area:,
           code: 'TC',
           emblem_public_id: 'test_emblem',
           gender: 'male')
  end
  let(:season) { create(:season, competition:) }
  let(:home_team) do
    create(:team, :valur, area:)
  end
  let(:away_team) do
    create(:team, :kr, area:)
  end
  let(:match) do
    create(:match, season:, home_team:, away_team:, status: 'FINISHED')
  end

  describe 'associations' do
    it { should belong_to(:match) }
  end

  describe 'validations' do
    it 'is valid without scores' do
      score = Score.new(match:)
      expect(score).to be_valid
    end

    it 'is valid with full time scores and calculated winner' do
      score = Score.new(
        match:,
        fullTimeHome: 2,
        fullTimeAway: 1
      )
      expect(score).to be_valid
      expect(score.winner).to eq('HOME_TEAM')
    end

    it 'validates winner when full time scores are present' do
      score = Score.new(
        match:,
        fullTimeHome: 2,
        fullTimeAway: 1,
        winner: 'INVALID'
      )
      expect(score).not_to be_valid
      expect(score.errors[:winner]).to include('INVALID is not a valid winner')
    end
  end

  describe 'callbacks' do
    context 'before validation' do
      it 'calculates HOME_TEAM as winner when home team scores more' do
        score = Score.create!(match:, fullTimeHome: 2, fullTimeAway: 1)
        expect(score.winner).to eq('HOME_TEAM')
      end

      it 'calculates AWAY_TEAM as winner when away team scores more' do
        score = Score.create!(match:, fullTimeHome: 1, fullTimeAway: 2)
        expect(score.winner).to eq('AWAY_TEAM')
      end

      it 'calculates DRAW as winner when scores are equal' do
        score = Score.create!(match:, fullTimeHome: 1, fullTimeAway: 1)
        expect(score.winner).to eq('DRAW')
      end

      it 'does not calculate winner when scores are nil' do
        score = Score.create!(match:)
        expect(score.winner).to be_nil
      end
    end
  end
end
