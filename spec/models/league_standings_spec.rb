require 'rails_helper'

RSpec.describe League, type: :model do
  describe '#calculate_standings_from_users' do
    before do
      # Create area, competitions, and seasons
      @area = Area.create!(name: 'Test Area', code: 'TA')

      @competition1 = Competition.create!(
        name: 'Competition 1',
        competition_type: 'LEAGUE',
        area: @area,
        code: 'C1',
        emblem_public_id: 'test_emblem1',
        gender: 'male',
        external_service_id: 1,
        source: 'football_data'
      )

      @competition2 = Competition.create!(
        name: 'Competition 2',
        competition_type: 'LEAGUE',
        area: @area,
        code: 'C2',
        emblem_public_id: 'test_emblem2',
        gender: 'male',
        external_service_id: 2,
        source: 'football_data'
      )

      @season1 = Season.create!(
        start_date: Date.today,
        end_date: Date.today + 1.year,
        competition: @competition1,
        external_service_id: 1,
        current_matchday: 1
      )

      @season2 = Season.create!(
        start_date: Date.today,
        end_date: Date.today + 1.year,
        competition: @competition2,
        external_service_id: 2,
        current_matchday: 1
      )

      # Create users
      @user1 = User.create!(email: '<EMAIL>', username: 'user1', password: 'password')
      @user2 = User.create!(email: '<EMAIL>', username: 'user2', password: 'password')

      # Create leagues for each competition
      @league1 = League.create!(
        name: 'League 1',
        owner: @user1,
        competition_id: @competition1.id,
        season_id: @season1.id
      )

      @league2 = League.create!(
        name: 'League 2',
        owner: @user1,
        competition_id: @competition2.id,
        season_id: @season2.id
      )

      # Add users to leagues
      @league1.memberships.create!(user: @user2)
      @league2.memberships.create!(user: @user2)

      # Create teams
      @team1 = create(:team, name: 'Team 1', external_service_id: 1, area: @area)
      @team2 = create(:team, name: 'Team 2', external_service_id: 2, area: @area)

      # Create matches for competition 1
      @match1 = create(:match,
                       home_team: @team1,
                       away_team: @team2,
                       season: @season1,
                       matchday: 1,
                       status: 'FINISHED',
                       stage: 'REGULAR_SEASON',
                       utc_date: Time.now)

      # Create matches for competition 2
      @match2 = create(:match,
                       home_team: @team1,
                       away_team: @team2,
                       season: @season2,
                       matchday: 1,
                       status: 'FINISHED',
                       stage: 'REGULAR_SEASON',
                       utc_date: Time.now)

      # Create scores for the matches
      @score1 = Score.create!(match: @match1, fullTimeHome: 2, fullTimeAway: 1)
      @score2 = Score.create!(match: @match2, fullTimeHome: 3, fullTimeAway: 0)

      # Create round predictions for user1 in competition 1
      @rp1_user1 = RoundPrediction.create!(
        user: @user1,
        matchday: 1,
        season_id: @season1.id,
        competition_id: @competition1.id,
        stage: 'REGULAR_SEASON'
      )

      # Create round predictions for user1 in competition 2
      @rp2_user1 = RoundPrediction.create!(
        user: @user1,
        matchday: 1,
        season_id: @season2.id,
        competition_id: @competition2.id,
        stage: 'REGULAR_SEASON'
      )

      # Create round predictions for user2 in competition 1
      @rp1_user2 = RoundPrediction.create!(
        user: @user2,
        matchday: 1,
        season_id: @season1.id,
        competition_id: @competition1.id,
        stage: 'REGULAR_SEASON'
      )

      # Create round predictions for user2 in competition 2
      @rp2_user2 = RoundPrediction.create!(
        user: @user2,
        matchday: 1,
        season_id: @season2.id,
        competition_id: @competition2.id,
        stage: 'REGULAR_SEASON'
      )

      # Create match predictions for user1 in competition 1
      @mp1_user1 = @rp1_user1.match_predictions.create!(
        match: @match1,
        home_score: 2,
        away_score: 1,
        user: @user1
      )

      # Create match predictions for user1 in competition 2
      @mp2_user1 = @rp2_user1.match_predictions.create!(
        match: @match2,
        home_score: 1,
        away_score: 1,
        user: @user1
      )

      # Create match predictions for user2 in competition 1
      @mp1_user2 = @rp1_user2.match_predictions.create!(
        match: @match1,
        home_score: 1,
        away_score: 1,
        user: @user2
      )

      # Create match predictions for user2 in competition 2
      @mp2_user2 = @rp2_user2.match_predictions.create!(
        match: @match2,
        home_score: 3,
        away_score: 0,
        user: @user2
      )

      # Calculate points for all match predictions
      @mp1_user1.calculate_points
      @mp2_user1.calculate_points
      @mp1_user2.calculate_points
      @mp2_user2.calculate_points
    end

    it 'only includes points for the specific competition and season' do
      # Get standings for league 1 (competition 1)
      standings1 = @league1.calculate_standings_from_users

      # User1 should have 3 points (perfect prediction) in competition 1
      user1_standing = standings1.find { |s| s[:username] == 'user1' }
      expect(user1_standing[:points]).to eq(3)
      expect(user1_standing[:perfect]).to eq(1)
      expect(user1_standing[:correct]).to eq(0)
      expect(user1_standing[:incorrect]).to eq(0)

      # User2 should have 0 points (incorrect prediction) in competition 1
      user2_standing = standings1.find { |s| s[:username] == 'user2' }
      expect(user2_standing[:points]).to eq(0)
      expect(user2_standing[:perfect]).to eq(0)
      expect(user2_standing[:correct]).to eq(0)
      expect(user2_standing[:incorrect]).to eq(1)

      # Get standings for league 2 (competition 2)
      standings2 = @league2.calculate_standings_from_users

      # User1 should have 0 points (incorrect prediction) in competition 2
      user1_standing = standings2.find { |s| s[:username] == 'user1' }
      expect(user1_standing[:points]).to eq(0)
      expect(user1_standing[:perfect]).to eq(0)
      expect(user1_standing[:correct]).to eq(0)
      expect(user1_standing[:incorrect]).to eq(1)

      # User2 should have 3 points (perfect prediction) in competition 2
      user2_standing = standings2.find { |s| s[:username] == 'user2' }
      expect(user2_standing[:points]).to eq(3)
      expect(user2_standing[:perfect]).to eq(1)
      expect(user2_standing[:correct]).to eq(0)
      expect(user2_standing[:incorrect]).to eq(0)
    end
  end
end
