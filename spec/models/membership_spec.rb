require 'rails_helper'

RSpec.describe Membership, type: :model do
  describe 'associations' do
    it { should belong_to(:user) }
    it { should belong_to(:league) }
  end
  
  describe 'validations' do
    let(:user) { create(:user) }
    let(:league) { create(:league, owner: create(:user)) }
    
    it 'is valid with valid attributes' do
      membership = Membership.new(user: user, league: league)
      expect(membership).to be_valid
    end
    
    context 'when user has reached league limit' do
      let(:user_at_limit) { create(:user) }
      let(:new_league) { create(:league, owner: create(:user)) }
      
      before do
        # Create 5 leagues and memberships for the user
        5.times do |i|
          league = create(:league, name: "Test League #{i}", owner: create(:user))
          create(:membership, user: user_at_limit, league: league)
        end
      end
      
      it 'prevents creating a new membership' do
        membership = Membership.new(user: user_at_limit, league: new_league)
        
        expect(membership).not_to be_valid
        expect(membership.errors[:user]).to include('is already a member of 5 leagues')
      end
    end
  end
  
  describe 'subscription status' do
    let(:user) { create(:user) }
    let(:league) { create(:league, owner: create(:user), subscriber_only: true, youtube_channel_id: 'test_channel') }
    
    it 'can have an active subscription status' do
      membership = create(:membership, :with_subscription, user: user, league: league)
      expect(membership.subscription_status).to eq('active')
      expect(membership.subscription_verified_at).to be_present
    end
    
    it 'can have a grace period' do
      membership = create(:membership, :with_grace_period, user: user, league: league)
      expect(membership.subscription_status).to eq('grace_period')
      expect(membership.grace_period_ends_at).to be_present
    end
  end
end
