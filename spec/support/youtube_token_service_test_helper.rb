# Helper for testing YoutubeTokenService
module YoutubeTokenServiceTestHelper
  # Create a mock user class that doesn't use encryption
  class MockUser
    attr_accessor :youtube_credentials

    def initialize
      @youtube_credentials = nil
      @id = 1 # Add a mock ID
    end

    attr_reader :id

    def update(attributes)
      @youtube_credentials = attributes[:youtube_credentials] if attributes.key?(:youtube_credentials)
      true
    end

    def reload
      self
    end

    def disconnect_youtube_account
      @youtube_credentials = nil
    end
  end
end
