module ApiHelpers
  def parsed_body
    JSON.parse(response.body, object_class: OpenStruct)
  end

  def json
    JSON.parse(response.body)
  end

  def json_response
    JSON.parse(response.body)
  end

  def authentication_headers_for(_user, token)
    {
      'Authorization' => "Bearer #{token.access_token}",
      'Accept' => 'application/json',
      'Content-Type' => 'application/json'
    }
  end
end
