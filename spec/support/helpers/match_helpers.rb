require 'pry'
require 'round_robin_tournament'

def create_rounds_and_matches(season_id)
  # Get all teams for this season
  season = Season.find(season_id)
  teams = season.teams

  # Generate a complete round robin schedule
  team_ids = teams.map(&:id)
  schedule = RoundRobinTournament.schedule(team_ids)

  # Create first half of the season
  schedule.each_with_index do |matchday, day_index|
    matchday.each_with_index do |pairing, match_index|
      next if pairing[0].nil? || pairing[1].nil?

      home_team = Team.find(pairing[0])
      away_team = Team.find(pairing[1])

      match = FactoryBot.create(:match,
                                home_team_id: home_team.id,
                                away_team_id: away_team.id,
                                matchDay: day_index + 1,
                                status: 'SCHEDULED',
                                utc_date: (day_index * 7 + match_index).days.from_now,
                                season_id:)
      match.score = Score.create
    end
  end

  # Create second half of the season with home/away teams swapped
  schedule.each_with_index do |matchday, day_index|
    matchday.each_with_index do |pairing, match_index|
      next if pairing[0].nil? || pairing[1].nil?

      # Swap home and away teams
      away_team = Team.find(pairing[0])
      home_team = Team.find(pairing[1])

      first_half_matchdays = schedule.length
      match = FactoryBot.create(:match,
                                home_team_id: home_team.id,
                                away_team_id: away_team.id,
                                matchDay: first_half_matchdays + day_index + 1,
                                status: 'SCHEDULED',
                                utc_date: ((first_half_matchdays + day_index) * 7 + match_index).days.from_now,
                                season_id:)
      match.score = Score.create
    end
  end
end

def add_scores_to_matches(comp)
  r1 = [{ home: 2, away: 1 }, { home: 0, away: 3 }, { home: 2, away: 2 }]
  r2 = [{ home: 1, away: 0 }, { home: 0, away: 4 }, { home: 3, away: 4 }]

  set_matches_to_finished(1, comp, r1)
  set_matches_to_finished(2, comp, r2)

  season = comp.current_season
  season.current_matchday = 3
  season.save! # Ensure the changes are saved
end

def set_matches_to_finished(matchday, comp, results)
  matches = Match.where(matchDay: matchday, season_id: comp.current_season_id).order(:id)
  matches.each_with_index do |match, index|
    next if index >= results.length

    update_match(match, results[index][:home], results[index][:away])
  end
end

def update_match(match, home_score, away_score)
  match.status = 'FINISHED'
  match.save!

  match.score ||= Score.create(match:)
  match.score.update!(
    fullTimeHome: home_score,
    fullTimeAway: away_score
  )
end
