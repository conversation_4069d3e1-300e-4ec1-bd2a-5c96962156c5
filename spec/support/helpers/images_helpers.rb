module ImagesHelpers
  def image(team) # rubocop:disable Metrics/MethodLength
    arr = [
      {
        name: 'Kr',
        filename: '101.png',
        content_type: 'image/png'
      }, {
        name: '<PERSON>ur',
        filename: '107.svg',
        content_type: 'image/svg+xml'
      }, {
        name: 'Bre<PERSON>b<PERSON>',
        filename: '200.svg',
        content_type: 'image/svg+xml'
      }, {
        name: 'Reynir',
        filename: 'reynir.png',
        content_type: 'image/png'
      }, {
        name: '<PERSON><PERSON>',
        filename: '103.svg',
        content_type: 'image/svg+xml'
      }, {
        name: 'Fram',
        filename: '108.svg',
        content_type: 'image/svg+xml'
      }
    ]

    arr.find { |obj| obj[:name] == team.name }
  end
end
