def user1_with_predictions(user, comp)
  match_predictions_r1 = [
    { home: 2, away: 1 },
    { home: 3, away: 0 },
    { home: 2, away: 2 }
  ]

  match_predictions_r2 = [
    { home: 1, away: 3 },
    { home: 0, away: 4 },
    { home: 2, away: 2 }
  ]

  setup_user_with_predictions(1, user, comp, match_predictions_r1)
  setup_user_with_predictions(2, user, comp, match_predictions_r2)
end

def user2_with_predictions(user, comp)
  match_predictions_r1 = [
    { home: 2, away: 1 },
    { home: 1, away: 2 },
    { home: 3, away: 4 }
  ]

  match_predictions_r2 = [
    { home: 1, away: 0 },
    { home: 0, away: 1 },
    { home: 3, away: 4 }
  ]

  setup_user_with_predictions(1, user, comp, match_predictions_r1)
  setup_user_with_predictions(2, user, comp, match_predictions_r2)
end

def setup_user_with_predictions(match_day, user, comp, match_predictions)
  round_prediction = create(:round_prediction, user:, competition_id: comp.id, season_id: comp.current_season_id,
                                               matchDay: match_day)

  create_round_predictions_with_match_predictions(match_predictions, round_prediction)
end

def create_round_predictions_with_match_predictions(match_predictions, round_prediction)
  matches_round = Match.where(matchDay: round_prediction.matchDay)

  matches_round.each_with_index do |match, index|
    create(:match_prediction, home: match_predictions[index][:home],
                              away: match_predictions[index][:away], round_prediction:, match:)
  end
end
