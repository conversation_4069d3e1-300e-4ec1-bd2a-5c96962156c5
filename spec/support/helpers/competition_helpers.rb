require 'pry'
require 'round_robin_tournament'

def create_competition_with_teams
  area = FactoryBot.create(:area)
  comp = FactoryBot.build(:competition, area:)
  season = FactoryBot.create(:season, competition: comp)
  comp.current_season_id = season.id
  teams = %w[
    valur
    kr
    fram
    breidablik
    reynir
    vikingur
  ]

  teams.each do |trait|
    FactoryBot.create(:team, trait.to_sym, area_id: area.id)
  end
  comp.current_winner = Team.first.id
  comp.save
end
