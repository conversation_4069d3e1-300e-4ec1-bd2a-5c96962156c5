# Mock for Google::Apis::YoutubeV3::YouTubeService
module Google
  module Apis
    # Define the Error class that's used in the tests
    class Error < StandardError
      attr_reader :status_code, :body

      def initialize(message, status_code = nil, body = nil)
        super(message)
        @status_code = status_code
        @body = body
      end
    end

    module YoutubeV3
      class YouTubeService
        attr_accessor :key, :authorization

        def list_subscriptions(part, options = {})
          # Return a mock response
          OpenStruct.new(items: [])
        end

        def list_channels(part, options = {})
          # Return a mock response
          OpenStruct.new(items: [])
        end
      end
    end
  end
end
