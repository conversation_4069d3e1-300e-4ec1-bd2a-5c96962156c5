module DatabasePopulate
  def populate_database
    Rails.logger.info 'DatabasePopulate: Populating database with test data...'
    # Create a shared area for both competition and teams
    area = create(:area)

    # Create competition
    competition = create(:competition, area:)

    # Create a season for the competition
    season = create(:season, competition:)
    competition.update(current_season: season)

    # Create teams using the same area
    teams = []
    teams << create(:team, :kr, area:)
    teams << create(:team, :valur, area:)
    teams << create(:team, :fram, area:)
    teams << create(:team, :breidablik, area:)
    teams << create(:team, :reynir, area:)
    teams << create(:team, :vikingur, area:)

    # Associate teams with season
    teams.each { |team| season.teams << team }

    # Create a complete season schedule using round robin tournament
    create_round_robin_schedule(season.id, teams)

    # Set a couple of matches as finished with scores
    first_two_matches = Match.where(season_id: season.id, matchday: 1).limit(2)
    first_two_matches.each_with_index do |match, index|
      match.status = 'FINISHED'
      match.utc_date = index.days.ago
      match.save

      score = Score.create(match:)
      score.update(
        fullTimeHome: 2 - index, # First match: 2-1, Second match: 1-0
        fullTimeAway: 1 - index
      )
    end

    season.update(current_matchday: 1)

    # Return the competition as a convenience
    competition
  end

  private

  def create_round_robin_schedule(season_id, teams)
    # Generate a complete round robin schedule
    team_ids = teams.map(&:id)
    schedule = RoundRobinTournament.schedule(team_ids)

    # For each matchday in the schedule
    schedule.each_with_index do |matchday, day_index|
      # For each match pairing in this matchday
      matchday.each_with_index do |pairing, match_index|
        # Skip matches with nil (occurs in odd-numbered leagues)
        next if pairing[0].nil? || pairing[1].nil?

        # Create the match for this pairing
        home_team = Team.find(pairing[0])
        away_team = Team.find(pairing[1])

        # Create the match with basic attributes
        match = create(:match,
                       matchday: day_index + 1,
                       status: 'SCHEDULED',
                       utc_date: (day_index * 7 + match_index).days.from_now,
                       home_team:,
                       away_team:,
                       season_id:)

        # Create an empty score record for the match
        Score.create(match:)
      end
    end

    # Create the second half of the season with home/away teams swapped
    schedule.each_with_index do |matchday, day_index|
      # For each match pairing in this matchday
      matchday.each_with_index do |pairing, match_index|
        # Skip matches with nil (occurs in odd-numbered leagues)
        next if pairing[0].nil? || pairing[1].nil?

        # Create the match with swapped home/away teams
        away_team = Team.find(pairing[0])
        home_team = Team.find(pairing[1])

        # Create the match - add number of first half matchdays to current day_index
        first_half_matchdays = schedule.length
        match = create(:match,
                       matchday: first_half_matchdays + day_index + 1,
                       status: 'SCHEDULED',
                       utc_date: ((first_half_matchdays + day_index) * 7 + match_index).days.from_now,
                       home_team:,
                       away_team:,
                       season_id:)

        # Create an empty score record for the match
        Score.create(match:)
      end
    end
  end
end

RSpec.configure do |config|
  config.include DatabasePopulate
end
