# Mock ActiveRecord encryption for tests
module ActiveR<PERSON>ordEncryptionMock
  def self.setup
    # Skip encryption in test environment
    if defined?(ActiveRecord::Encryption)
      # Create a mock configuration for encryption
      ActiveRecord::Encryption.configure(
        primary_key: "test" * 8,
        deterministic_key: "test" * 8,
        key_derivation_salt: "test" * 8,
        support_unencrypted_data: true
      )
    end
  end
end

# Set up the mock configuration
ActiveRecordEncryptionMock.setup
