require 'rails_helper'

RSpec.describe Api::V1::UserStatisticsController, type: :controller do
  let(:user) { create(:user) }
  let(:other_user) { create(:user) }
  let(:competition) { create(:competition) }
  let(:season) { create(:season, competition:) }

  before do
    # Create some round predictions for the user
    3.times do |i|
      rp = create(:round_prediction,
                  user:,
                  competition_id: competition.id,
                  season_id: season.id,
                  matchday: i + 1)

      # Create some match predictions with different points
      create(:match_prediction, round_prediction: rp, points: 3) # Perfect
      create(:match_prediction, round_prediction: rp, points: 1) # Correct
      create(:match_prediction, round_prediction: rp, points: 0) # Incorrect
    end

    # Create a favorite team for the user
    team = create(:team)
    create(:favorite_team, user:, team:, competition:)

    # Set up authentication
    token = create(:devise_api_token, resource_owner: user)
    request.headers['Authorization'] = "Bearer #{token.access_token}"
    allow(controller).to receive(:current_devise_api_user).and_return(user)
  end

  describe '.invalidate_cache' do
    it 'invalidates the cache for a user' do
      # First, make a request to cache some statistics
      get :show, params: { user_id: user.id, type: 'basic' }
      expect(response).to have_http_status(:ok)

      # Mock Rails.cache.delete to verify it's called
      expect(Rails.cache).to receive(:delete).at_least(:once)

      # Call the invalidate_cache method
      Api::V1::UserStatisticsController.invalidate_cache(user.id)
    end
  end

  describe 'GET #show' do
    context 'when requesting basic stats' do
      it 'returns basic statistics for the user' do
        get :show, params: { user_id: user.id, type: 'basic' }

        expect(response).to have_http_status(:ok)
        expect(json_response[:data]).to include(
          :total_points,
          :total_predictions,
          :prediction_accuracy,
          :perfect_predictions,
          :correct_predictions,
          :incorrect_predictions,
          :highest_round_score,
          :average_round_score,
          :most_perfect_predictions_in_a_round
        )
      end

      it 'caches the statistics' do
        # Mock the Rails cache to verify it's being used
        expect(Rails.cache).to receive(:fetch).and_call_original

        get :show, params: { user_id: user.id, type: 'basic' }
        expect(response).to have_http_status(:ok)
      end

      it 'uses cached statistics on subsequent requests' do
        # Mock Rails.cache to verify fetch behavior
        cache_key = anything

        # First call should calculate and cache
        expect(Rails.cache).to receive(:fetch).with(cache_key, expires_in: 1.hour).and_yield

        # First request should calculate statistics
        get :show, params: { user_id: user.id, type: 'basic' }
        expect(response).to have_http_status(:ok)

        # Reset the controller for the next request
        @controller = Api::V1::UserStatisticsController.new
        allow(@controller).to receive(:current_devise_api_user).and_return(user)

        # Second call should return cached value
        expect(Rails.cache).to receive(:fetch).with(cache_key, expires_in: 1.hour).and_return({
                                                                                                total_points: 0,
                                                                                                total_predictions: 0,
                                                                                                prediction_accuracy: 0,
                                                                                                perfect_predictions: {
                                                                                                  count: 0, percentage: 0
                                                                                                },
                                                                                                correct_predictions: {
                                                                                                  count: 0, percentage: 0
                                                                                                },
                                                                                                incorrect_predictions: {
                                                                                                  count: 0, percentage: 0
                                                                                                },
                                                                                                highest_round_score: 0,
                                                                                                average_round_score: 0,
                                                                                                most_perfect_predictions_in_a_round: 0
                                                                                              })

        # Second request should use cached statistics
        get :show, params: { user_id: user.id, type: 'basic' }
        expect(response).to have_http_status(:ok)
      end
    end

    context 'when requesting advanced stats' do
      it 'returns advanced statistics for the user' do
        get :show, params: { user_id: user.id, type: 'advanced' }

        expect(response).to have_http_status(:ok)
        expect(json_response[:data]).to include(
          :consistency_rating,
          :streaks
        )
      end
    end

    context 'when requesting competition stats' do
      it 'returns competition-specific statistics for the user' do
        get :show, params: { user_id: user.id, type: 'competition' }

        expect(response).to have_http_status(:ok)
        expect(json_response[:data]).to include(:competition_performance)
        expect(json_response[:data][:competition_performance]).to be_an(Array)

        if json_response[:data][:competition_performance].any?
          comp_stats = json_response[:data][:competition_performance].first
          expect(comp_stats).to include(
            :competition_id,
            :competition_name,
            :points,
            :total_predictions,
            :accuracy,
            :perfect_predictions,
            :correct_predictions,
            :incorrect_predictions
          )
        end
      end

      it 'filters by competition_id when provided' do
        get :show, params: { user_id: user.id, type: 'competition', competition_id: competition.id }

        expect(response).to have_http_status(:ok)
        expect(json_response[:data][:competition_performance]).to be_an(Array)

        if json_response[:data][:competition_performance].any?
          comp_stats = json_response[:data][:competition_performance].first
          expect(comp_stats[:competition_id]).to eq(competition.id)
        end
      end
    end

    context 'when requesting comparative stats' do
      it 'returns comparative statistics for the user' do
        get :show, params: { user_id: user.id, type: 'comparative' }

        expect(response).to have_http_status(:ok)
        expect(json_response[:data]).to include(
          :global_rank,
          :total_users,
          :percentile_ranking
        )
      end
    end

    context 'when requesting all stats' do
      it 'returns all statistics for the user' do
        get :show, params: { user_id: user.id, type: 'all' }

        expect(response).to have_http_status(:ok)
        expect(json_response[:data]).to include(
          :total_points,
          :prediction_accuracy,
          :perfect_predictions,
          :correct_predictions,
          :incorrect_predictions,
          :consistency_rating,
          :streaks,
          :competition_performance,
          :global_rank,
          :percentile_ranking
        )
      end
    end

    context 'when filtering by time period' do
      it 'returns filtered statistics for current_season' do
        get :show, params: { user_id: user.id, type: 'basic', time_period: 'current_season' }

        expect(response).to have_http_status(:ok)
        expect(json_response[:data]).to include(:total_points)
      end

      it 'returns filtered statistics for last_month' do
        get :show, params: { user_id: user.id, type: 'basic', time_period: 'last_month' }

        expect(response).to have_http_status(:ok)
        expect(json_response[:data]).to include(:total_points)
      end
    end

    context 'when user is not authorized' do
      before do
        # Set up authentication for other_user
        token = create(:devise_api_token, resource_owner: other_user)
        request.headers['Authorization'] = "Bearer #{token.access_token}"
        allow(controller).to receive(:current_devise_api_user).and_return(other_user)
      end

      it 'returns forbidden status' do
        get :show, params: { user_id: user.id, type: 'basic' }

        expect(response).to have_http_status(:forbidden)
      end
    end

    context 'when user is not authenticated' do
      before do
        # Clear authentication
        request.headers['Authorization'] = nil
        allow(controller).to receive(:current_devise_api_user).and_return(nil)
      end

      it 'returns unauthorized status' do
        get :show, params: { user_id: user.id, type: 'basic' }

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  private

  def json_response
    JSON.parse(response.body, symbolize_names: true)
  end
end
