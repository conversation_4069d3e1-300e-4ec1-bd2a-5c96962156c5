require 'rails_helper'

# Mock class to avoid API key check in tests
class MockFootballDataService < FootballDataService
  def initialize
    # Skip the API key check in tests
    @base_url = 'https://api.football-data.org/v4'
    @cloudinary_service = CloudinaryService.new
  end
end

RSpec.describe Api::V1::Admin::CompetitionsController, type: :controller do
  let(:user) { create(:user, admin: true) }
  let(:area) { create(:area) }
  let(:token) { create(:devise_api_token, resource_owner: user) }

  before do
    request.headers['Authorization'] = "Bearer #{token.access_token}"
    sign_in user
  end

  describe 'POST #create_new_season' do
    context 'with FootballData competition' do
      let(:competition) { create(:competition, area:, code: 'PL', source: 'football_data') }
      let(:old_season) { create(:season, competition:, start_date: 1.year.ago, end_date: 1.day.ago) }
      let(:new_season) { build(:season, competition:, start_date: Date.today, end_date: 1.year.from_now) }

      before do
        competition.update!(current_season_id: old_season.id)
        # Use the mock class to avoid API key check
        allow(FootballDataService).to receive(:new).and_return(MockFootballDataService.new)
        allow_any_instance_of(MockFootballDataService).to receive(:create_new_season_for_competition)
          .and_return(new_season)
      end

      it 'calls the FootballDataService to create a new season' do
        expect_any_instance_of(MockFootballDataService).to receive(:create_new_season_for_competition)
          .with(competition)
          .and_return(new_season)

        post :create_new_season, params: { id: competition.id }

        expect(response).to have_http_status(:success)
        expect(JSON.parse(response.body)['message']).to include("New season created successfully for #{competition.name}")
      end
    end

    context 'with ApiFootball competition' do
      let(:competition) { create(:competition, area:, code: 'SV', source: 'api_football', external_service_id: '113') }
      let(:old_season) { create(:season, competition:, start_date: 1.year.ago, end_date: 1.day.ago) }
      let(:new_season) { build(:season, competition:, start_date: Date.today, end_date: 1.year.from_now) }
      let(:season_external_service_id) { '2026' }

      before do
        competition.update!(current_season_id: old_season.id)
        allow_any_instance_of(ApiFootballService).to receive(:create_new_season_for_competition)
          .and_return(new_season)
      end

      it 'calls the ApiFootballService to create a new season with external service ID' do
        expect_any_instance_of(ApiFootballService).to receive(:create_new_season_for_competition)
          .with(competition, season_external_service_id)
          .and_return(new_season)

        post :create_new_season, params: { id: competition.id, season_external_service_id: }

        expect(response).to have_http_status(:success)
        expect(JSON.parse(response.body)['message']).to include("New season created successfully for #{competition.name}")
      end

      it 'returns an error if season_external_service_id is missing' do
        post :create_new_season, params: { id: competition.id }

        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['errors']).to include('Season external service ID is required for ApiFootball competitions')
      end

      it 'returns an error if competition external_service_id is missing' do
        # Mock the ApiFootballService to raise an error when external_service_id is missing
        allow_any_instance_of(ApiFootballService).to receive(:create_new_season_for_competition)
          .and_raise(ApiFootballService::ApiError.new('Both competition.external_service_id and season_id must be provided'))

        # Temporarily set external_service_id to nil in the controller
        allow_any_instance_of(Competition).to receive(:external_service_id).and_return(nil)

        post :create_new_season, params: { id: competition.id, season_external_service_id: }

        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['errors']).to include('Competition external service ID is missing. Please update the competition first.')
      end
    end

    context 'with KsiSoap competition' do
      let(:competition) { create(:competition, area:, code: 'BDkk', source: 'ksi_soap') }
      let(:old_season) { create(:season, competition:, start_date: 1.year.ago, end_date: 1.day.ago) }
      let(:new_season) { build(:season, competition:, start_date: Date.today, end_date: 1.year.from_now) }
      let(:season_external_service_id) { '50000' }

      before do
        competition.update!(current_season_id: old_season.id)
        allow_any_instance_of(KsiSoapService).to receive(:create_new_season_for_competition)
          .and_return(new_season)
      end

      it 'calls the KsiSoapService to create a new season with external service ID' do
        expect_any_instance_of(KsiSoapService).to receive(:create_new_season_for_competition)
          .with(competition, season_external_service_id)
          .and_return(new_season)

        post :create_new_season, params: { id: competition.id, season_external_service_id: }

        expect(response).to have_http_status(:success)
        expect(JSON.parse(response.body)['message']).to include("New season created successfully for #{competition.name}")
      end

      it 'returns an error if season_external_service_id is missing' do
        post :create_new_season, params: { id: competition.id }

        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['errors']).to include('Season external service ID is required for KSI competitions')
      end
    end

    context 'with unsupported competition code' do
      let(:competition) { create(:competition, area:, code: 'UNSUPPORTED', source: 'football_data') }

      it 'returns an error for unsupported competition code' do
        post :create_new_season, params: { id: competition.id }

        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['errors']).to include('Unsupported competition code')
      end
    end

    context 'when an error occurs' do
      let(:competition) { create(:competition, area:, code: 'PL', source: 'football_data') }

      before do
        # Use the mock class to avoid API key check
        allow(FootballDataService).to receive(:new).and_return(MockFootballDataService.new)
        allow_any_instance_of(MockFootballDataService).to receive(:create_new_season_for_competition)
          .and_raise(StandardError.new('API error'))
      end

      it 'returns an error response' do
        post :create_new_season, params: { id: competition.id }

        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['errors']).to include('Failed to create new season: API error')
      end
    end
  end
end
