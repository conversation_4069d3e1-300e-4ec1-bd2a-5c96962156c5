require 'rails_helper'

RSpec.describe Api::V1::Admin::CompetitionsController, type: :controller do
  let(:user) { create(:user, admin: true) }
  let(:area) { create(:area) }
  let(:competition) { create(:competition, area:) }
  let(:token) { create(:devise_api_token, resource_owner: user) }

  before do
    request.headers['Authorization'] = "Bearer #{token.access_token}"
    sign_in user
  end

  describe 'GET #index' do
    it 'returns a success response' do
      get :index
      expect(response).to be_successful
      expect(JSON.parse(response.body)['data']).to be_an(Array)
    end
  end

  describe 'GET #show' do
    it 'returns a success response' do
      get :show, params: { id: competition.id }
      expect(response).to be_successful
      expect(JSON.parse(response.body)['data']['id']).to eq(competition.id)
    end
  end

  describe 'PUT #update' do
    context 'with valid params' do
      let(:new_attributes) do
        {
          competition: {
            name: 'Updated Competition Name',
            colors: {
              primary: '#FF0000',
              secondary: '#0000FF',
              tertiary: '#00FF00'
            }
          }
        }
      end

      it 'updates the requested competition' do
        put :update, params: { id: competition.id }.merge(new_attributes)
        competition.reload
        expect(competition.name).to eq('Updated Competition Name')
        expect(competition.colors['primary']).to eq('#FF0000')
        expect(competition.colors['secondary']).to eq('#0000FF')
        expect(competition.colors['tertiary']).to eq('#00FF00')
      end

      it 'returns a success response' do
        put :update, params: { id: competition.id }.merge(new_attributes)
        expect(response).to be_successful
        expect(JSON.parse(response.body)['data']['name']).to eq('Updated Competition Name')
        expect(JSON.parse(response.body)['data']['colors']['primary']).to eq('#FF0000')
      end
    end

    context 'with invalid params' do
      let(:invalid_attributes) do
        {
          competition: {
            name: nil
          }
        }
      end

      it 'returns an error response' do
        put :update, params: { id: competition.id }.merge(invalid_attributes)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['errors']).to include("Name can't be blank")
      end
    end
  end
end
