require 'rails_helper'

RSpec.describe Api::V1::Admin::CompetitionsController, type: :controller do
  let(:user) { create(:user, admin: true) }
  let(:area) { create(:area) }
  let(:token) { create(:devise_api_token, resource_owner: user) }

  before do
    request.headers['Authorization'] = "Bearer #{token.access_token}"
    sign_in user
  end

  describe 'GET #new_season_requirements' do
    context 'with FootballData competition' do
      let(:competition) do
        create(:competition, area:, code: 'PL', source: 'football_data', external_service_id: '2021')
      end

      it 'returns requirements for FootballData competitions' do
        get :new_season_requirements, params: { id: competition.id }

        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)

        expect(json_response['competition']['id']).to eq(competition.id)
        expect(json_response['competition']['code']).to eq('PL')
        expect(json_response['competition']['source']).to eq('football_data')

        expect(json_response['requirements']['needs_season_external_service_id']).to be false
        expect(json_response['requirements']['season_id_format']).to be_nil
      end
    end

    context 'with ApiFootball competition' do
      let(:competition) { create(:competition, area:, code: 'SV', source: 'api_football', external_service_id: '113') }

      it 'returns requirements for ApiFootball competitions' do
        get :new_season_requirements, params: { id: competition.id }

        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)

        expect(json_response['competition']['id']).to eq(competition.id)
        expect(json_response['competition']['code']).to eq('SV')
        expect(json_response['competition']['source']).to eq('api_football')

        expect(json_response['requirements']['needs_season_external_service_id']).to be true
        expect(json_response['requirements']['season_id_format']).to eq('year')
        expect(json_response['requirements']['season_id_example']).to eq((Date.today.year + 1).to_s)
      end

      it 'indicates when competition external_service_id is missing' do
        # Mock the controller to simulate missing external_service_id
        allow_any_instance_of(Api::V1::Admin::CompetitionsController).to receive(:set_competition).and_wrap_original do |original, *args|
          original.call(*args)
          allow(@controller.instance_variable_get(:@competition)).to receive(:external_service_id).and_return(nil)
        end

        get :new_season_requirements, params: { id: competition.id }

        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)

        expect(json_response['requirements']['missing_competition_external_service_id']).to be true
      end
    end

    context 'with KSI competition' do
      let(:competition) { create(:competition, area:, code: 'BDkk', source: 'ksi_soap') }

      it 'returns requirements for KSI competitions' do
        get :new_season_requirements, params: { id: competition.id }

        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)

        expect(json_response['competition']['id']).to eq(competition.id)
        expect(json_response['competition']['code']).to eq('BDkk')
        expect(json_response['competition']['source']).to eq('ksi_soap')

        expect(json_response['requirements']['needs_season_external_service_id']).to be true
        expect(json_response['requirements']['season_id_format']).to eq('numeric_id')
        expect(json_response['requirements']['season_id_example']).to eq('50000')
      end
    end

    context 'with unsupported competition code' do
      let(:competition) { create(:competition, area:, code: 'UNSUPPORTED', source: 'football_data') }

      it 'returns an error for unsupported competition code' do
        get :new_season_requirements, params: { id: competition.id }

        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['errors']).to include('Unsupported competition code')
      end
    end

    context 'when competition is not found' do
      it 'returns a 404 error' do
        get :new_season_requirements, params: { id: 999 }

        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)['errors']).to include('Competition not found')
      end
    end
  end
end
