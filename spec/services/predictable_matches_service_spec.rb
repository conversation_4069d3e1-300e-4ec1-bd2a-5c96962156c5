require 'rails_helper'

RSpec.describe PredictableMatchesService do # rubocop:disable Metrics/BlockLength
  describe '.fetch_matches' do # rubocop:disable Metrics/BlockLength
    let(:area) { create(:area, name: 'England', code: 'ENG') }
    let(:competition) do
      create(:competition, name: 'Premier League', code: 'PL', area:, competition_type: 'LEAGUE')
    end
    let(:season) do
      create(:season, competition:, current_matchday: 10, start_date: 1.month.ago, end_date: 3.months.from_now)
    end
    let(:team1) do
      create(:team, name: 'Team 1', tla: 'Team1',
                    short_name: 'TT1', venue: 'Test Venue 1',
                    crest_public_id: 'test1')
    end
    let(:team2) do
      create(:team, name: 'Team 2', tla: 'TEST2',
                    short_name: 'TT2', venue: 'Test Venue 2',
                    crest_public_id: 'test2')
    end

    before do
      competition.update(current_season: season)
      season.teams << [team1, team2]
    end

    context 'when there are matches in the next 7 days' do
      before do
        create(:match,
               season:,
               matchday: 10,
               home_team: team1,
               away_team: team2,
               status: 'TIMED',
               utc_date: 2.days.from_now)
      end

      it 'returns matches within the next 7 days' do
        matches = described_class.fetch_matches(competition_code: 'PL')
        expect(matches.length).to eq(1)
        expect(matches.first.utc_date).to be_within(1.day).of(2.days.from_now)
      end
    end

    context 'when there are no matches in the next 7 days (international break)' do
      let!(:next_match) do
        create(:match,
               season:,
               matchday: 11,
               home_team: team1,
               away_team: team2,
               status: 'TIMED',
               utc_date: 14.days.from_now)
      end

      it 'extends the search to find the next upcoming matchday' do
        matches = described_class.fetch_matches(competition_code: 'PL')
        expect(matches.length).to eq(1)
        expect(matches.first.utc_date).to be_within(1.day).of(14.days.from_now)
        expect(matches.first.matchday).to eq(11)
      end
    end

    context 'when the season is over' do
      let(:ended_season) do
        create(:season, competition:, current_matchday: 38, start_date: 6.months.ago, end_date: 1.day.ago)
      end

      before do
        competition.update(current_season: ended_season)
        # Mock the season_over? method to return true
        allow_any_instance_of(Season).to receive(:season_over?).and_return(true)
      end

      it 'returns an empty array with season_over flag' do
        result = described_class.fetch_matches(competition_code: 'PL')
        expect(result).to be_empty
        expect(result.instance_variable_get(:@season_over)).to be true
      end
    end

    context 'when the season is marked as completed' do
      let(:completed_season) do
        create(:season, competition:, current_matchday: 38, start_date: 6.months.ago, end_date: 1.day.ago,
                        completed: true)
      end

      before do
        competition.update(current_season: completed_season)
      end

      it 'returns an empty array with season_over flag' do
        result = described_class.fetch_matches(competition_code: 'PL')
        expect(result).to be_empty
        expect(result.instance_variable_get(:@season_over)).to be true
      end
    end

    context 'when a specific season_id is provided' do
      let(:current_season) { season }
      let(:archived_season) do
        create(:season,
               competition:,
               current_matchday: 38,
               start_date: 1.year.ago,
               end_date: 6.months.from_now) # Change to future date to avoid season_over check
      end
      let!(:archived_match) do
        create(:match,
               season: archived_season,
               matchday: 10,
               home_team: team1,
               away_team: team2,
               status: 'TIMED',
               utc_date: 2.days.from_now)
      end

      # Add teams to the archived season
      before do
        archived_season.teams << [team1, team2]
      end

      before do
        competition.update(current_season:)
      end

      it 'returns matches from the specified season instead of the current season' do
        matches = described_class.fetch_matches(competition_code: 'PL', season_id: archived_season.id)
        expect(matches.length).to eq(1)
        expect(matches.first.season_id).to eq(archived_season.id)
      end

      it 'returns matches from the current season when no season_id is provided' do
        create(:match,
               season: current_season,
               matchday: 10,
               home_team: team1,
               away_team: team2,
               status: 'TIMED',
               utc_date: 2.days.from_now)

        matches = described_class.fetch_matches(competition_code: 'PL')
        expect(matches.length).to eq(1)
        expect(matches.first.season_id).to eq(current_season.id)
      end
    end
  end
end
