require 'rails_helper'

RSpec.describe YoutubeQuotaService do
  let(:redis_double) { instance_double(Redis) }
  
  before do
    allow(RedisConnection).to receive(:redis).and_return(redis_double)
    allow(redis_double).to receive(:incrby).and_return(100)
    allow(redis_double).to receive(:expire)
    allow(redis_double).to receive(:get).and_return('100')
    allow(redis_double).to receive(:del)
  end
  
  describe '.track_api_call' do
    it 'increments the usage counter in Redis' do
      today = Date.today.to_s
      key = "youtube_api:usage:#{today}"
      
      expect(redis_double).to receive(:incrby).with(key, 50).and_return(150)
      expect(redis_double).to receive(:expire).with(key, 48.hours)
      
      result = YoutubeQuotaService.track_api_call('test_method', 50)
      expect(result).to eq(150)
    end
  end
  
  describe '.can_make_api_call?' do
    context 'when there is enough quota remaining' do
      before do
        allow(redis_double).to receive(:get).and_return('5000')
      end
      
      it 'returns true' do
        expect(YoutubeQuotaService.can_make_api_call?('test_method', 1000)).to be true
      end
    end
    
    context 'when there is not enough quota remaining' do
      before do
        allow(redis_double).to receive(:get).and_return('9500')
      end
      
      it 'returns false' do
        expect(YoutubeQuotaService.can_make_api_call?('test_method', 1000)).to be false
      end
    end
  end
  
  describe '.get_current_usage' do
    it 'returns the current usage from Redis' do
      today = Date.today.to_s
      key = "youtube_api:usage:#{today}"
      
      expect(redis_double).to receive(:get).with(key).and_return('250')
      
      expect(YoutubeQuotaService.get_current_usage).to eq(250)
    end
    
    it 'returns 0 if no usage is recorded' do
      allow(redis_double).to receive(:get).and_return(nil)
      
      expect(YoutubeQuotaService.get_current_usage).to eq(0)
    end
  end
  
  describe '.get_remaining_quota' do
    it 'returns the remaining quota' do
      allow(YoutubeQuotaService).to receive(:get_current_usage).and_return(3000)
      stub_const('YoutubeQuotaService::DAILY_QUOTA_LIMIT', 10000)
      
      expect(YoutubeQuotaService.get_remaining_quota).to eq(7000)
    end
  end
  
  describe '.reset_usage' do
    it 'deletes the usage key from Redis' do
      today = Date.today.to_s
      key = "youtube_api:usage:#{today}"
      
      expect(redis_double).to receive(:del).with(key)
      
      YoutubeQuotaService.reset_usage
    end
  end
end
