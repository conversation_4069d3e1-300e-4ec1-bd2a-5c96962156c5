require 'rails_helper'
require 'webmock/rspec'

RSpec.describe FootballDataService do
  # Create a test class that overrides the initialize method to avoid API key check
  class TestFootballDataService < FootballDataService
    def initialize
      @api_key = 'test_api_key'
      @base_url = 'https://api.football-data.org/v4'
      @cloudinary_service = CloudinaryService.new
    end
  end

  let(:service) { TestFootballDataService.new }
  let(:area) { create(:area) }
  let(:competition) { create(:competition, area:, code: 'PL', source: 'football_data', external_service_id: 2021) }
  let(:old_season) { create(:season, competition:, start_date: 1.year.ago, end_date: 1.day.ago) }
  let(:new_season_data) do
    {
      'id' => 1234,
      'startDate' => Date.today.to_s,
      'endDate' => 1.year.from_now.to_s,
      'currentMatchday' => 1,
      'winner' => nil
    }
  end
  let(:competition_data) do
    {
      'id' => 2021,
      'name' => 'Premier League',
      'code' => 'PL',
      'type' => 'LEAGUE',
      'emblem' => 'https://example.com/emblem.png',
      'currentSeason' => new_season_data,
      'area' => {
        'id' => 2072,
        'name' => 'England',
        'code' => 'ENG',
        'flag' => 'https://example.com/flag.png'
      }
    }
  end

  before do
    # Mock the API responses
    stub_request(:get, 'https://api.football-data.org/v4/competitions/PL')
      .with(headers: { 'X-Auth-Token' => 'test_api_key' })
      .to_return(status: 200, body: competition_data.to_json, headers: { 'Content-Type' => 'application/json' })

    # Mock teams API response
    stub_request(:get, 'https://api.football-data.org/v4/competitions/PL/teams')
      .with(headers: { 'X-Auth-Token' => 'test_api_key' })
      .to_return(status: 200, body: { "teams": [] }.to_json, headers: { 'Content-Type' => 'application/json' })

    # Mock matches API response
    stub_request(:get, 'https://api.football-data.org/v4/competitions/PL/matches')
      .with(headers: { 'X-Auth-Token' => 'test_api_key' })
      .to_return(status: 200, body: { "matches": [] }.to_json, headers: { 'Content-Type' => 'application/json' })

    # Mock the Cloudinary service
    allow_any_instance_of(CloudinaryService).to receive(:upload_image).and_return('cloudinary_public_id')

    # Mock URI.open
    allow(URI).to receive(:open).and_return(StringIO.new('image data'))

    # Mock the StageTransitionService
    allow_any_instance_of(StageTransitionService).to receive(:update_stage_and_matchday!).and_return(true)

    # Set up the competition with an old season
    competition.update!(current_season_id: old_season.id)
  end

  describe '#create_new_season_for_competition' do
    context 'when the season does not exist' do
      it 'creates a new season' do
        expect do
          service.create_new_season_for_competition(competition)
        end.to change(Season, :count).by(1)

        new_season = Season.last
        expect(new_season.external_service_id).to eq('1234')
        expect(new_season.source).to eq('football_data')
        expect(new_season.start_date).to eq(Date.today)
        expect(new_season.end_date).to eq(Date.parse(1.year.from_now.to_date.to_s))
        expect(new_season.current_matchday).to eq(1)

        # Check that the competition was updated to use the new season
        competition.reload
        expect(competition.current_season_id).to eq(new_season.id)
      end

      it 'calls transition_leagues_to_new_season' do
        expect(service).to receive(:transition_leagues_to_new_season)
          .with(competition, an_instance_of(Season))
          .and_return(0)

        service.create_new_season_for_competition(competition)
      end
    end

    context 'when the season already exists and is set as current season' do
      let!(:existing_season) do
        create(:season,
               competition:,
               external_service_id: '1234',
               source: 'football_data',
               start_date: Date.today,
               end_date: 1.year.from_now,
               current_matchday: 1)
      end

      before do
        competition.update!(current_season_id: existing_season.id)
      end

      it 'returns the existing season without creating a new one' do
        expect do
          result = service.create_new_season_for_competition(competition)
          expect(result).to eq(existing_season)
        end.not_to change(Season, :count)
      end

      it 'does not call transition_leagues_to_new_season' do
        expect(service).not_to receive(:transition_leagues_to_new_season)
        service.create_new_season_for_competition(competition)
      end
    end

    context 'when the API returns no current season data' do
      let(:competition_data_without_season) do
        data = competition_data.dup
        data.delete('currentSeason')
        data
      end

      before do
        # Override the stub for this specific test
        stub_request(:get, 'https://api.football-data.org/v4/competitions/PL')
          .with(headers: { 'X-Auth-Token' => 'test_api_key' })
          .to_return(status: 200, body: competition_data_without_season.to_json, headers: { 'Content-Type' => 'application/json' })
      end

      it 'raises an error' do
        expect do
          service.create_new_season_for_competition(competition)
        end.to raise_error(FootballDataService::ApiError, /Failed to fetch current season data/)
      end
    end

    context 'when the API request fails' do
      before do
        # Override the stub for this specific test
        stub_request(:get, 'https://api.football-data.org/v4/competitions/PL')
          .with(headers: { 'X-Auth-Token' => 'test_api_key' })
          .to_return(status: 404, body: '{"message": "Not found"}', headers: { 'Content-Type' => 'application/json' })
      end

      it 'raises an error' do
        expect do
          service.create_new_season_for_competition(competition)
        end.to raise_error(FootballDataService::ApiError, /Failed to fetch current season data/)
      end
    end
  end

  describe '#transition_leagues_to_new_season' do
    let(:new_season) { create(:season, competition:, start_date: Date.today, end_date: 1.year.from_now) }
    let!(:leagues) { create_list(:league, 3, competition_id: competition.id, season_id: old_season.id) }

    it 'delegates to LeagueSeasonTransitionService' do
      # Use our test service class that doesn't require API key
      service_instance = TestFootballDataService.new

      transition_service = instance_double(LeagueSeasonTransitionService)
      expect(LeagueSeasonTransitionService).to receive(:new)
        .with(competition, new_season)
        .and_return(transition_service)

      expect(transition_service).to receive(:new_season?).and_return(true)
      expect(transition_service).to receive(:transition_leagues_to_new_season).and_return(3)

      result = service_instance.send(:transition_leagues_to_new_season, competition, new_season)
      expect(result).to eq(3)
    end

    context 'when no transition is needed' do
      it 'returns 0 without calling transition_leagues_to_new_season' do
        # Use our test service class that doesn't require API key
        service_instance = TestFootballDataService.new

        transition_service = instance_double(LeagueSeasonTransitionService)
        expect(LeagueSeasonTransitionService).to receive(:new)
          .with(competition, new_season)
          .and_return(transition_service)

        expect(transition_service).to receive(:new_season?).and_return(false)
        expect(transition_service).not_to receive(:transition_leagues_to_new_season)

        result = service_instance.send(:transition_leagues_to_new_season, competition, new_season)
        expect(result).to eq(0)
      end
    end
  end
end
