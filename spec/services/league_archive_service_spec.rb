require 'rails_helper'

RSpec.describe LeagueArchiveService do
  let(:area) { create(:area) }
  let(:competition) { create(:competition, area:) }

  describe '#archive_all_leagues' do
    let(:season) { create(:season, competition:) }
    let!(:leagues) { create_list(:league, 3, competition_id: competition.id, season_id: season.id) }

    it 'archives all active leagues for a season' do
      service = LeagueArchiveService.new(season)

      expect do
        service.archive_all_leagues
      end.to change { League.archived.count }.by(3)

      expect(League.active.count).to eq(0)
    end

    it 'returns the number of archived leagues' do
      service = LeagueArchiveService.new(season)

      expect(service.archive_all_leagues).to eq(3)
    end

    it 'handles errors gracefully' do
      service = LeagueArchiveService.new(season)

      # Simulate an error when archiving
      allow_any_instance_of(League).to receive(:archive!).and_raise(StandardError.new('Test error'))

      expect do
        service.archive_all_leagues
      end.not_to raise_error

      expect(service.archive_all_leagues).to eq(0)
    end
  end

  describe '#season_completed?' do
    it 'returns true if season end date is in the past' do
      season = create(:season,
                      competition:,
                      start_date: 1.year.ago,
                      end_date: 1.day.ago)

      service = LeagueArchiveService.new(season)

      expect(service.season_completed?).to be true
    end

    it 'returns false if season end date is in the future' do
      season = create(:season,
                      competition:,
                      start_date: 1.month.ago,
                      end_date: 1.month.from_now)

      service = LeagueArchiveService.new(season)

      expect(service.season_completed?).to be false
    end

    it 'returns false if season end date is nil' do
      season = create(:season,
                      competition:,
                      start_date: 1.month.ago,
                      end_date: 1.month.from_now)

      # Manually set end_date to nil after creation to bypass validation
      season.update_column(:end_date, nil)

      service = LeagueArchiveService.new(season)

      expect(service.season_completed?).to be false
    end
  end

  describe '#all_matches_completed?' do
    let(:season) { create(:season, competition:) }

    it 'returns true if all matches are completed' do
      create(:match, season:, status: 'FINISHED')
      create(:match, season:, status: 'FINISHED')

      service = LeagueArchiveService.new(season)

      expect(service.all_matches_completed?).to be true
    end

    it 'returns true if all matches are completed, postponed, or cancelled' do
      create(:match, season:, status: 'FINISHED')
      create(:match, season:, status: 'POSTPONED')
      create(:match, season:, status: 'CANCELLED')

      service = LeagueArchiveService.new(season)

      expect(service.all_matches_completed?).to be true
    end

    it 'returns false if any match is not completed' do
      create(:match, season:, status: 'FINISHED')
      create(:match, season:, status: 'SCHEDULED')

      service = LeagueArchiveService.new(season)

      expect(service.all_matches_completed?).to be false
    end
  end

  describe '#archive_leagues_if_season_completed' do
    it 'archives leagues if season is completed and all matches are completed' do
      season = create(:season,
                      competition:,
                      start_date: 1.year.ago,
                      end_date: 1.day.ago)

      create_list(:league, 2, competition_id: competition.id, season_id: season.id)
      create(:match, season:, status: 'FINISHED')

      service = LeagueArchiveService.new(season)

      expect do
        service.archive_leagues_if_season_completed
      end.to change { League.archived.count }.by(2)
    end

    it 'does not archive leagues if season is not completed' do
      season = create(:season,
                      competition:,
                      start_date: 1.month.ago,
                      end_date: 1.month.from_now)

      create_list(:league, 2, competition_id: competition.id, season_id: season.id)

      service = LeagueArchiveService.new(season)

      expect do
        service.archive_leagues_if_season_completed
      end.not_to(change { League.archived.count })
    end

    it 'does not archive leagues if matches are not completed' do
      season = create(:season,
                      competition:,
                      start_date: 1.year.ago,
                      end_date: 1.day.ago)

      create_list(:league, 2, competition_id: competition.id, season_id: season.id)
      create(:match, season:, status: 'SCHEDULED')

      service = LeagueArchiveService.new(season)

      expect do
        service.archive_leagues_if_season_completed
      end.not_to(change { League.archived.count })
    end
  end
end
