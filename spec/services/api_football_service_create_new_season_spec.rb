require 'rails_helper'
require 'webmock/rspec'

RSpec.describe ApiFootballService do
  let(:service) { described_class.new }
  let(:area) { create(:area) }
  let(:competition) { create(:competition, area:, code: 'SV', source: 'api_football', external_service_id: 113) }
  let(:old_season) { create(:season, competition:, start_date: 1.year.ago, end_date: 1.day.ago) }

  let(:league_response) do
    {
      'response' => [
        {
          'league' => {
            'id' => 113,
            'name' => 'Superliga',
            'type' => 'League',
            'logo' => 'https://example.com/logo.png'
          },
          'country' => {
            'name' => 'Iceland',
            'code' => 'IS',
            'flag' => 'https://example.com/flag.png'
          },
          'seasons' => [
            {
              'year' => 2025,
              'start' => Date.today.to_s,
              'end' => 1.year.from_now.to_s,
              'current' => true,
              'coverage' => {}
            }
          ]
        }
      ]
    }
  end

  before do
    # Mock the API responses
    stub_request(:get, 'https://v3.football.api-sports.io/leagues?id=113&season=2025')
      .to_return(status: 200, body: league_response.to_json, headers: { 'Content-Type' => 'application/json' })

    # Mock the Cloudinary service
    allow_any_instance_of(CloudinaryService).to receive(:upload_image).and_return('cloudinary_public_id')

    # Mock URI.open
    allow(URI).to receive(:open).and_return(StringIO.new('image data'))

    # Set up the competition with an old season
    competition.update!(current_season_id: old_season.id)

    # Mock other API calls that might be made during the process
    stub_request(:get, 'https://v3.football.api-sports.io/teams?league=113&season=2025')
      .to_return(status: 200, body: { 'response' => [] }.to_json, headers: { 'Content-Type' => 'application/json' })

    stub_request(:get, 'https://v3.football.api-sports.io/fixtures?league=113&season=2025')
      .to_return(status: 200, body: { 'response' => [] }.to_json, headers: { 'Content-Type' => 'application/json' })
  end

  describe '#create_new_season_for_competition' do
    context 'when the season does not exist' do
      it 'creates a new season with provided season ID' do
        expect do
          service.create_new_season_for_competition(competition, '2025')
        end.to change(Season, :count).by(1)

        new_season = Season.last
        expect(new_season.external_service_id).to eq('2025')
        expect(new_season.source).to eq('api_football')
        expect(new_season.start_date).to eq(Date.today)
        expect(new_season.end_date).to eq(Date.parse(1.year.from_now.to_date.to_s))
        expect(new_season.current_matchday).to eq(1)

        # Check that the competition was updated to use the new season
        competition.reload
        expect(competition.current_season_id).to eq(new_season.id)
      end

      it 'creates a new season with specified season ID' do
        # Add a stub for the specific season ID
        stub_request(:get, 'https://v3.football.api-sports.io/leagues?id=113&season=2026')
          .to_return(status: 200, body: league_response.to_json, headers: { 'Content-Type' => 'application/json' })

        # Mock other API calls for the new season
        stub_request(:get, 'https://v3.football.api-sports.io/teams?league=113&season=2026')
          .to_return(status: 200, body: { 'response' => [] }.to_json, headers: { 'Content-Type' => 'application/json' })

        stub_request(:get, 'https://v3.football.api-sports.io/fixtures?league=113&season=2026')
          .to_return(status: 200, body: { 'response' => [] }.to_json, headers: { 'Content-Type' => 'application/json' })

        expect do
          service.create_new_season_for_competition(competition, '2026')
        end.to change(Season, :count).by(1)
      end

      it 'calls transition_leagues_to_new_season' do
        expect(service).to receive(:transition_leagues_to_new_season)
          .with(competition, an_instance_of(Season))
          .and_return(0)

        service.create_new_season_for_competition(competition, '2025')
      end
    end

    context 'when the season already exists and is set as current season' do
      let!(:existing_season) do
        create(:season,
               competition:,
               external_service_id: '2025',
               source: 'api_football',
               start_date: Date.today,
               end_date: 1.year.from_now,
               current_matchday: 1)
      end

      before do
        competition.update!(current_season_id: existing_season.id)
      end

      it 'returns the existing season without creating a new one' do
        expect do
          result = service.create_new_season_for_competition(competition, '2025')
          expect(result).to eq(existing_season)
        end.not_to change(Season, :count)
      end

      it 'does not call transition_leagues_to_new_season' do
        expect(service).not_to receive(:transition_leagues_to_new_season)
        service.create_new_season_for_competition(competition, '2025')
      end
    end

    context 'when the API returns no season data' do
      let(:league_response_without_seasons) do
        response = league_response.dup
        response['response'][0]['seasons'] = []
        response
      end

      before do
        stub_request(:get, 'https://v3.football.api-sports.io/leagues?id=113&season=2025')
          .to_return(status: 200, body: league_response_without_seasons.to_json, headers: { 'Content-Type' => 'application/json' })
      end

      it 'raises an error' do
        expect do
          service.create_new_season_for_competition(competition, '2025')
        end.to raise_error(ApiFootballService::ApiError, /Failed to fetch season data/)
      end
    end

    context 'when the API request fails' do
      before do
        stub_request(:get, 'https://v3.football.api-sports.io/leagues?id=113&season=2025')
          .to_return(status: 404, body: '{"message": "Not found"}', headers: { 'Content-Type' => 'application/json' })
      end

      it 'raises an error' do
        expect do
          service.create_new_season_for_competition(competition, '2025')
        end.to raise_error(ApiFootballService::ApiError, /Failed to create new season|Failed to fetch season data/)
      end
    end
  end

  describe '#transition_leagues_to_new_season' do
    let(:new_season) { create(:season, competition:, start_date: Date.today, end_date: 1.year.from_now) }
    let!(:leagues) { create_list(:league, 3, competition_id: competition.id, season_id: old_season.id) }

    it 'delegates to LeagueSeasonTransitionService' do
      transition_service = instance_double(LeagueSeasonTransitionService)
      expect(LeagueSeasonTransitionService).to receive(:new)
        .with(competition, new_season)
        .and_return(transition_service)

      expect(transition_service).to receive(:new_season?).and_return(true)
      expect(transition_service).to receive(:transition_leagues_to_new_season).and_return(3)

      result = service.send(:transition_leagues_to_new_season, competition, new_season)
      expect(result).to eq(3)
    end

    context 'when no transition is needed' do
      it 'returns 0 without calling transition_leagues_to_new_season' do
        transition_service = instance_double(LeagueSeasonTransitionService)
        expect(LeagueSeasonTransitionService).to receive(:new)
          .with(competition, new_season)
          .and_return(transition_service)

        expect(transition_service).to receive(:new_season?).and_return(false)
        expect(transition_service).not_to receive(:transition_leagues_to_new_season)

        result = service.send(:transition_leagues_to_new_season, competition, new_season)
        expect(result).to eq(0)
      end
    end
  end
end
