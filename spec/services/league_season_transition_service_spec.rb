require 'rails_helper'

RSpec.describe LeagueSeasonTransitionService do
  let(:area) { create(:area) }
  let(:competition) { create(:competition, area:) }
  let(:old_season) { create(:season, competition:, start_date: 1.year.ago, end_date: 1.day.ago) }
  let(:new_season) { create(:season, competition:, start_date: Date.today, end_date: 1.year.from_now) }

  before do
    competition.update!(current_season_id: new_season.id)
  end

  describe '#transition_leagues_to_new_season' do
    let!(:leagues) { create_list(:league, 3, competition_id: competition.id, season_id: old_season.id) }

    it 'updates all active leagues to use the new season' do
      service = LeagueSeasonTransitionService.new(competition, new_season)

      expect do
        service.transition_leagues_to_new_season
      end.to change { League.where(season_id: new_season.id).count }.from(0).to(3)

      expect(League.where(season_id: old_season.id).count).to eq(0)
    end

    it 'returns the number of transitioned leagues' do
      service = LeagueSeasonTransitionService.new(competition, new_season)

      expect(service.transition_leagues_to_new_season).to eq(3)
    end

    it 'stores league season data for the old season' do
      # Create a user with some predictions in the old season
      user = create(:user)
      league = leagues.first
      league.users << user

      # Create some round predictions for the user in the old season
      round_prediction = create(:round_prediction, user:, season_id: old_season.id, competition_id: competition.id)
      create(:match_prediction, round_prediction:, points: 3)
      create(:match_prediction, round_prediction:, points: 1)
      create(:match_prediction, round_prediction:, points: 0)

      service = LeagueSeasonTransitionService.new(competition, new_season)
      service.transition_leagues_to_new_season

      # Check that a league_season record was created
      league_season = LeagueSeason.find_by(league:, season: old_season, user:)
      expect(league_season).to be_present
      expect(league_season.points).to be > 0
      expect(league_season.perfect_predictions).to eq(1)
      expect(league_season.correct_predictions).to eq(1)
      expect(league_season.incorrect_predictions).to eq(1)
    end

    it 'archives leagues from the old season' do
      # Create a mock LeagueArchiveService
      archive_service = instance_double(LeagueArchiveService)
      expect(LeagueArchiveService).to receive(:new).with(old_season).and_return(archive_service)
      expect(archive_service).to receive(:archive_all_leagues).and_return(3)

      service = LeagueSeasonTransitionService.new(competition, new_season)
      service.transition_leagues_to_new_season
    end
  end

  describe '#new_season?' do
    context 'when leagues need to be transitioned' do
      let!(:leagues) { create_list(:league, 2, competition_id: competition.id, season_id: old_season.id) }

      it 'returns true' do
        service = LeagueSeasonTransitionService.new(competition, new_season)
        expect(service.new_season?).to be true
      end
    end

    context 'when leagues are already using the new season' do
      let!(:leagues) { create_list(:league, 2, competition_id: competition.id, season_id: new_season.id) }

      it 'returns false' do
        service = LeagueSeasonTransitionService.new(competition, new_season)
        expect(service.new_season?).to be false
      end
    end
  end
end
