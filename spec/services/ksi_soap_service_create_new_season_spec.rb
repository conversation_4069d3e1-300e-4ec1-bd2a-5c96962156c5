require 'rails_helper'
require 'webmock/rspec'

RSpec.describe KsiSoapService do
  let(:service) { described_class.new }
  let(:area) { create(:area, name: 'Iceland', code: 'IS', source: 'ksi_soap') }
  let(:competition) { create(:competition, area:, code: 'BDkk', source: 'ksi_soap', gender: 'male') }
  let(:old_season) { create(:season, competition:, start_date: 1.year.ago, end_date: 1.day.ago, source: 'ksi_soap') }
  let(:season_external_service_id) { '50000' }
  let(:endpoint) { 'http://www2.ksi.is/vefthjonustur/mot.asmx' }

  before do
    # Stub the WSDL request that <PERSON><PERSON> makes first
    stub_request(:get, "#{endpoint}?WSDL")
      .to_return(
        status: 200,
        body: File.read(Rails.root.join('spec/fixtures/soap_responses/ksi_wdsl.xml')),
        headers: { 'Content-Type' => 'text/xml' }
      )

    # Stub the actual SOAP request to fetch tournament matches
    stub_request(:post, endpoint)
      .with(
        headers: { 'Content-Type' => %r{application/soap\+xml} },
        body: %r{<tns:mot_leikir><tns:MotNumer>#{season_external_service_id}</tns:MotNumer></tns:mot_leikir>}
      )
      .to_return(
        status: 200,
        body: File.read(Rails.root.join('spec/fixtures/soap_responses/mot_leikir_response.xml')),
        headers: { 'Content-Type' => 'text/xml' }
      )

    # Set up the competition with an old season
    competition.update!(current_season_id: old_season.id)

    # Mock the Cloudinary service
    allow_any_instance_of(CloudinaryService).to receive(:upload_image).and_return('cloudinary_public_id')
  end

  describe '#create_new_season_for_competition' do
    context 'when the season does not exist' do
      it 'creates a new season' do
        expect do
          service.create_new_season_for_competition(competition, season_external_service_id)
        end.to change(Season, :count).by(1)

        new_season = Season.last
        expect(new_season.external_service_id).to eq(season_external_service_id)
        expect(new_season.source).to eq('ksi_soap')
        expect(new_season.start_date).to eq(Date.parse('2025-04-05'))
        expect(new_season.end_date).to eq(Date.parse('2025-09-14'))
        expect(new_season.current_matchday).to eq(1)

        # Check that the competition was updated to use the new season
        competition.reload
        expect(competition.current_season_id).to eq(new_season.id)
      end

      it 'calls transition_leagues_to_new_season' do
        expect(service).to receive(:transition_leagues_to_new_season)
          .with(competition, an_instance_of(Season))
          .and_return(0)

        service.create_new_season_for_competition(competition, season_external_service_id)
      end
    end

    context 'when the season already exists and is set as current season' do
      let!(:existing_season) do
        create(:season,
               competition:,
               external_service_id: season_external_service_id,
               source: 'ksi_soap',
               start_date: Date.parse('2025-04-05'),
               end_date: Date.parse('2025-09-14'),
               current_matchday: 1)
      end

      before do
        competition.update!(current_season_id: existing_season.id)
      end

      it 'returns the existing season without creating a new one' do
        expect do
          result = service.create_new_season_for_competition(competition, season_external_service_id)
          expect(result).to eq(existing_season)
        end.not_to change(Season, :count)
      end

      it 'does not call transition_leagues_to_new_season' do
        expect(service).not_to receive(:transition_leagues_to_new_season)
        service.create_new_season_for_competition(competition, season_external_service_id)
      end
    end

    context 'when the API returns no match data' do
      before do
        # Stub the SOAP request to return an empty response
        stub_request(:post, endpoint)
          .with(
            headers: { 'Content-Type' => %r{application/soap\+xml} },
            body: %r{<tns:mot_leikir><tns:MotNumer>#{season_external_service_id}</tns:MotNumer></tns:mot_leikir>}
          )
          .to_return(
            status: 200,
            body: '<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope"><soap:Body><mot_leikir_svar xmlns="http://www2.ksi.is/vefthjonustur/mot.asmx"></mot_leikir_svar></soap:Body></soap:Envelope>',
            headers: { 'Content-Type' => 'text/xml' }
          )
      end

      it 'raises an error' do
        expect do
          service.create_new_season_for_competition(competition, season_external_service_id)
        end.to raise_error(/Failed to fetch match data for season|No match data returned for season/)
      end
    end
  end

  describe '#transition_leagues_to_new_season' do
    let(:new_season) do
      create(:season, competition:, start_date: Date.today, end_date: 1.year.from_now, source: 'ksi_soap')
    end
    let!(:leagues) { create_list(:league, 3, competition_id: competition.id, season_id: old_season.id) }

    it 'delegates to LeagueSeasonTransitionService' do
      transition_service = instance_double(LeagueSeasonTransitionService)
      expect(LeagueSeasonTransitionService).to receive(:new)
        .with(competition, new_season)
        .and_return(transition_service)

      expect(transition_service).to receive(:new_season?).and_return(true)
      expect(transition_service).to receive(:transition_leagues_to_new_season).and_return(3)

      result = service.send(:transition_leagues_to_new_season, competition, new_season)
      expect(result).to eq(3)
    end

    context 'when no transition is needed' do
      it 'returns 0 without calling transition_leagues_to_new_season' do
        transition_service = instance_double(LeagueSeasonTransitionService)
        expect(LeagueSeasonTransitionService).to receive(:new)
          .with(competition, new_season)
          .and_return(transition_service)

        expect(transition_service).to receive(:new_season?).and_return(false)
        expect(transition_service).not_to receive(:transition_leagues_to_new_season)

        result = service.send(:transition_leagues_to_new_season, competition, new_season)
        expect(result).to eq(0)
      end
    end
  end
end
