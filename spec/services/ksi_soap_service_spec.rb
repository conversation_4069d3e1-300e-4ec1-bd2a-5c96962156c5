# rubocop:disable Metrics/BlockLength
require 'rails_helper'
require 'webmock/rspec'

RSpec.describe KsiSoapService do
  let(:service) { described_class.new }

  xdescribe '#get_matches' do
    it 'fetches matches for a team' do
      stub_request(:post, /www2.ksi.is/).to_return(
        body: File.read('spec/fixtures/soap_responses/felog_leikir.xml'),
        headers: { 'Content-Type' => 'text/xml' }
      )

      matches = service.get_matches(101, '2025-04-01', '2025-04-30')
      expect(matches).to be_an(Array)
      expect(matches.first).to include(:leikur_numer, :felag_heima_numer, :felag_uti_numer)
    end
  end

  xdescribe '#get_players' do
    it 'fetches players for a match' do
      stub_request(:post, /www2.ksi.is/).to_return(
        body: File.read('spec/fixtures/soap_responses/leikur_leikmenn.xml'),
        headers: { 'Content-Type' => 'text/xml' }
      )

      players = service.get_players(12_345)
      expect(players).to be_an(Array)
      expect(players.first).to include(:leikmadur_numer, :nafn, :felag)
    end
  end

  describe 'Setup competition' do
    let(:service) { described_class.new }
    let(:competition_code) { 'BDkk' } # Male competition code
    let(:gender) { 'male' }
    let(:endpoint) { 'http://www2.ksi.is/vefthjonustur/mot.asmx' }
    let(:response_fixture_path) { Rails.root.join('spec/fixtures/soap_responses/mot_leikir_response.xml') }
    let(:tournament_number) { '49315' }

    describe '#import_competition' do
      before do
        # Stub the WSDL request that Savon makes first
        stub_request(:get, "#{endpoint}?WSDL")
          .to_return(
            status: 200,
            body: File.read(Rails.root.join('spec/fixtures/soap_responses/ksi_wdsl.xml')),
            headers: { 'Content-Type' => 'text/xml' }
          )

        # Stub the actual SOAP request to fetch tournament matches
        # This is the key addition to fix your test
        stub_request(:post, endpoint)
          .with(
            headers: { 'Content-Type' => %r{application/soap\+xml} },
            body: %r{<tns:mot_leikir><tns:MotNumer>#{tournament_number}</tns:MotNumer></tns:mot_leikir>}
          )
          .to_return(
            status: 200,
            body: File.read(Rails.root.join('spec/fixtures/soap_responses/mot_leikir_response.xml')),
            headers: { 'Content-Type' => 'text/xml' }
          )

        # Allow file writing for the debug output
        allow(File).to receive(:write).and_return(true)

        # 49_315
        # Mock the tournament_matches method to return our mock data
        service.setup_service('BDkk', 'male', 49_315)
      end

      it 'creates a new competition' do
        # expect { service.setup_service }.to change(Competition, :count).by(1)
        competition = Competition.last
        expect(competition.name).not_to be_nil
        expect(competition.code).to eq(competition_code)
        expect(competition.source).to eq('ksi_soap')
        expect(competition.area.name).to eq('Iceland')
      end

      it 'creates the Iceland area' do
        # expect { service.setup_service }.to change(Area, :count).by(1)
        area = Area.last
        expect(area.name).to eq('Iceland')
        expect(area.code).to eq('IS')
        expect(area.source).to eq('ksi_soap')
      end

      it 'creates a new season' do
        # expect { service.setup_service }.to change(Season, :count).by(1)
        expect(Season.count).to eq(1)
        season = Season.last
        expect(season.competition).to eq(Competition.last)
        expect(season.source).to eq('ksi_soap')
        expect(season.start_date).to eq(Date.parse('2025-04-05'))
        expect(season.end_date).to eq(Date.parse('2025-09-14'))
      end

      it 'creates 10 teams' do
        # expect { service.setup_service }.to change(Team, :count).by(10)

        teams = Team.all
        expect(teams.count).to eq(12)
        expect(teams.pluck(:source).uniq).to eq(['ksi_soap'])
      end

      it 'creates 132 matches' do
        # Each team plays against 11 others, home and away = 12*11 matches total
        # expect { service.setup_service }.to change(Match, :count).by(132)

        matches = Match.all
        expect(matches.pluck(:source).uniq).to eq(['ksi_soap'])
        expect(matches.pluck(:winner).uniq).to eq([nil])
        expect(matches.count).to eq(132)
        expect(matches.pluck(:status).uniq).to eq(['TIMED'])
        # Verify home and away matchups exist for all teams
        # Get all teams
        teams = Team.all
        team_ids = teams.pluck(:id)

        # Fetch all matches in a single query and index them by team pairs
        matches_by_teams = {}
        Match.where(home_team_id: team_ids, away_team_id: team_ids).each do |match|
          key = [match.home_team_id, match.away_team_id]
          matches_by_teams[key] ||= 0
          matches_by_teams[key] += 1
        end

        # Verify all combinations exist
        teams.each do |home_team|
          teams.each do |away_team|
            next if home_team.id == away_team.id

            home_away_key = [home_team.id, away_team.id]
            away_home_key = [away_team.id, home_team.id]

            # Check home_team vs away_team match
            expect(matches_by_teams[home_away_key]).to eq(1),
                                                       "Expected one match with #{home_team.name} (home) vs #{away_team.name} (away), but found #{matches_by_teams[home_away_key] || 0}" # rubocop:disable Layout/LineLength

            # Check away_team vs home_team match (reverse fixture)
            expect(matches_by_teams[away_home_key]).to eq(1),
                                                       "Expected one match with #{away_team.name} (home) vs #{home_team.name} (away), but found #{matches_by_teams[away_home_key] || 0}" # rubocop:disable Layout/LineLength
          end
        end
      end

      it 'links teams to the season' do
        season = Season.last

        expect(season.teams.count).to eq(12)
      end

      xcontext 'when a competition already exists' do
        let!(:existing_competition) do
          Competition.create!(
            name: 'Old Name',
            code: competition_code,
            area: Area.create!(name: 'Iceland', code: 'IS', source: 'ksi_soap'),
            source: 'ksi_soap'
          )
        end

        it 'updates the existing competition rather than creating a new one' do
          expect do
            service.import_competition(competition_code, gender)
          end.not_to change(Competition, :count)

          existing_competition.reload
          expect(existing_competition.name).not_to eq('Old Name')
        end
      end

      xcontext 'when a match already exists' do
        let!(:existing_match) do
          # Create a season, teams, and a match that will match one from our import
          season = Season.create!(
            competition: Competition.create!(
              name: 'Existing Comp',
              code: competition_code,
              area: Area.create!(name: 'Iceland', code: 'IS', source: 'ksi_soap'),
              source: 'ksi_soap'
            ),
            source: 'ksi_soap'
          )

          home_team = Team.create!(
            name: mock_teams.first[:name],
            external_service_id: mock_teams.first[:id],
            area: Area.last,
            source: 'ksi_soap'
          )

          away_team = Team.create!(
            name: mock_teams.second[:name],
            external_service_id: mock_teams.second[:id],
            area: Area.last,
            source: 'ksi_soap'
          )

          Match.create!(
            external_service_id: "#{mock_teams.first[:id]}#{mock_teams.second[:id]}1",
            season:,
            home_team:,
            away_team:,
            status: 'OLD_STATUS',
            source: 'ksi_soap'
          )
        end

        it 'updates the existing match rather than creating a new one' do
          match_count_before = Match.count

          service.import_competition(competition_code, gender)

          # We should have 90 total matches: 89 new ones + 1 updated
          expect(Match.count).to eq(90)
          expect(Match.count - match_count_before).to eq(89)

          # Check the existing match got updated
          existing_match.reload
          expect(existing_match.status).not_to eq('OLD_STATUS')
        end
      end
    end
  end

  describe '#map_match_data' do # rubocop:disable Metrics/BlockLength
    let(:raw_match_data_unplayed) do
      { 'leikur_numer' => '687703',
        'umferd_numer' => '1',
        'leik_dagur' => '2025-04-05T19:15:00.000+00:00',
        'felag_heima_nafn' => 'Breiðablik',
        'felag_heima_numer' => '200',
        'felag_uti_nafn' => 'Afturelding',
        'felag_uti_numer' => '270',
        'urslit_heima' => nil,
        'urslit_uti' => nil,
        'stada_fyrri_halfleik_heima' => nil,
        'stada_fyrri_halfleik_uti' => nil,
        'stada_seinni_halfleik_heima' => nil,
        'stada_seinni_half_leik_uti' => nil,
        'skyrsla_stada' => nil,
        'vollur_nafn' => 'Kópavogsvöllur',
        'ahorfendur' => nil,
        'vollur_numer' => '52' }
    end

    let(:raw_match_data_played) do
      {
        "leikur_numer": '638239',
        "umferd_numer": '22',
        "leik_dagur": '2024-09-16T19:15:00.000+00:00',
        "felag_heima_nafn": 'Valur',
        "felag_heima_numer": '101',
        "felag_uti_nafn": 'KR',
        "felag_uti_numer": '107',
        "urslit_heima": '4',
        "urslit_uti": '1',
        "stada_fyrri_halfleik_heima": '2',
        "stada_fyrri_halfleik_uti": '0',
        "stada_seinni_halfleik_heima": '2',
        "stada_seinni_half_leik_uti": '1',
        "skyrsla_stada": 'S',
        "vollur_nafn": 'N1-völlurinn Hlíðarenda',
        "ahorfendur": '785',
        "vollur_numer": '8421'
      }
    end

    it 'maps raw unplayed match data to a structured data object' do
      mapped_data = service.map_match_data(raw_match_data_unplayed)

      expect(mapped_data).to include(
        external_service_id: 687_703,
        matchday: 1,
        date: '2025-04-05T19:15:00.000+00:00',
        status: 'TIMED',
        home_team: {
          name: 'Breiðablik',
          external_service_id: 200,
          venue: 'Kópavogsvöllur'
        },
        away_team: {
          name: 'Afturelding',
          external_service_id: 270
        },
        scores: {
          fullTimeAway: nil,
          halfTimeAway: nil,
          fullTimeHome: nil,
          halfTimeHome: nil
        },
        venue: 'Kópavogsvöllur'
      )
    end

    it 'maps raw played match data to a structured data object' do
      mapped_data = service.map_match_data(raw_match_data_played)

      expect(mapped_data).to include(
        external_service_id: 638_239,
        matchday: 22,
        date: '2024-09-16T19:15:00.000+00:00',
        status: 'FINISHED',
        home_team: {
          name: 'Valur',
          external_service_id: 101,
          venue: 'N1-völlurinn Hlíðarenda'
        },
        away_team: {
          name: 'KR',
          external_service_id: 107
        },
        scores: {
          fullTimeAway: 1,
          halfTimeAway: 0,
          fullTimeHome: 4,
          halfTimeHome: 2
          # winner: 'HOME_TEAM'
        },
        venue: 'N1-völlurinn Hlíðarenda'
      )
    end
  end

  describe '#update_matches' do
    let(:service) { described_class.new }
    let(:competition_code) { 'BDkk' } # Male competition code
    let(:gender) { 'male' }
    let(:endpoint) { 'http://www2.ksi.is/vefthjonustur/mot.asmx' }
    let(:response_fixture_path) do
      Rails.root.join('spec/fixtures/soap_responses/mot_leikir_response_1_leikur_buinn.xml')
    end
    let(:tournament_number) { '49315' }

    before do
      # Stub the WSDL request that Savon makes first
      stub_request(:get, "#{endpoint}?WSDL")
        .to_return(
          status: 200,
          body: File.read(Rails.root.join('spec/fixtures/soap_responses/ksi_wdsl.xml')),
          headers: { 'Content-Type' => 'text/xml' }
        )

      # Stub the actual SOAP request to fetch tournament matches
      # This is the key addition to fix your test
      stub_request(:post, endpoint)
        .with(
          headers: { 'Content-Type' => %r{application/soap\+xml} },
          body: %r{<tns:mot_leikir><tns:MotNumer>#{tournament_number}</tns:MotNumer></tns:mot_leikir>}
        )
        .to_return(
          status: 200,
          body: File.read(Rails.root.join('spec/fixtures/soap_responses/mot_leikir_response_1_leikur_buinn.xml')),
          headers: { 'Content-Type' => 'text/xml' }
        )

      # Allow file writing for the debug output
      allow(File).to receive(:write).and_return(true)

      # 49_315
      # Mock the tournament_matches method to return our mock data
      service.setup_service('BDkk', 'male', 49_315)
    end

    it 'updates the match between Breiðablik and Afturelding' do
      match = Match.find_by(external_service_id: 687_703)
      expect(match).not_to be_nil
      expect(match.status).to eq('FINISHED')
      expect(match.score.halfTimeHome).to eq(2)
      expect(match.score.halfTimeAway).to eq(0)
      expect(match.score.fullTimeHome).to eq(2)
      expect(match.score.fullTimeAway).to eq(0)
      expect(match.score.winner).to eq('HOME_TEAM')
      expect(match.utc_date).to eq('2025-04-05T19:15:00.000+00:00')
      expect(match.matchday).to eq(1)
      expect(match.home_team.external_service_id).to eq('200')
      expect(match.away_team.external_service_id).to eq('270')
    end
  end
end
# rubocop:enable Metrics/BlockLength

# Match response from KSI SOAP API
# This is a sample response from the KSI SOAP API tournament_matches for a match.
# leikur_numer  -> external_service_id
# umferd_numer  -> matchday
# leik_dagur  -> date in GMT
# felag_heima_nafn  -> home team name
# felag_heima_numer  -> home team external_service_id
# felag_uti_nafn  -> away team name
# felag_uti_numer  -> away team external_service_id
# urslit_heima  -> home team score
# urslit_uti  -> away team score
# stada_fyrri_halfleik_heima  -> home team first half score
# stada_fyrri_halfleik_uti  -> away team first half score
# stada_seinni_halfleik_heima  -> home team second half score
# stada_seinni_half_leik_uti  -> away team second half score
# skyrsla_stada  -> match report (URL). We dont use this field
# vollur_nafn  -> stadium name
# ahorfendur  -> spectators count. We dont use this field
# vollur_numer  -> stadium external_service_id We dont use this field

# { "leikur_numer": '687703',
#   "umferd_numer": '1',
#   "leik_dagur": '2025-04-05T19:15:00.000+00:00',
#   "felag_heima_nafn": 'Breiðablik',
#   "felag_heima_numer": '200',
#   "felag_uti_nafn": 'Afturelding',
#   "felag_uti_numer": '270',
#   "urslit_heima": null,
#   "urslit_uti": null,
#   "stada_fyrri_halfleik_heima": null,
#   "stada_fyrri_halfleik_uti": null,
#   "stada_seinni_halfleik_heima": null,
#   "stada_seinni_half_leik_uti": null,
#   "skyrsla_stada": null,
#   "vollur_nafn": 'Kópavogsvöllur',
#   "ahorfendur": null,
#   "vollur_numer": '52' }

# Example of a match response from KSI SOAP API with scores
# {
#   "leikur_numer": '638239',
#   "umferd_numer": '22',
#   "leik_dagur": '2024-09-16T19:15:00.000+00:00',
#   "felag_heima_nafn": 'Valur',
#   "felag_heima_numer": '101',
#   "felag_uti_nafn": 'KR',
#   "felag_uti_numer": '107',
#   "urslit_heima": '4',
#   "urslit_uti": '1',
#   "stada_fyrri_halfleik_heima": '2',
#   "stada_fyrri_halfleik_uti": '0',
#   "stada_seinni_halfleik_heima": '2',
#   "stada_seinni_half_leik_uti": '1',
#   "skyrsla_stada": 'S',
#   "vollur_nafn": 'N1-völlurinn Hlíðarenda',
#   "ahorfendur": '785',
#   "vollur_numer": '8421'
# }
