require 'rails_helper'

RSpec.describe YoutubeTokenService do
  include YoutubeTokenServiceTestHelper

  # We'll stub the environment variables directly in the tests that need them

  let(:user) { YoutubeTokenServiceTestHelper::MockUser.new }
  let(:credentials) do
    {
      'access_token' => 'ya29.a0AfB_test_token',
      'refresh_token' => '1//04dK_test_refresh_token',
      'expires_at' => (Time.now.to_i + 3600).to_s,
      'token_type' => 'Bearer',
      'scope' => 'https://www.googleapis.com/auth/youtube.readonly'
    }
  end

  describe '.ensure_fresh_token' do
    context 'when user has no YouTube credentials' do
      it 'returns nil' do
        expect(YoutubeTokenService.ensure_fresh_token(user)).to be_nil
      end
    end

    context 'when user has valid non-expired credentials' do
      before do
        user.update(youtube_credentials: credentials.to_json)
      end

      it 'returns the access token without refreshing' do
        expect(YoutubeTokenService).not_to receive(:refresh_token)
        expect(YoutubeTokenService.ensure_fresh_token(user)).to eq(credentials['access_token'])
      end
    end

    context 'when user has expired credentials' do
      let(:expired_credentials) do
        credentials.merge('expires_at' => (Time.now.to_i - 60).to_s)
      end

      before do
        user.update(youtube_credentials: expired_credentials.to_json)
      end

      it 'attempts to refresh the token' do
        expect(YoutubeTokenService).to receive(:refresh_token).with(user, anything)
        YoutubeTokenService.ensure_fresh_token(user)
      end
    end

    context 'when credentials are invalid JSON' do
      before do
        user.update(youtube_credentials: 'invalid json')
      end

      it 'returns nil and logs an error' do
        expect(Rails.logger).to receive(:error).with(/Error parsing YouTube credentials/).at_least(:once)
        expect(Rails.logger).to receive(:error).with(any_args).at_least(:once)
        expect(YoutubeTokenService.ensure_fresh_token(user)).to be_nil
      end
    end
  end

  describe '.store_credentials' do
    let(:auth_data) do
      {
        access_token: 'new_access_token',
        refresh_token: 'new_refresh_token',
        expires_in: 3600,
        token_type: 'Bearer',
        scope: 'https://www.googleapis.com/auth/youtube.readonly'
      }
    end

    it 'formats and stores credentials as JSON' do
      YoutubeTokenService.store_credentials(user, auth_data)

      stored_credentials = JSON.parse(user.reload.youtube_credentials)
      expect(stored_credentials['access_token']).to eq('new_access_token')
      expect(stored_credentials['refresh_token']).to eq('new_refresh_token')
      expect(stored_credentials['token_type']).to eq('Bearer')
      expect(stored_credentials['scope']).to eq('https://www.googleapis.com/auth/youtube.readonly')

      # Check that expires_at is calculated correctly (within 5 seconds tolerance)
      expected_expiry = Time.now.to_i + 3600
      expect(stored_credentials['expires_at'].to_i).to be_within(5).of(expected_expiry)
    end
  end

  describe '.revoke_tokens' do
    before do
      user.update(youtube_credentials: credentials.to_json)

      # Mock HTTP requests
      allow(HTTP).to receive(:post).and_return(double(status: double(success?: true)))
    end

    it 'makes revocation requests for both tokens' do
      expect(HTTP).to receive(:post).with(
        'https://oauth2.googleapis.com/revoke',
        form: { token: credentials['access_token'] }
      )

      expect(HTTP).to receive(:post).with(
        'https://oauth2.googleapis.com/revoke',
        form: { token: credentials['refresh_token'] }
      )

      YoutubeTokenService.revoke_tokens(user)
    end

    context 'when an error occurs' do
      before do
        allow(HTTP).to receive(:post).and_raise(StandardError.new('Network error'))
      end

      it 'logs the error and returns false' do
        expect(Rails.logger).to receive(:error).with(/Error revoking YouTube tokens/).at_least(:once)
        expect(Rails.logger).to receive(:error).with(any_args).at_least(:once)
        expect(YoutubeTokenService.revoke_tokens(user)).to be false
      end
    end
  end

  describe '.refresh_token' do
    let(:new_token_response) do
      {
        'access_token' => 'new_access_token',
        'expires_in' => 3600
      }
    end

    before do
      # Make sure user has credentials to update
      user.update(youtube_credentials: credentials.to_json)
    end

    context 'with valid environment variables' do
      before do
        # Store original ENV values
        @original_client_id = ENV['GOOGLE_CLIENT_ID']
        @original_client_secret = ENV['GOOGLE_CLIENT_SECRET']

        # Set test values
        ENV['GOOGLE_CLIENT_ID'] = 'mock_client_id'
        ENV['GOOGLE_CLIENT_SECRET'] = 'mock_client_secret'

        # Mock HTTP response
        allow(HTTP).to receive(:post).and_return(
          double(
            status: double(success?: true),
            body: double(to_s: new_token_response.to_json)
          )
        )
      end

      after do
        # Restore original ENV values
        ENV['GOOGLE_CLIENT_ID'] = @original_client_id
        ENV['GOOGLE_CLIENT_SECRET'] = @original_client_secret
      end

      it 'makes a request to refresh the token' do
        # Use allow instead of expect to avoid timing issues
        expect(HTTP).to receive(:post).with(
          'https://oauth2.googleapis.com/token',
          hash_including(
            form: {
              client_id: 'mock_client_id',
              client_secret: 'mock_client_secret',
              refresh_token: credentials['refresh_token'],
              grant_type: 'refresh_token'
            }
          )
        )

        YoutubeTokenService.refresh_token(user, credentials)
      end

      it 'updates the user credentials with the new token' do
        YoutubeTokenService.refresh_token(user, credentials)

        updated_credentials = JSON.parse(user.reload.youtube_credentials)
        expect(updated_credentials['access_token']).to eq('new_access_token')
        expect(updated_credentials['refresh_token']).to eq(credentials['refresh_token'])

        # Check that expires_at is calculated correctly (within 5 seconds tolerance)
        expected_expiry = Time.now.to_i + 3600
        expect(updated_credentials['expires_at'].to_i).to be_within(5).of(expected_expiry)
      end

      context 'when the refresh fails' do
        before do
          allow(HTTP).to receive(:post).and_return(
            double(
              status: double(success?: false, client_error?: true),
              body: double(to_s: '{"error":"invalid_grant"}')
            )
          )
        end

        it 'logs the error and returns nil' do
          # The error message should match what's in the service
          expect(Rails.logger).to receive(:error).with(/Failed to refresh YouTube token for user: #{user.id}/)
          expect(YoutubeTokenService).to receive(:handle_token_refresh_failure)
          expect(YoutubeTokenService.refresh_token(user, credentials)).to be_nil
        end
      end
    end

    context 'when environment variables are missing' do
      before do
        # Store original ENV values
        @original_client_id = ENV['GOOGLE_CLIENT_ID']
        @original_client_secret = ENV['GOOGLE_CLIENT_SECRET']

        # Clear environment variables
        ENV['GOOGLE_CLIENT_ID'] = nil
        ENV['GOOGLE_CLIENT_SECRET'] = nil

        # Don't mock HTTP as we expect the method to return before making HTTP calls
        allow(HTTP).to receive(:post).and_raise('HTTP should not be called')
      end

      after do
        # Restore original ENV values
        ENV['GOOGLE_CLIENT_ID'] = @original_client_id
        ENV['GOOGLE_CLIENT_SECRET'] = @original_client_secret
      end

      it 'logs the error and returns nil' do
        expect(Rails.logger).to receive(:error).with('Missing required environment variables for token refresh: GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET')
        expect(YoutubeTokenService.refresh_token(user, credentials)).to be_nil
      end
    end
  end
end
