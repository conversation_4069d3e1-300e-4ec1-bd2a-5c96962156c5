<!-- spec/fixtures/ksi_wsdl.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/"
                  xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/"
                  xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/"
                  xmlns:tns="http://tempuri.org/"
                  xmlns:s="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
                  xmlns:http="http://schemas.xmlsoap.org/wsdl/http/"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  targetNamespace="http://tempuri.org/">
  <!-- Basic WSDL structure with mot_leikir operation -->
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="mot_leikir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="MotNumer" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="mot_leikir_response">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="mot_leikir_svar" type="tns:MotLeikir" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="MotLeikir">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="villa_numer" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="villa_texti" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="array_mot_leikir" type="tns:ArrayOfMotLeikur" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfMotLeikur">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="mot_leikur" type="tns:MotLeikur" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="MotLeikur">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="leikur_numer" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="leik_dagur" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="umferd_numer" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="skyrsla_stada" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="felag_heima_nafn" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="felag_heima_numer" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="felag_uti_nafn" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="felag_uti_numer" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="vollur_nafn" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="urslit_heima" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="urslit_uti" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="stada_fyrri_halfleik_heima" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="stada_fyrri_halfleik_uti" type="s:string" />
        </s:sequence>
      </s:complexType>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="mot_leikirSoapIn">
    <wsdl:part name="parameters" element="tns:mot_leikir" />
  </wsdl:message>
  <wsdl:message name="mot_leikirSoapOut">
    <wsdl:part name="parameters" element="tns:mot_leikir_response" />
  </wsdl:message>
  <wsdl:portType name="MotSoap">
    <wsdl:operation name="mot_leikir">
      <wsdl:input message="tns:mot_leikirSoapIn" />
      <wsdl:output message="tns:mot_leikirSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="MotSoap" type="tns:MotSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="mot_leikir">
      <soap12:operation soapAction="http://tempuri.org/mot_leikir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="Mot">
    <wsdl:port name="MotSoap" binding="tns:MotSoap">
      <soap12:address location="http://www2.ksi.is/vefthjonustur/mot.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>