require 'rails_helper'

RSpec.describe MailerSendDeliveryMethod do
  let(:settings) { { api_key: 'test_api_key' } }
  let(:delivery_method) { MailerSendDeliveryMethod.new(settings) }
  let(:mail) do
    Mail.new do
      from    '<EMAIL>'
      to      '<EMAIL>'
      subject 'Test email'
      body    'This is a test email body'
    end
  end

  describe '#deliver!' do
    let(:client_double) { instance_double(Mailersend::Client) }
    let(:email_double) { instance_double(Mailersend::Email) }
    let(:success_response) { { 'message_id' => 'test_message_id', 'success' => true } }

    before do
      allow(Mailersend::Client).to receive(:new).and_return(client_double)
      allow(Mailersend::Email).to receive(:new).with(client_double).and_return(email_double)
      
      # Setup default stubs for email methods
      allow(email_double).to receive(:add_from)
      allow(email_double).to receive(:add_recipients)
      allow(email_double).to receive(:add_subject)
      allow(email_double).to receive(:add_text)
      allow(email_double).to receive(:add_html)
      allow(email_double).to receive(:send).and_return(success_response)
    end

    it 'sends an email using the MailerSend API' do
      # Expectations for email setup
      expect(email_double).to receive(:add_from).with({"email" => "<EMAIL>", "name" => "Brag Rights"})
      expect(email_double).to receive(:add_recipients).with({"email" => "<EMAIL>", "name" => "recipient"})
      expect(email_double).to receive(:add_subject).with("Test email")
      expect(email_double).to receive(:add_text).with("This is a test email body")
      expect(email_double).to receive(:add_html).with("<div>This is a test email body</div>")
      expect(email_double).to receive(:send).and_return(success_response)
      
      response = delivery_method.deliver!(mail)
      
      expect(response).to eq(success_response)
    end

    it 'handles missing API key' do
      delivery_method.settings[:api_key] = nil
      
      expect { delivery_method.deliver!(mail) }.to raise_error(ArgumentError, 'API key is missing')
    end

    it 'handles API errors gracefully in test environment' do
      expect(email_double).to receive(:send).and_raise(StandardError.new('API error'))
      
      response = delivery_method.deliver!(mail)
      
      expect(response).to include('success' => false, 'error' => 'API error')
    end

    it 'properly extracts HTML content from multipart emails' do
      multipart_mail = Mail.new do
        from    '<EMAIL>'
        to      '<EMAIL>'
        subject 'Multipart test'
        
        text_part do
          body 'Plain text version'
        end
        
        html_part do
          content_type 'text/html; charset=UTF-8'
          body '<h1>HTML version</h1>'
        end
      end

      # Expectations for multipart email
      expect(email_double).to receive(:add_html).with('<h1>HTML version</h1>')
      expect(email_double).to receive(:add_text).with('Plain text version')
      
      delivery_method.deliver!(multipart_mail)
    end

    it 'properly handles cc, bcc and reply_to fields' do
      complex_mail = Mail.new do
        from     '<EMAIL>'
        to       '<EMAIL>'
        cc       '<EMAIL>'
        bcc      '<EMAIL>'
        reply_to '<EMAIL>'
        subject  'Complex email'
        body     'Test with all fields'
      end

      # Expectations for CC and BCC
      expect(email_double).to receive(:add_cc).with({"email" => "<EMAIL>", "name" => "copy"})
      expect(email_double).to receive(:add_bcc).with({"email" => "<EMAIL>", "name" => "hidden"})
      
      delivery_method.deliver!(complex_mail)
    end

    it 'handles attachments correctly' do
      mail_with_attachment = Mail.new do
        from     '<EMAIL>'
        to       '<EMAIL>'
        subject  'Email with attachment'
        body     'See attachment'
        
        add_file filename: 'test.txt', content: 'attachment content'
      end

      # Expectations for attachment handling
      expect(email_double).to receive(:add_attachment) do |args|
        expect(args[:filename]).to eq('test.txt')
        expect(args[:disposition]).to eq('attachment')
        expect(args[:content]).to be_a(String) # Base64 encoded content
      end
      
      delivery_method.deliver!(mail_with_attachment)
    end
    
    it 'handles template_id when specified' do
      template_mail = Mail.new do
        from          '<EMAIL>'
        to            '<EMAIL>'
        subject       'Template email'
        header['template_id'] = '12345'
        body          'This will be ignored when using a template'
      end
      
      expect(email_double).to receive(:add_template_id).with('12345')
      
      delivery_method.deliver!(template_mail)
    end
    
    it 'handles personalization variables when specified' do
      variables = [{ email: '<EMAIL>', data: { name: 'John' } }].to_json
      personalized_mail = Mail.new do
        from          '<EMAIL>'
        to            '<EMAIL>'
        subject       'Personalized email'
        header['variables'] = variables
        body          'Hello {{name}}'
      end
      
      parsed_variables = JSON.parse(variables)
      expect(email_double).to receive(:add_personalization).with(parsed_variables.first)
      
      delivery_method.deliver!(personalized_mail)
    end
  end
end