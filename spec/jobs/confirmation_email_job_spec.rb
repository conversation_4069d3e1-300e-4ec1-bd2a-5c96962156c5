require 'rails_helper'

RSpec.describe ConfirmationEmailJob, type: :job do
  describe '#perform' do
    context 'when user is unconfirmed' do
      let(:user) { create(:user, confirmed_at: nil) }
      
      before do
        # Prevent the job from actually sending emails
        mail_delivery = instance_double(ActionMailer::MessageDelivery)
        allow(mail_delivery).to receive(:deliver_now)
        allow(CustomDeviseMailer).to receive(:confirmation_instructions).and_return(mail_delivery)
      end
      
      xit 'sends confirmation instructions to the user' do
        # Ensure user has a confirmation token
        user.update_column(:confirmation_token, 'test-token')
        
        ConfirmationEmailJob.new.perform(user.id)
        
        # Verify mailer was called with correct user
        expect(CustomDeviseMailer).to have_received(:confirmation_instructions).with(user, 'test-token')
      end
      
      xit 'handles errors gracefully' do
        allow(CustomDeviseMailer).to receive(:confirmation_instructions)
          .and_raise(Net::OpenTimeout.new("execution expired"))
        
        # Should not raise the error in test environment
        expect { ConfirmationEmailJob.new.perform(user.id) }.not_to raise_error
      end
      
      xit 'generates a token if none exists' do
        # Ensure user has no confirmation token
        user.update_column(:confirmation_token, nil)
        
        ConfirmationEmailJob.new.perform(user.id)
        
        # Should have received confirmation instructions with a non-nil token
        expect(CustomDeviseMailer).to have_received(:confirmation_instructions)
          .with(user, kind_of(String))
      end
    end
    
    context 'when user is not found' do
      it 'does nothing' do
        allow(CustomDeviseMailer).to receive(:confirmation_instructions)
        
        ConfirmationEmailJob.new.perform(0)
        
        expect(CustomDeviseMailer).not_to have_received(:confirmation_instructions)
      end
    end
    
    context 'when user is already confirmed' do
      let(:confirmed_user) { create(:user, confirmed_at: Time.current) }
      
      it 'does nothing' do
        allow(CustomDeviseMailer).to receive(:confirmation_instructions)
        
        ConfirmationEmailJob.new.perform(confirmed_user.id)
        
        expect(CustomDeviseMailer).not_to have_received(:confirmation_instructions)
      end
    end
  end
end