require 'rails_helper'

RSpec.describe BatchRoundPredictionsJob, type: :job do
  let(:user) { create(:user) }
  let(:area) { create(:area) }
  let(:competition) { create(:competition, area:) }
  let(:season) { create(:season, competition:) }
  let(:home_team) { create(:team, area:) }
  let(:away_team) { create(:team, area:) }
  let(:match) { create(:match, home_team:, away_team:, season:) }
  let(:job_id) { SecureRandom.uuid }

  let(:prediction_data) do
    [
      {
        matchday: 1,
        season_id: season.id,
        competition_id: competition.id,
        stage: 'REGULAR_SEASON',
        match_predictions: [
          {
            match_id: match.id,
            home_score: 2,
            away_score: 1
          }
        ]
      }
    ]
  end

  describe '#perform' do
    before do
      # Clear any existing cache entries
      Rails.cache.clear

      # Mock Rails.cache.write to capture the data
      allow(Rails.cache).to receive(:write).and_call_original
    end

    it 'creates round predictions with match predictions' do
      expect do
        described_class.new.perform(user.id, prediction_data, job_id)
      end.to change(RoundPrediction, :count).by(1)
                                            .and change(MatchPrediction, :count).by(1)
    end

    it 'stores results in cache' do
      described_class.new.perform(user.id, prediction_data, job_id)

      # Verify that Rails.cache.write was called with the expected data
      expect(Rails.cache).to have_received(:write).with(
        "batch_predictions:#{job_id}",
        hash_including(
          status: 'completed',
          successful: kind_of(Array),
          failed: kind_of(Array)
        ),
        hash_including(expires_in: 1.hour)
      )
    end

    context 'with invalid prediction data' do
      let(:invalid_prediction_data) do
        [
          {
            # Missing required matchday
            season_id: season.id,
            competition_id: competition.id,
            stage: 'REGULAR_SEASON',
            match_predictions: [
              {
                match_id: match.id,
                home_score: 2,
                away_score: 1
              }
            ]
          }
        ]
      end

      it 'handles errors and records failed predictions' do
        described_class.new.perform(user.id, invalid_prediction_data, job_id)

        # Verify that Rails.cache.write was called with the expected data
        expect(Rails.cache).to have_received(:write).with(
          "batch_predictions:#{job_id}",
          hash_including(
            status: 'completed',
            successful: [],
            failed: array_including(hash_including(:errors))
          ),
          hash_including(expires_in: 1.hour)
        )
      end
    end

    context 'with mixed valid and invalid predictions' do
      let(:mixed_prediction_data) do
        [
          # Valid prediction
          {
            matchday: 1,
            season_id: season.id,
            competition_id: competition.id,
            stage: 'REGULAR_SEASON',
            match_predictions: [
              {
                match_id: match.id,
                home_score: 2,
                away_score: 1
              }
            ]
          },
          # Invalid prediction (missing matchday)
          {
            season_id: season.id,
            competition_id: competition.id,
            stage: 'REGULAR_SEASON',
            match_predictions: [
              {
                match_id: match.id,
                home_score: 3,
                away_score: 2
              }
            ]
          }
        ]
      end

      it 'processes valid predictions and records failures for invalid ones' do
        described_class.new.perform(user.id, mixed_prediction_data, job_id)

        # Verify that Rails.cache.write was called with the expected data
        expect(Rails.cache).to have_received(:write).with(
          "batch_predictions:#{job_id}",
          hash_including(
            status: 'completed',
            successful: array_including(kind_of(Integer)),
            failed: array_including(hash_including(:errors))
          ),
          hash_including(expires_in: 1.hour)
        )
      end
    end

    context 'when user does not exist' do
      it 'handles the error gracefully' do
        expect do
          described_class.new.perform(0, prediction_data, job_id)
        end.to raise_error(ActiveRecord::RecordNotFound)
      end
    end
  end
end
