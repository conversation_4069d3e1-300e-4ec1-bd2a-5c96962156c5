require 'rails_helper'

RSpec.describe TodaysMatchesUpdateJob, type: :job do
  describe '#perform' do
    let(:area) { create(:area) }
    let(:pl_competition) { create(:competition, code: 'PL', area:) }
    let(:sv_competition) { create(:competition, code: 'SV', area:) }
    let(:pl_season) { create(:season, competition: pl_competition) }
    let(:sv_season) { create(:season, competition: sv_competition) }

    let(:football_data_service) { instance_double(FootballDataService) }
    let(:api_football_service) { instance_double(ApiFootballService) }

    before do
      # Set current seasons
      pl_competition.update(current_season: pl_season)
      sv_competition.update(current_season: sv_season)

      # Mock the services to avoid actual API calls
      allow(FootballDataService).to receive(:new).and_return(football_data_service)
      allow(ApiFootballService).to receive(:new).and_return(api_football_service)
      allow(football_data_service).to receive(:update_matches_by_date_range).and_return(true)
      allow(api_football_service).to receive(:update_matches_by_date_range).and_return(true)
    end

    context 'with PL competition code' do
      it 'calls FootballDataService to update matches' do
        described_class.new.perform('PL')

        expect(football_data_service).to have_received(:update_matches_by_date_range)
          .with(pl_competition, Date.today, Date.today, pl_season)
      end
    end

    context 'with SV competition code' do
      it 'calls ApiFootballService to update matches' do
        described_class.new.perform('SV')

        expect(api_football_service).to have_received(:update_matches_by_date_range)
          .with(sv_competition.external_service_id, Date.today, Date.today, sv_season)
      end
    end

    context 'with invalid competition code' do
      it 'does nothing when competition is not found' do
        described_class.new.perform('INVALID')

        expect(football_data_service).not_to have_received(:update_matches_by_date_range)
        expect(api_football_service).not_to have_received(:update_matches_by_date_range)
      end

      it 'does nothing when competition has no current season' do
        # Remove current season
        pl_competition.update(current_season: nil)

        described_class.new.perform('PL')

        expect(football_data_service).not_to have_received(:update_matches_by_date_range)
      end
    end

    describe '.schedule_direct_test' do
      before do
        # Set up ActiveJob test adapter
        ActiveJob::Base.queue_adapter = :test
      end

      it 'schedules the job with the given competition code' do
        expect do
          TodaysMatchesUpdateJob.schedule_direct_test('PL', 1)
        end.to have_enqueued_job(TodaysMatchesUpdateJob).with('PL')
      end
    end
  end
end
