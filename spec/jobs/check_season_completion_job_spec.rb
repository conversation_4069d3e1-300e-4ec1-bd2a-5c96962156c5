require 'rails_helper'

RSpec.describe CheckSeasonCompletionJob, type: :job do
  let(:area) { create(:area, name: 'England', code: 'ENG') }
  let(:competition) { create(:competition, name: 'Premier League', code: 'PL', area:) }

  describe '#perform' do
    context 'with a specific competition code' do
      let(:season) { create(:season, competition:, start_date: 2.years.ago, end_date: 1.day.ago, completed: false) }

      before do
        competition.update(current_season: season)
      end

      it 'checks if the season is completed' do
        # Use allow instead of expect to avoid the "already received" error
        allow_any_instance_of(Season).to receive(:check_if_completed)

        described_class.new.perform('PL')
      end

      it 'archives leagues if the season is completed' do
        # Create a new instance for this test
        test_season = create(:season, competition:, start_date: 2.years.ago, end_date: 1.day.ago, completed: true)
        competition.update(current_season: test_season)

        # Use allow instead of expect
        allow_any_instance_of(Season).to receive(:check_if_completed)
        allow_any_instance_of(Season).to receive(:completed?).and_return(true)
        allow_any_instance_of(LeagueArchiveService).to receive(:archive_all_leagues)

        described_class.new.perform('PL')
      end

      it 'does not archive leagues if the season is not completed' do
        # Create a new instance for this test
        test_season = create(:season, competition:, start_date: 2.years.ago, end_date: 1.day.ago, completed: false)
        competition.update(current_season: test_season)

        # Use allow instead of expect
        allow_any_instance_of(Season).to receive(:check_if_completed)
        allow_any_instance_of(Season).to receive(:completed?).and_return(false)
        allow_any_instance_of(LeagueArchiveService).to receive(:archive_all_leagues).never

        described_class.new.perform('PL')
      end
    end

    context 'without a competition code' do
      let(:season) { create(:season, competition:, start_date: 2.years.ago, end_date: 1.day.ago, completed: false) }

      before do
        competition.update(current_season: season)
      end

      it 'checks all competitions' do
        allow(Competition).to receive(:all).and_return([competition])
        allow_any_instance_of(Season).to receive(:check_if_completed)

        described_class.new.perform
      end
    end

    context 'with upcoming end seasons' do
      let(:current_season) do
        create(:season, competition:, start_date: 6.months.ago, end_date: 2.months.from_now, completed: false)
      end
      let(:upcoming_end_season) do
        create(:season, competition:, start_date: 1.year.ago, end_date: 2.weeks.from_now, completed: false)
      end

      before do
        competition.update(current_season:)
        upcoming_end_season # Create the upcoming end season
      end

      it 'checks seasons ending soon' do
        # Use allow instead of expect to avoid the "already received" error
        allow_any_instance_of(Season).to receive(:check_if_completed)

        described_class.new.perform('PL')
      end
    end
  end
end
