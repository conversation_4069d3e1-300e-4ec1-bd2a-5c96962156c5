require 'rails_helper'

RSpec.describe SubscriptionVerificationJob, type: :job do
  describe '#perform' do
    let(:user) { create(:user) }
    let(:owner) { create(:user) }
    let(:youtube_channel_id) { 'UC1234567890' }
    let(:subscriber_league) do
      create(:league, owner:, subscriber_only: true, youtube_channel_id:)
    end
    let(:regular_league) { create(:league, owner:, subscriber_only: false) }

    before do
      # Mock the YouTube service to avoid actual API calls
      allow_any_instance_of(BragRightsYouTubeService).to receive(:verify_subscription?).and_return(true)
    end

    context 'when processing expired grace periods' do
      let!(:expired_membership) do
        create(:membership, :with_grace_period,
               user:,
               league: subscriber_league,
               grace_period_ends_at: 1.day.ago)
      end

      let(:another_user) { create(:user) }
      let(:another_league) { create(:league, owner:, subscriber_only: true, youtube_channel_id:) }

      let!(:active_grace_membership) do
        create(:membership, :with_grace_period,
               user: another_user,
               league: another_league,
               grace_period_ends_at: 1.day.from_now)
      end

      it 'removes users from leagues when grace period has expired' do
        expect do
          described_class.new.perform
        end.to change { Membership.count }.by(-1)

        # Verify the expired membership was removed
        expect(Membership.exists?(expired_membership.id)).to be_falsey

        # Verify the active grace membership was not removed
        expect(Membership.exists?(active_grace_membership.id)).to be_truthy
      end
    end

    context 'when verifying active users' do
      let!(:active_membership) do
        create(:membership, :with_subscription,
               user:,
               league: subscriber_league,
               subscription_verified_at: 2.weeks.ago)
      end

      # Mock the League#user_meets_subscription_requirements? method
      before do
        allow_any_instance_of(League).to receive(:user_meets_subscription_requirements?).and_return(true)

        # Mock the verify_tier method directly to avoid complex User class mocking
        allow_any_instance_of(SubscriptionVerificationJob).to receive(:verify_tier).and_return(nil)

        # Create a mock for the membership collection
        membership = active_membership
        memberships = [membership]

        # Mock the Membership.joins(...).where(...) chain
        membership_relation = double('ActiveRecord::Relation')
        allow(Membership).to receive(:joins).with(:league).and_return(membership_relation)
        allow(membership_relation).to receive(:where).with(user:).and_return(membership_relation)
        allow(membership_relation).to receive(:where).with(leagues: { subscriber_only: true }).and_return(memberships)
      end

      it 'verifies subscriptions for active users' do
        # Call the method directly to test the membership update logic
        job = described_class.new
        job.send(:verify_user_subscriptions, user)
      end

      context 'when user is no longer subscribed' do
        before do
          allow_any_instance_of(BragRightsYouTubeService).to receive(:verify_subscription?).and_return(false)
          allow_any_instance_of(League).to receive(:user_meets_subscription_requirements?).and_return(false)
        end

        it 'puts the user in grace period' do
          # Call the method directly to test the membership update logic
          job = described_class.new
          job.send(:verify_user_subscriptions, user)
        end
      end
    end

    context 'with regular leagues' do
      let!(:regular_membership) do
        create(:membership, user:, league: regular_league)
      end

      it 'does not verify subscriptions for regular leagues' do
        # Ensure the YouTube service is never called for regular leagues
        expect_any_instance_of(BragRightsYouTubeService).not_to receive(:verify_subscription?)

        described_class.new.perform('active')
      end
    end

    context 'with missing YouTube channel ID' do
      let(:incomplete_league) { create(:league, subscriber_only: true, youtube_channel_id: nil) }
      let!(:incomplete_membership) do
        create(:membership, :with_subscription, user:, league: incomplete_league)
      end

      it 'skips verification for leagues without YouTube channel ID' do
        # Ensure the YouTube service is never called for incomplete leagues
        expect_any_instance_of(BragRightsYouTubeService).not_to receive(:verify_subscription?)
          .with(user, nil)

        described_class.new.perform('active')
      end
    end
  end
end
