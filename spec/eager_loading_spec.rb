require 'rails_helper'

RSpec.describe 'Eager Loading' do
  it 'can load all controllers without errors' do
    # This will attempt to load all controllers and their dependencies
    Rails.application.eager_load!

    # Get all controller classes
    controllers = ApplicationController.descendants

    # Log the number of controllers loaded for debugging
    puts "Successfully loaded #{controllers.count} controllers"

    # If we get here without errors, the test passes
    expect(true).to be true
  end

  it 'can initialize all controller classes' do
    Rails.application.eager_load!

    # Get all controller classes
    controllers = ApplicationController.descendants

    # Try to initialize each controller class
    # This will catch issues with constructor arguments or initialization logic
    controllers.each do |controller_class|
      # Skip abstract controllers that aren't meant to be instantiated directly
      next if controller_class.abstract?

      # Try to initialize the controller
      controller_instance = controller_class.new
      expect(controller_instance).to be_a(controller_class)
    rescue StandardError => e
      raise "Failed to initialize #{controller_class.name}: #{e.message}"
    end
  end

  it 'verifies all controller inheritance chains' do
    Rails.application.eager_load!

    # Get all controller classes
    controllers = ApplicationController.descendants

    # Check each controller's inheritance chain
    controllers.each do |controller_class|
      # Skip abstract controllers
      next if controller_class.abstract?

      # Walk up the inheritance chain to make sure all parent classes exist
      current_class = controller_class
      while current_class != ApplicationController
        parent_class = current_class.superclass
        expect(parent_class).not_to be_nil, "#{current_class.name} has a nil superclass"
        current_class = parent_class
      end
    end
  end
end
