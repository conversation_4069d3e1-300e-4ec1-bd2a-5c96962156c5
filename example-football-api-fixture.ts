export interface RootObject {
  competition: Competition;
  filters:     Filters;
  matches:     Match[];
  resultSet:   ResultSet;
 }
 
 export interface Competition {
  code:   CompetitionCode;
  emblem: string;
  id:     number;
  name:   CompetitionName;
  type:   Type;
 }
 
 export enum CompetitionCode {
  Pl = "PL",
 }
 
 export enum CompetitionName {
  PremierLeague = "Premier League",
 }
 
 export enum Type {
  League = "LEAGUE",
 }
 
 export interface Filters {
  season: string;
  status: Status[];
 }
 
 export enum Status {
  Scheduled = "SCHEDULED",
  Timed = "TIMED",
 }
 
 export interface Match {
  area:        Area;
  awayTeam:    Team;
  competition: Competition;
  group:       null;
  homeTeam:    Team;
  id:          number;
  lastUpdated: Date;
  matchday:    number;
  odds:        Odds;
  referees:    any[];
  score:       Score;
  season:      Season;
  stage:       Stage;
  status:      Status;
  utcDate:     Date;
 }
 
 export interface Area {
  code: AreaCode;
  flag: string;
  id:   number;
  name: AreaName;
 }
 
 export enum AreaCode {
  Eng = "ENG",
 }
 
 export enum AreaName {
  England = "England",
 }
 
 export interface Team {
  crest:     string;
  id:        number;
  name:      string;
  shortName: string;
  tla:       string;
 }
 
 export interface Odds {
  msg: Msg;
 }
 
 export enum Msg {
  ActivateOddsPackageInUserPanelToRetrieveOdds = "Activate Odds-Package in User-Panel to retrieve odds.",
 }
 
 export interface Score {
  duration: Duration;
  fullTime: Time;
  halfTime: Time;
  winner:   null;
 }
 
 export enum Duration {
  Regular = "REGULAR",
 }
 
 export interface Time {
  away: null;
  home: null;
 }
 
 export interface Season {
  currentMatchday: number;
  endDate:         Date;
  id:              number;
  startDate:       Date;
  winner:          null;
 }
 
 export enum Stage {
  RegularSeason = "REGULAR_SEASON",
 }
 
 export interface ResultSet {
  count:  number;
  first:  Date;
  last:   Date;
  played: number;
 }
 