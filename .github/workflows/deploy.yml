name: Deploy to Digital Ocean

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: brag_rights_test
        ports: ["5432:5432"]
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis
        ports: ["6379:6379"]
        options: --health-cmd "redis-cli ping" --health-interval 10s --health-timeout 5s --health-retries 5

    steps:
      - uses: actions/checkout@v3
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.3.0
          bundler-cache: true
      - name: Set up database
        env:
          RAILS_ENV: test
          DATABASE_URL: postgres://postgres:postgres@localhost:5432/brag_rights_test
        run: |
          bundle exec rails db:create db:schema:load
      - name: Run tests
        env:
          RAILS_ENV: test
          DATABASE_URL: postgres://postgres:postgres@localhost:5432/brag_rights_test
          REDIS_URL: redis://localhost:6379/0
          FOOTBALL_API_KEY: test_api_key
          API_FOOTBALL_KEY: test_api_key
        run: bundle exec rspec

      - name: Test eager loading
        env:
          RAILS_ENV: test
          DATABASE_URL: postgres://postgres:postgres@localhost:5432/brag_rights_test
          REDIS_URL: redis://localhost:6379/0
          FOOTBALL_API_KEY: test_api_key
          API_FOOTBALL_KEY: test_api_key
        run: bundle exec rake test:eager_loading

      - name: Test production boot
        env:
          RAILS_ENV: production
          DATABASE_URL: postgres://postgres:postgres@localhost:5432/brag_rights_test
          REDIS_URL: redis://localhost:6379/0
          FOOTBALL_API_KEY: test_api_key
          API_FOOTBALL_KEY: test_api_key
          SECRET_KEY_BASE: test_secret_key_base
        run: bundle exec rails runner "puts 'App booted successfully in production mode'"

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Digital Ocean
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.DO_HOST }}
          username: ${{ secrets.DO_USERNAME }}
          key: ${{ secrets.DO_SSH_KEY }}
          script: |
            cd /var/www/bragrights-be
            git pull origin main
            source ~/.bashrc
            # Load Ruby environment - try both RVM and rbenv
            if [ -f ~/.rvm/scripts/rvm ]; then
              source ~/.rvm/scripts/rvm
              rvm use
            elif [ -d ~/.rbenv ]; then
              export PATH="$HOME/.rbenv/bin:$PATH"
              eval "$(rbenv init -)"
            fi

            # Verify Ruby is available
            ruby -v

            export RAILS_ENV=production
            bundle config set --local deployment 'true'
            bundle config set --local without 'development test'
            bundle install
            bundle exec rails db:migrate
            sudo systemctl restart puma
            sudo systemctl restart sidekiq
            echo "Deployment completed successfully at $(date)"
