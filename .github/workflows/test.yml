name: Run Tests

on:
  pull_request:
    branches: [main]
  push:
    branches-ignore: [main]
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: brag_rights_test
        ports: ["5432:5432"]
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis
        ports: ["6379:6379"]
        options: --health-cmd "redis-cli ping" --health-interval 10s --health-timeout 5s --health-retries 5

    steps:
      - uses: actions/checkout@v3
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.3.0
          bundler-cache: true
      - name: Set up database
        env:
          RAILS_ENV: test
          DATABASE_URL: postgres://postgres:postgres@localhost:5432/brag_rights_test
        run: |
          bundle exec rails db:create db:schema:load
      - name: Run tests
        env:
          RAILS_ENV: test
          DATABASE_URL: postgres://postgres:postgres@localhost:5432/brag_rights_test
          REDIS_URL: redis://localhost:6379/0
          # Add mock API keys for services to prevent API key errors in tests
          FOOTBALL_API_KEY: test_api_key
          API_FOOTBALL_KEY: test_api_key
        run: bundle exec rspec

      - name: Test eager loading
        env:
          RAILS_ENV: test
          DATABASE_URL: postgres://postgres:postgres@localhost:5432/brag_rights_test
          REDIS_URL: redis://localhost:6379/0
          FOOTBALL_API_KEY: test_api_key
          API_FOOTBALL_KEY: test_api_key
        run: bundle exec rake test:eager_loading
