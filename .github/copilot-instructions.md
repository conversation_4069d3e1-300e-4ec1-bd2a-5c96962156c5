# AI Agent Instructions: Rails 7 API Football Prediction App

## General Development Approach

As a senior Rails developer working on a Rails 7 API-only application for football match predictions and leagues, you should:

1. Follow Test-Driven Development (TDD) principles
2. Write clean, maintainable code adhering to Ruby and Rails best practices
3. Respect the established Rubocop setup for consistent code style
4. Focus on API endpoint design that follows RESTful principles
5. Implement proper error handling and status codes
6. Consider performance implications, especially for database queries

## Testing Requirements

### Controller Tests (Primary Focus)

- Write request specs for all API endpoints before implementation
- Test both happy paths and edge cases
- Verify proper status codes (200, 201, 401, 403, 404, 422, etc.)
- Test JSON response structure and content
- Ensure authentication and authorization are properly tested
- Use factories to create test data

### Model Tests

- Test validations, scopes, and custom methods
- Ensure proper relationships between models
- Test business logic thoroughly
- Focus on edge cases and potential failures

### Other Tests

- Test service objects, if used
- Test background jobs, if implemented
- Consider integration tests for critical user journeys

## Code Structure and Organization

### Models

- Define clear relationships (belongs_to, has_many, etc.)
- Implement appropriate validations
- Use concerns for shared functionality
- Leverage scopes for common queries
- Follow single responsibility principle

### Controllers

- Keep controllers skinny
- Handle authentication and authorization
- Use strong parameters for data filtering
- Return appropriate status codes and responses
- Implement proper error handling

### Service Objects

- Move complex business logic to service objects
- Follow single responsibility principle
- Make service objects testable and reusable

## Feature-Specific Guidance

### User Authentication

- Use a proven solution like Devise or implement JWT authentication
- Test token generation, validation, and expiration
- Consider refresh token strategies

### Match Predictions

- Design a flexible prediction model that can accommodate different match outcomes
- Consider performance for retrieving and calculating user predictions
- Implement scoring mechanism that can be easily adjusted

### Leagues

- Implement proper access controls for private vs. public leagues
- Design efficient queries for leaderboards
- Consider background jobs for score calculations

## API Design Principles

- Use versioning (e.g., `/api/v1/`)
- Follow consistent naming conventions
- Use appropriate HTTP verbs (GET, POST, PUT/PATCH, DELETE)
- Return meaningful error messages
- Use proper status codes
- Consider pagination for large resources
- Implement sorting and filtering where appropriate

## Example Test-First Approach

When implementing a new feature:

1. Write request specs first describing the expected behavior
2. Run the tests and watch them fail
3. Implement the minimal code needed to make tests pass
4. Refactor while keeping tests passing
5. Document any API changes

## Sample Test Structure

```ruby
# spec/requests/api/v1/predictions_controller_spec.rb
require 'rails_helper'

RSpec.describe Api::V1::PredictionsController, type: :request do
  let(:user) { create(:user) }
  let(:match) { create(:match, :upcoming) }
  let(:headers) { valid_headers_for(user) }

  describe 'POST /api/v1/matches/:match_id/predictions' do
    context 'with valid parameters' do
      let(:valid_params) { { prediction: { home_score: 2, away_score: 1 } } }

      it 'creates a new prediction' do
        expect {
          post "/api/v1/matches/#{match.id}/predictions",
               params: valid_params.to_json,
               headers: headers
        }.to change(Prediction, :count).by(1)

        expect(response).to have_http_status(:created)
        expect(json_response['prediction']).to include(
          'home_score' => 2,
          'away_score' => 1
        )
      end
    end

    context 'with invalid parameters' do
      let(:invalid_params) { { prediction: { home_score: -1, away_score: 1 } } }

      it 'returns an error' do
        post "/api/v1/matches/#{match.id}/predictions",
             params: invalid_params.to_json,
             headers: headers

        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['errors']).to include('home_score')
      end
    end
  end
end
```

## Rubocop Compliance

- Run Rubocop before finalizing code
- Address all offenses or add justified exceptions
- Follow naming conventions consistently
- Format code according to style guide
- Keep methods small and focused
- Use proper indentation and spacing

## Documentation

- Add meaningful comments for complex logic
- Document public API endpoints
- Use proper YARD/RDoc syntax for method documentation
- Include examples for non-obvious usage
